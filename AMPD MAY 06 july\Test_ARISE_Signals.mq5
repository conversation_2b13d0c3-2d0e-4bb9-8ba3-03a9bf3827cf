//+------------------------------------------------------------------+
//| Test_ARISE_Signals.mq5                                          |
//| Test script for ARISE-MAY-DYNASTY Signal Engine                 |
//+------------------------------------------------------------------+
#property copyright "ARISE-MAY-DYNASTY"
#property version   "1.00"
#property script_show_inputs

//--- Input parameters
input int TestBars = 100; // Number of bars to test

//--- Global variables
int indicatorHandle;
double buySignals[], sellSignals[], signalStrength[];

//+------------------------------------------------------------------+
//| Script program start function                                   |
//+------------------------------------------------------------------+
void OnStart()
{
   Print("ARISE-MAY-DYNASTY: Starting signal test...");
   
   // Create indicator handle
   indicatorHandle = iCustom(_Symbol, _Period, "ARISE_MAY_DYNASTY_Signal_Engine");
   
   if(indicatorHandle == INVALID_HANDLE)
   {
      Print("ARISE-MAY-DYNASTY: ERROR - Failed to create indicator handle");
      return;
   }
   
   Print("ARISE-MAY-DYNASTY: Indicator handle created successfully: ", indicatorHandle);
   
   // Wait for indicator to calculate
   Sleep(2000);
   
   // Test signal detection
   TestSignalDetection();
   
   // Clean up
   IndicatorRelease(indicatorHandle);
   Print("ARISE-MAY-DYNASTY: Signal test completed");
}

//+------------------------------------------------------------------+
//| Test signal detection functionality                             |
//+------------------------------------------------------------------+
void TestSignalDetection()
{
   ArraySetAsSeries(buySignals, true);
   ArraySetAsSeries(sellSignals, true);
   ArraySetAsSeries(signalStrength, true);
   
   // Copy indicator buffers
   if(CopyBuffer(indicatorHandle, 0, 0, TestBars, buySignals) <= 0)
   {
      Print("ARISE-MAY-DYNASTY: ERROR - Failed to copy BUY signals buffer");
      return;
   }
   
   if(CopyBuffer(indicatorHandle, 1, 0, TestBars, sellSignals) <= 0)
   {
      Print("ARISE-MAY-DYNASTY: ERROR - Failed to copy SELL signals buffer");
      return;
   }
   
   if(CopyBuffer(indicatorHandle, 3, 0, TestBars, signalStrength) <= 0)
   {
      Print("ARISE-MAY-DYNASTY: WARNING - Failed to copy signal strength buffer");
   }
   
   // Analyze signals
   int buyCount = 0, sellCount = 0;
   
   for(int i = 0; i < TestBars; i++)
   {
      if(buySignals[i] != EMPTY_VALUE && buySignals[i] != 0.0)
      {
         buyCount++;
         Print("ARISE-MAY-DYNASTY: BUY signal found at bar ", i, " - Value: ", DoubleToString(buySignals[i], _Digits));
      }
      
      if(sellSignals[i] != EMPTY_VALUE && sellSignals[i] != 0.0)
      {
         sellCount++;
         Print("ARISE-MAY-DYNASTY: SELL signal found at bar ", i, " - Value: ", DoubleToString(sellSignals[i], _Digits));
      }
   }
   
   Print("ARISE-MAY-DYNASTY: Signal Analysis Results:");
   Print("ARISE-MAY-DYNASTY: Total BUY signals: ", buyCount);
   Print("ARISE-MAY-DYNASTY: Total SELL signals: ", sellCount);
   Print("ARISE-MAY-DYNASTY: Total signals: ", buyCount + sellCount);
   
   if(buyCount == 0 && sellCount == 0)
   {
      Print("ARISE-MAY-DYNASTY: WARNING - No signals detected! Check indicator parameters.");
      
      // Debug buffer values
      Print("ARISE-MAY-DYNASTY: Debug - First 10 BUY buffer values:");
      for(int i = 0; i < 10 && i < ArraySize(buySignals); i++)
      {
         Print("ARISE-MAY-DYNASTY: BUY[", i, "] = ", DoubleToString(buySignals[i], _Digits));
      }
      
      Print("ARISE-MAY-DYNASTY: Debug - First 10 SELL buffer values:");
      for(int i = 0; i < 10 && i < ArraySize(sellSignals); i++)
      {
         Print("ARISE-MAY-DYNASTY: SELL[", i, "] = ", DoubleToString(sellSignals[i], _Digits));
      }
   }
}
