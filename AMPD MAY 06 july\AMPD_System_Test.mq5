//+------------------------------------------------------------------+
//|                                        AMPD System Test.mq5     |
//|                        Copyright 2024, <PERSON><PERSON> |
//|                                             https://www.ampd.com |
//+------------------------------------------------------------------+
#property copyright "Arise Moroka Prince Dynasty"
#property link      "https://ampd.com"
#property description "Test Script for AMPD Jump Crash System"
#property version   "1.0"
#property script_show_inputs

//--- Input parameters
input string InpTestSymbol = "Jump 75 Index";      // Symbol to Test
input int    InpTestBars = 1000;                   // Number of Bars to Test
input bool   InpShowDetails = true;                // Show Detailed Results
input bool   InpTestIndicatorOnly = false;         // Test Indicator Loading Only

//--- Global variables
int IndicatorHandle;
double JumpSignals[];
double CrashSignals[];
double ExitSignals[];

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
   Print("=== AMPD JUMP CRASH SYSTEM TEST ===");
   Print("Testing Symbol: ", InpTestSymbol);
   Print("Test Period: ", InpTestBars, " bars");
   Print("========================================");
   
   // Test 1: Indicator Loading
   if(!TestIndicatorLoading())
   {
      Print("ERROR: Indicator loading test failed!");
      return;
   }
   
   // Test 2: Data Retrieval
   if(!TestDataRetrieval())
   {
      Print("ERROR: Data retrieval test failed!");
      return;
   }
   
   // Test 3: Signal Analysis
   TestSignalAnalysis();
   
   // Test 4: Performance Metrics
   TestPerformanceMetrics();
   
   // Cleanup
   if(IndicatorHandle != INVALID_HANDLE)
      IndicatorRelease(IndicatorHandle);
   
   Print("========================================");
   Print("=== SYSTEM TEST COMPLETED ===");
}

//+------------------------------------------------------------------+
//| Test indicator loading                                           |
//+------------------------------------------------------------------+
bool TestIndicatorLoading()
{
   Print("Testing indicator loading...");
   
   // Try to load the custom indicator
   IndicatorHandle = iCustom(InpTestSymbol, PERIOD_M1, "AMPD_Jump_Crash_Indicator");
   
   if(IndicatorHandle == INVALID_HANDLE)
   {
      Print("FAILED: Cannot load AMPD_Jump_Crash_Indicator");
      Print("Please ensure the indicator is compiled and available");
      return false;
   }
   
   Print("SUCCESS: Indicator loaded successfully");
   return true;
}

//+------------------------------------------------------------------+
//| Test data retrieval                                             |
//+------------------------------------------------------------------+
bool TestDataRetrieval()
{
   Print("Testing data retrieval...");
   
   // Initialize arrays
   ArraySetAsSeries(JumpSignals, true);
   ArraySetAsSeries(CrashSignals, true);
   ArraySetAsSeries(ExitSignals, true);
   
   // Try to copy indicator buffers
   int jumpCopied = CopyBuffer(IndicatorHandle, 0, 0, InpTestBars, JumpSignals);
   int crashCopied = CopyBuffer(IndicatorHandle, 1, 0, InpTestBars, CrashSignals);
   int exitCopied = CopyBuffer(IndicatorHandle, 2, 0, InpTestBars, ExitSignals);
   
   if(jumpCopied <= 0 || crashCopied <= 0 || exitCopied <= 0)
   {
      Print("FAILED: Cannot retrieve indicator data");
      Print("Jump signals copied: ", jumpCopied);
      Print("Crash signals copied: ", crashCopied);
      Print("Exit signals copied: ", exitCopied);
      return false;
   }
   
   Print("SUCCESS: Data retrieved successfully");
   Print("Jump signals: ", jumpCopied, " values");
   Print("Crash signals: ", crashCopied, " values");
   Print("Exit signals: ", exitCopied, " values");
   
   return true;
}

//+------------------------------------------------------------------+
//| Test signal analysis                                            |
//+------------------------------------------------------------------+
void TestSignalAnalysis()
{
   Print("Analyzing signals...");
   
   int jumpCount = 0;
   int crashCount = 0;
   int exitCount = 0;
   
   // Count signals
   for(int i = 0; i < ArraySize(JumpSignals); i++)
   {
      if(JumpSignals[i] != 0.0 && JumpSignals[i] != EMPTY_VALUE)
         jumpCount++;
      
      if(CrashSignals[i] != 0.0 && CrashSignals[i] != EMPTY_VALUE)
         crashCount++;
      
      if(ExitSignals[i] != 0.0 && ExitSignals[i] != EMPTY_VALUE)
         exitCount++;
   }
   
   Print("Signal Analysis Results:");
   Print("- Jump signals found: ", jumpCount);
   Print("- Crash signals found: ", crashCount);
   Print("- Exit signals found: ", exitCount);
   
   // Calculate signal frequency
   double barsPerHour = 60; // 1-minute bars
   double testHours = InpTestBars / barsPerHour;
   
   if(testHours > 0)
   {
      Print("Signal Frequency (per hour):");
      Print("- Jump signals: ", DoubleToString(jumpCount / testHours, 2));
      Print("- Crash signals: ", DoubleToString(crashCount / testHours, 2));
      Print("- Exit signals: ", DoubleToString(exitCount / testHours, 2));
   }
   
   // Show recent signals if requested
   if(InpShowDetails)
   {
      Print("Recent signals (last 50 bars):");
      for(int i = 0; i < 50 && i < ArraySize(JumpSignals); i++)
      {
         string signalInfo = "Bar " + IntegerToString(i) + ": ";
         
         if(JumpSignals[i] != 0.0 && JumpSignals[i] != EMPTY_VALUE)
            signalInfo += "JUMP ";
         
         if(CrashSignals[i] != 0.0 && CrashSignals[i] != EMPTY_VALUE)
            signalInfo += "CRASH ";
         
         if(ExitSignals[i] != 0.0 && ExitSignals[i] != EMPTY_VALUE)
            signalInfo += "EXIT ";
         
         if(StringFind(signalInfo, "JUMP") >= 0 || StringFind(signalInfo, "CRASH") >= 0 || StringFind(signalInfo, "EXIT") >= 0)
            Print(signalInfo);
      }
   }
}

//+------------------------------------------------------------------+
//| Test performance metrics                                        |
//+------------------------------------------------------------------+
void TestPerformanceMetrics()
{
   Print("Calculating performance metrics...");
   
   // Get price data for analysis
   double close[];
   double open[];
   double high[];
   double low[];
   
   ArraySetAsSeries(close, true);
   ArraySetAsSeries(open, true);
   ArraySetAsSeries(high, true);
   ArraySetAsSeries(low, true);
   
   int pricesCopied = CopyClose(InpTestSymbol, PERIOD_M1, 0, InpTestBars, close);
   CopyOpen(InpTestSymbol, PERIOD_M1, 0, InpTestBars, open);
   CopyHigh(InpTestSymbol, PERIOD_M1, 0, InpTestBars, high);
   CopyLow(InpTestSymbol, PERIOD_M1, 0, InpTestBars, low);
   
   if(pricesCopied <= 0)
   {
      Print("WARNING: Cannot retrieve price data for performance analysis");
      return;
   }
   
   // Simulate trading performance
   double totalProfit = 0;
   int totalTrades = 0;
   int winningTrades = 0;
   double maxDrawdown = 0;
   double currentDrawdown = 0;
   double peakBalance = 1000; // Starting balance
   double currentBalance = 1000;
   
   // Simple simulation based on signals
   for(int i = InpTestBars - 1; i >= 50; i--) // Leave some bars for exit
   {
      double tradeProfit = 0;
      bool tradeExecuted = false;
      
      // Check for Jump signal
      if(JumpSignals[i] != 0.0 && JumpSignals[i] != EMPTY_VALUE)
      {
         // Simulate Jump trade (BUY)
         double entryPrice = close[i];
         double exitPrice = 0;
         
         // Look for exit within next 20 bars or use fixed TP/SL
         for(int j = i - 1; j >= MathMax(0, i - 20); j--)
         {
            if(ExitSignals[j] != 0.0 && ExitSignals[j] != EMPTY_VALUE)
            {
               exitPrice = close[j];
               break;
            }
         }
         
         if(exitPrice == 0)
         {
            // Use fixed TP/SL (50 points TP, 25 points SL)
            double tp = entryPrice + 50 * SymbolInfoDouble(InpTestSymbol, SYMBOL_POINT);
            double sl = entryPrice - 25 * SymbolInfoDouble(InpTestSymbol, SYMBOL_POINT);
            
            // Check if TP or SL was hit in next bars
            for(int j = i - 1; j >= MathMax(0, i - 20); j--)
            {
               if(high[j] >= tp)
               {
                  exitPrice = tp;
                  break;
               }
               if(low[j] <= sl)
               {
                  exitPrice = sl;
                  break;
               }
            }
            
            if(exitPrice == 0)
               exitPrice = close[MathMax(0, i - 20)]; // Exit at end of period
         }
         
         tradeProfit = exitPrice - entryPrice;
         tradeExecuted = true;
      }
      
      // Check for Crash signal
      else if(CrashSignals[i] != 0.0 && CrashSignals[i] != EMPTY_VALUE)
      {
         // Simulate Crash trade (SELL)
         double entryPrice = close[i];
         double exitPrice = 0;
         
         // Look for exit within next 20 bars or use fixed TP/SL
         for(int j = i - 1; j >= MathMax(0, i - 20); j--)
         {
            if(ExitSignals[j] != 0.0 && ExitSignals[j] != EMPTY_VALUE)
            {
               exitPrice = close[j];
               break;
            }
         }
         
         if(exitPrice == 0)
         {
            // Use fixed TP/SL (75 points TP, 35 points SL)
            double tp = entryPrice - 75 * SymbolInfoDouble(InpTestSymbol, SYMBOL_POINT);
            double sl = entryPrice + 35 * SymbolInfoDouble(InpTestSymbol, SYMBOL_POINT);
            
            // Check if TP or SL was hit in next bars
            for(int j = i - 1; j >= MathMax(0, i - 20); j--)
            {
               if(low[j] <= tp)
               {
                  exitPrice = tp;
                  break;
               }
               if(high[j] >= sl)
               {
                  exitPrice = sl;
                  break;
               }
            }
            
            if(exitPrice == 0)
               exitPrice = close[MathMax(0, i - 20)]; // Exit at end of period
         }
         
         tradeProfit = entryPrice - exitPrice; // Profit for SELL trade
         tradeExecuted = true;
      }
      
      // Update statistics
      if(tradeExecuted)
      {
         totalTrades++;
         totalProfit += tradeProfit;
         currentBalance += tradeProfit;
         
         if(tradeProfit > 0)
            winningTrades++;
         
         // Update drawdown
         if(currentBalance > peakBalance)
            peakBalance = currentBalance;
         
         currentDrawdown = peakBalance - currentBalance;
         if(currentDrawdown > maxDrawdown)
            maxDrawdown = currentDrawdown;
      }
   }
   
   // Display results
   Print("Performance Simulation Results:");
   Print("- Total trades: ", totalTrades);
   Print("- Winning trades: ", winningTrades);
   Print("- Win rate: ", totalTrades > 0 ? DoubleToString(100.0 * winningTrades / totalTrades, 1) + "%" : "N/A");
   Print("- Total profit: ", DoubleToString(totalProfit, 2));
   Print("- Average profit per trade: ", totalTrades > 0 ? DoubleToString(totalProfit / totalTrades, 2) : "N/A");
   Print("- Maximum drawdown: ", DoubleToString(maxDrawdown, 2));
   Print("- Final balance: ", DoubleToString(currentBalance, 2));
   
   // Recommendations
   Print("Recommendations:");
   if(totalTrades == 0)
      Print("- No trades generated. Check indicator settings and symbol data.");
   else if(totalTrades < 10)
      Print("- Few trades generated. Consider adjusting sensitivity settings.");
   
   if(totalTrades > 0)
   {
      double winRate = 100.0 * winningTrades / totalTrades;
      if(winRate < 50)
         Print("- Low win rate. Consider tightening entry conditions.");
      else if(winRate > 80)
         Print("- Very high win rate. May indicate over-optimization.");
      
      if(maxDrawdown > currentBalance * 0.2)
         Print("- High drawdown detected. Consider reducing risk or improving stops.");
   }
}
