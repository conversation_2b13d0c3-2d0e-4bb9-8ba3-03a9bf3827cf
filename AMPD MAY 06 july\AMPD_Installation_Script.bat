@echo off
echo ================================================================
echo AMPD HIGH-FREQUENCY TRADING SYSTEM - INSTALLATION SCRIPT
echo ================================================================
echo.
echo This script will help you install the AMPD High-Frequency Trading System
echo.
echo PACKAGE CONTENTS:
echo [1] AMPD_RealTime_Precision_Indicator.mq5 (Enhanced Indicator)
echo [2] AMPD_HighFrequency_AutoTrader_EA.mq5 (Companion EA)
echo [3] AMPD_HighFrequency_Backtester.mq5 (Backtesting Script)
echo [4] AMPD_Performance_Analyzer.mq5 (Performance Analysis)
echo [5] AMPD_System_Validator.mq5 (System Validation)
echo [6] AMPD_Master_Dashboard.mq5 (Master Dashboard)
echo.
echo ================================================================
echo INSTALLATION INSTRUCTIONS:
echo ================================================================
echo.
echo STEP 1: LOCATE YOUR MT5 DATA FOLDER
echo ----------------------------------------
echo 1. Open MetaTrader 5
echo 2. Go to File ^> Open Data Folder
echo 3. Navigate to MQL5 folder
echo.
echo STEP 2: COPY FILES TO CORRECT DIRECTORIES
echo ----------------------------------------
echo Copy the following files to their respective folders:
echo.
echo TO: MQL5\Indicators\
echo    - AMPD_RealTime_Precision_Indicator.mq5
echo    - AMPD_Performance_Analyzer.mq5
echo    - AMPD_Master_Dashboard.mq5
echo.
echo TO: MQL5\Experts\
echo    - AMPD_HighFrequency_AutoTrader_EA.mq5
echo.
echo TO: MQL5\Scripts\
echo    - AMPD_HighFrequency_Backtester.mq5
echo    - AMPD_System_Validator.mq5
echo.
echo STEP 3: COMPILE ALL FILES
echo ----------------------------------------
echo 1. Open MetaEditor (F4 in MT5)
echo 2. Open each .mq5 file
echo 3. Compile each file (F7)
echo 4. Verify "0 errors, 0 warnings" for all files
echo 5. Restart MT5 terminal
echo.
echo STEP 4: SYSTEM VALIDATION
echo ----------------------------------------
echo 1. Open Jump 75 Index chart (1-minute timeframe)
echo 2. Run AMPD_System_Validator.mq5 script
echo 3. Ensure 90%+ tests pass
echo 4. Address any failed tests
echo.
echo STEP 5: DEPLOY TRADING SYSTEM
echo ----------------------------------------
echo 1. Load AMPD_RealTime_Precision_Indicator.mq5 FIRST
echo    Settings: High-Frequency Mode = true
echo              Every Bar Signals = true
echo              Aggressive Entry = true
echo              Signal Sensitivity = 0.5
echo.
echo 2. Load AMPD_HighFrequency_AutoTrader_EA.mq5 SECOND
echo    Settings: Auto Trading Enabled = true
echo              Lot Size = 0.01
echo              Take Profit = 40 points
echo              Stop Loss = 20 points
echo.
echo 3. Load AMPD_Master_Dashboard.mq5 THIRD (Optional)
echo    For real-time system monitoring
echo.
echo 4. Enable "Auto Trading" button in MT5 (should be GREEN)
echo.
echo STEP 6: VERIFY OPERATION
echo ----------------------------------------
echo You should see:
echo - GOLD arrows (^) below candles for BUY signals
echo - LIME arrows (v) above candles for SELL signals
echo - White dots for entry points
echo - Yellow dots for exit points
echo - Info panel showing "HIGH-FREQ ACTIVE"
echo - EA smiley face indicating active EA
echo - Trades executing automatically when arrows appear
echo.
echo ================================================================
echo PERFORMANCE TARGETS:
echo ================================================================
echo - Signal Frequency: 10-20 signals per hour
echo - Execution Speed: Less than 100ms average
echo - Success Rate: Greater than 95% execution
echo - Win Rate: Greater than 60% target
echo - Visual Confirmation: GOLD/LIME arrows + entry/exit dots
echo.
echo ================================================================
echo TROUBLESHOOTING:
echo ================================================================
echo.
echo ISSUE: No signals appearing
echo SOLUTION: Check High-Frequency Mode enabled, lower Signal Sensitivity to 0.3
echo.
echo ISSUE: Signals but no trades
echo SOLUTION: Verify Auto Trading enabled, check EA status, review spread limits
echo.
echo ISSUE: Slow execution
echo SOLUTION: Check internet connection, reduce Max Retries, contact broker
echo.
echo ISSUE: Compilation errors
echo SOLUTION: Ensure all files copied correctly, check MT5 version compatibility
echo.
echo ================================================================
echo SUPPORT FILES INCLUDED:
echo ================================================================
echo - AMPD_Complete_Setup_Guide.md (Detailed setup instructions)
echo - AMPD_Complete_Testing_Guide.md (Comprehensive testing protocol)
echo - AMPD_Deployment_Package.md (Deployment verification)
echo - AMPD_Additional_Features_Summary.md (Feature overview)
echo.
echo ================================================================
echo READY TO INSTALL? 
echo ================================================================
echo.
echo 1. Follow the steps above carefully
echo 2. Ensure all files are in correct directories
echo 3. Compile all files successfully
echo 4. Run system validation before live trading
echo 5. Start with small lot sizes for testing
echo.
echo For detailed instructions, refer to the included documentation files.
echo.
echo ================================================================
echo AMPD HIGH-FREQUENCY TRADING SYSTEM INSTALLATION COMPLETE
echo ================================================================
pause
