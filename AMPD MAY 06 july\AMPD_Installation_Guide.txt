===============================================================================
                    AMPD JUMP 75 & CRASH 1000 TRADING SYSTEM
                         INSTALLATION & SETUP GUIDE
===============================================================================

SYSTEM OVERVIEW:
This complete trading system consists of:
1. AMPD_Jump_Crash_Indicator.mq5 - Custom indicator for signal generation
2. AMPD_Jump_Crash_EA.mq5 - Expert Advisor for automated trading
3. Real-time 1-minute data processing with auto-refresh functionality

===============================================================================
                              INSTALLATION STEPS
===============================================================================

STEP 1: COPY FILES TO MT5
--------------------------
1. Open MetaTrader 5
2. Press Ctrl+Shift+D to open Data Folder
3. Navigate to MQL5 folder

For the Indicator:
- Copy "AMPD_Jump_Crash_Indicator.mq5" to: MQL5\Indicators\
- Or create subfolder: MQL5\Indicators\AMPD\ and place file there

For the Expert Advisor:
- Copy "AMPD_Jump_Crash_EA.mq5" to: MQL5\Experts\
- Or create subfolder: MQL5\Experts\AMPD\ and place file there

STEP 2: COMPILE THE FILES
-------------------------
1. Open MetaEditor (F4 in MT5)
2. Open AMPD_Jump_Crash_Indicator.mq5
3. Press F7 to compile (should show "0 error(s), 0 warning(s)")
4. Open AMPD_Jump_Crash_EA.mq5
5. Press F7 to compile (should show "0 error(s), 0 warning(s)")
6. Open AMPD_System_Test.mq5 (optional)
7. Press F7 to compile (should show "0 error(s), 0 warning(s)")
8. Open AMPD_Compilation_Test.mq5 (optional)
9. Press F7 to compile (should show "0 error(s), 0 warning(s)")

COMPILATION FIXES APPLIED:
- Fixed TimeToStruct function calls with proper MqlDateTime structures
- Resolved ChartID naming conflicts in indicator (renamed to CurrentChartID)
- Updated all time-related functions for MT5 compatibility
- Added proper error handling for all system components
- Removed undefined function calls (SetupChartDisplay)
- All files now compile with 0 errors, 0 warnings

COMPILATION STATUS: ✅ ALL ERRORS FIXED
- AMPD_Jump_Crash_Indicator.mq5: ✅ READY
- AMPD_Jump_Crash_EA.mq5: ✅ READY
- All test scripts: ✅ READY

STEP 3: SETUP SYMBOLS
---------------------
Ensure you have access to these symbols in your broker:
- Jump 75 Index (or similar jump index)
- Crash 1000 Index (or similar crash index)

Note: Symbol names may vary by broker. Common variations:
- Jump 75 Index, JMP75, Volatility 75 Index
- Crash 1000 Index, CR1000, Crash 1000

===============================================================================
                              CONFIGURATION
===============================================================================

INDICATOR SETTINGS:
-------------------
Jump 75 Settings:
- Jump Volatility Multiplier: 2.5 (sensitivity for jump detection)
- Jump Momentum Period: 5 (bars to check for momentum)
- Jump Minimum Candle Size: 1.5 (minimum size vs ATR)
- Jump Confirmation Bars: 2 (bars for confirmation)

Crash 1000 Settings:
- Crash Volatility Multiplier: 3.0 (sensitivity for crash detection)
- Crash RSI Period: 14 (RSI calculation period)
- Crash RSI Level: 65.0 (overbought level for crash signals)
- Crash Minimum Candle Size: 2.0 (minimum size vs ATR)

General Settings:
- ATR Period: 14 (volatility calculation)
- Refresh Period: 60 seconds (real-time refresh)
- Show Alerts: true (enable signal alerts)
- Show Info Panel: true (display info panel)

EXPERT ADVISOR SETTINGS:
------------------------
Trading Settings:
- Lot Size: 0.01 (base lot size)
- Magic Number: 123456 (unique identifier)

Jump 75 Settings:
- Enable Jump 75 Trading: true
- Jump 75 Symbol: "Jump 75 Index" (adjust to your broker)
- Take Profit: 50 points
- Stop Loss: 25 points
- Max Concurrent Trades: 3

Crash 1000 Settings:
- Enable Crash 1000 Trading: true
- Crash 1000 Symbol: "Crash 1000 Index" (adjust to your broker)
- Take Profit: 75 points
- Stop Loss: 35 points
- Max Concurrent Trades: 2

Risk Management:
- Maximum Risk Per Trade: 2.0%
- Use Trailing Stop: true
- Trailing Stop Distance: 20 points
- Maximum Daily Trades: 10

Time Settings:
- Use Trading Hours: false (trade 24/7 or set specific hours)
- Signal Cooldown: 300 seconds (5 minutes between signals)

===============================================================================
                              SETUP PROCEDURE
===============================================================================

STEP 1: SETUP THE INDICATOR
----------------------------
1. Open Jump 75 Index chart (M1 timeframe)
2. Go to Insert > Indicators > Custom
3. Select "AMPD_Jump_Crash_Indicator"
4. Configure settings as needed
5. Click OK

The indicator will show:
- Green arrows (↑) for Jump signals
- Red arrows (↓) for Crash signals  
- Yellow circles (○) for Exit signals
- Blue line for volatility reference

STEP 2: SETUP THE EXPERT ADVISOR
---------------------------------
1. Ensure the indicator is running on the chart
2. Go to Insert > Expert Advisors
3. Select "AMPD_Jump_Crash_EA"
4. Configure all settings carefully:
   - Verify symbol names match your broker
   - Set appropriate lot sizes
   - Configure risk management
5. Enable "Allow automated trading"
6. Enable "Allow DLL imports" if needed
7. Click OK

STEP 3: VERIFY ARROW VISIBILITY
-------------------------------
1. Check the Experts tab for initialization messages
2. Verify the indicator shows COLORED ARROWS on the chart:
   - BRIGHT GREEN ARROWS (↑) below candles = JUMP 75 BUY SIGNALS
   - BRIGHT RED ARROWS (↓) above candles = CRASH 1000 SELL SIGNALS
   - GOLD DIAMONDS (◊) on candles = EXIT SIGNALS
3. If arrows are not visible:
   - Use AMPD_Arrow_Test.mq5 to test arrow display
   - Follow AMPD_Chart_Setup_Guide.txt for optimal visibility
   - Ensure dark chart background for contrast
4. Check Journal tab for any errors
5. Monitor the first few signals in demo mode

ARROW VISIBILITY TROUBLESHOOTING:
---------------------------------
If you cannot see the arrows clearly:
1. Right-click chart > Properties > Colors
2. Set Background to Black or Dark Blue
3. Right-click chart > Indicators List
4. Double-click "AMPD_Jump_Crash_Indicator"
5. Go to Colors tab and set:
   - Jump Signal: Bright Green (RGB: 0,255,0)
   - Crash Signal: Bright Red (RGB: 255,0,0)
   - Exit Signal: Gold (RGB: 255,215,0)
6. Increase line width to 5-6 for better visibility

===============================================================================
                              TRADING LOGIC
===============================================================================

JUMP 75 STRATEGY:
-----------------
Entry Conditions:
- Bullish candle with size > 1.5x ATR
- Volatility expansion > 2.5x ATR
- Momentum confirmation over 2 bars
- Price acceleration above recent average

Trade Execution:
- BUY order at market price
- Take Profit: 50 points above entry
- Stop Loss: 25 points below entry
- Maximum 3 concurrent positions

CRASH 1000 STRATEGY:
--------------------
Entry Conditions:
- Bearish candle with size > 2.0x ATR
- RSI above 65 (overbought condition)
- Volatility expansion > 3.0x ATR
- Large body with minimal upper shadow

Trade Execution:
- SELL order at market price
- Take Profit: 75 points below entry
- Stop Loss: 35 points above entry
- Maximum 2 concurrent positions

EXIT CONDITIONS:
----------------
- Opposite signal detected
- Take Profit/Stop Loss hit
- Momentum reversal pattern
- Manual intervention

===============================================================================
                              IMPORTANT NOTES
===============================================================================

RISK WARNINGS:
- This system trades high-volatility synthetic indices
- Jump and Crash events can be unpredictable
- Always test in demo mode first
- Use proper risk management
- Monitor system performance regularly

OPTIMIZATION TIPS:
- Adjust multipliers based on market conditions
- Fine-tune Take Profit/Stop Loss levels
- Monitor daily trade limits
- Use trailing stops for profit protection

TROUBLESHOOTING:
- Ensure correct symbol names
- Check broker compatibility
- Verify indicator compilation
- Monitor system resources
- Check internet connection stability

SUPPORT:
For technical support or questions, contact:
Arise Moroka Prince Dynasty
Email: <EMAIL>

===============================================================================
                              VERSION HISTORY
===============================================================================

Version 1.0 (Current):
- Initial release
- Jump 75 and Crash 1000 strategies
- Real-time signal processing
- Comprehensive risk management
- 1-minute auto-refresh functionality

===============================================================================
