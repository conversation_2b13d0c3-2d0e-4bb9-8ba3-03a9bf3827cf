===============================================================================
                    AMPD DUAL TRADING SYSTEM SETUP GUIDE
                   CRASH 1000 & JUMP 75 INDEPENDENT SYSTEMS
===============================================================================

OVERVIEW:
=========
Two completely independent trading systems based on the proven working structure:

SYSTEM 1: CRASH 1000 INDEX
- AMPD_Crash1000_Indicator.mq5 (Red arrows above candles for SELL signals)
- AMPD_Crash1000_EA.mq5 (Automated SELL order execution)

SYSTEM 2: JUMP 75 INDEX  
- AMPD_Jump75_Indicator.mq5 (Green arrows below candles for BUY signals)
- AMPD_Jump75_EA.mq5 (Automated BUY order execution)

===============================================================================
                              INSTALLATION STEPS
===============================================================================

STEP 1: COPY FILES TO MT5
-------------------------
1. Open MetaTrader 5
2. Press Ctrl+Shift+D to open Data Folder
3. Navigate to MQL5 folder

Copy Indicators:
- AMPD_Crash1000_Indicator.mq5 → MQL5\Indicators\
- AMPD_Jump75_Indicator.mq5 → MQL5\Indicators\

Copy Expert Advisors:
- AMPD_Crash1000_EA.mq5 → MQL5\Experts\
- AMPD_Jump75_EA.mq5 → MQL5\Experts\

STEP 2: COMPILE ALL FILES
-------------------------
1. Open MetaEditor (F4 in MT5)
2. Compile each file (F7) - Should show "0 error(s), 0 warning(s)"
3. Verify all files compile successfully

STEP 3: VERIFY SYMBOLS
----------------------
Ensure you have access to these symbols:
- Crash 1000 Index (or CR1000, Crash 1000)
- Jump 75 Index (or JMP75, Volatility 75 Index)

Note: Symbol names vary by broker - adjust in EA settings accordingly.

===============================================================================
                              CRASH 1000 SYSTEM SETUP
===============================================================================

CHART SETUP:
------------
1. Open Crash 1000 Index chart
2. Set timeframe to M1 (1-minute)
3. Set dark background for optimal arrow visibility

INDICATOR SETUP:
----------------
1. Insert → Indicators → Custom → AMPD_Crash1000_Indicator
2. Configure parameters:

Crash 1000 Settings:
- Volatility Threshold: 2.5 (ATR multiplier for crash detection)
- RSI Period: 14 (standard RSI calculation)
- RSI Level: 65.0 (overbought level for crash signals)
- Candle Size: 2.0 (minimum candle size multiplier)
- ATR Period: 14 (volatility calculation period)

Alert Settings:
- Show Alerts: true
- Sound Alerts: true
- Email Alerts: false (enable if needed)
- Push Alerts: false (enable if needed)

Display Settings:
- Show Info Panel: true (live statistics)
- Show Trend Line: true (reference line)

EXPERT ADVISOR SETUP:
---------------------
1. Insert → Expert Advisors → AMPD_Crash1000_EA
2. Configure parameters:

Trading Settings:
- Lot Size: 0.01 (adjust based on account size)
- Magic Number: 100001 (unique identifier)
- Symbol: "Crash 1000 Index" (adjust to your broker)

Crash 1000 Strategy:
- Take Profit: 75 points
- Stop Loss: 35 points
- Max Trades: 3 (concurrent positions)
- Trade on Every Signal: true

Risk Management:
- Max Risk Per Trade: 2.0%
- Use Trailing Stop: true
- Trailing Stop Distance: 20 points
- Max Daily Trades: 15

Time Settings:
- Use Trading Hours: false (24/7 trading)
- Signal Cooldown: 60 seconds

3. Enable "Allow automated trading"
4. Click OK

EXPECTED BEHAVIOR:
------------------
✓ Red arrows (↓) appear above candles during crash events
✓ Automatic SELL orders execute when signals are confirmed
✓ RSI overbought conditions (65+) trigger crash detection
✓ Large bearish candles (2.0x ATR) with volatility expansion
✓ Info panel shows live RSI levels and signal status

===============================================================================
                              JUMP 75 SYSTEM SETUP
===============================================================================

CHART SETUP:
------------
1. Open Jump 75 Index chart (separate chart from Crash 1000)
2. Set timeframe to M1 (1-minute)
3. Set dark background for optimal arrow visibility

INDICATOR SETUP:
----------------
1. Insert → Indicators → Custom → AMPD_Jump75_Indicator
2. Configure parameters:

Jump 75 Settings:
- Volatility Threshold: 2.0 (ATR multiplier for jump detection)
- Momentum Period: 3 (momentum confirmation period)
- Candle Size: 1.5 (minimum candle size multiplier)
- ATR Period: 14 (volatility calculation period)
- Confirmation Bars: 1 (immediate signal confirmation)

Alert Settings:
- Show Alerts: true
- Sound Alerts: true
- Email Alerts: false (enable if needed)
- Push Alerts: false (enable if needed)

Display Settings:
- Show Info Panel: true (live statistics)
- Show Trend Line: true (reference line)

EXPERT ADVISOR SETUP:
---------------------
1. Insert → Expert Advisors → AMPD_Jump75_EA
2. Configure parameters:

Trading Settings:
- Lot Size: 0.01 (adjust based on account size)
- Magic Number: 750001 (unique identifier)
- Symbol: "Jump 75 Index" (adjust to your broker)

Jump 75 Strategy:
- Take Profit: 50 points
- Stop Loss: 25 points
- Max Trades: 5 (concurrent positions)
- Trade on Every Signal: true

Risk Management:
- Max Risk Per Trade: 1.5%
- Use Trailing Stop: true
- Trailing Stop Distance: 15 points
- Max Daily Trades: 20

Time Settings:
- Use Trading Hours: false (24/7 trading)
- Signal Cooldown: 30 seconds

3. Enable "Allow automated trading"
4. Click OK

EXPECTED BEHAVIOR:
------------------
✓ Green arrows (↑) appear below candles during jump events
✓ Automatic BUY orders execute when signals are confirmed
✓ Momentum burst detection for sudden price acceleration
✓ Bullish candles (1.5x ATR) with volatility confirmation
✓ Info panel shows live momentum levels and signal status

===============================================================================
                              OPTIMAL CHART LAYOUT
===============================================================================

RECOMMENDED WINDOW ARRANGEMENT:
-------------------------------
1. Chart 1: Crash 1000 Index M1 with Crash indicator and EA
2. Chart 2: Jump 75 Index M1 with Jump indicator and EA
3. Terminal Window: Open to monitor trades and alerts
4. Navigator: Open to manage indicators and EAs

CHART THEME SETTINGS:
---------------------
For both charts:
- Background: Black or Dark Blue
- Candles: Green (Bull), Red (Bear)
- Grid: Dark Gray
- Arrows should be clearly visible against dark background

SIGNAL FREQUENCY EXPECTATIONS:
------------------------------
Crash 1000 System:
- 1-2 red arrows per hour during volatile periods
- SELL signals during RSI overbought conditions
- Larger, less frequent but higher probability trades

Jump 75 System:
- 3-5 green arrows per hour during active periods
- BUY signals during momentum bursts
- More frequent, smaller but consistent trades

===============================================================================
                              MONITORING & MANAGEMENT
===============================================================================

WHAT TO WATCH:
--------------
✓ Arrow signals appearing regularly on both charts
✓ EAs executing trades immediately after signals
✓ Proper positioning of arrows (red above, green below)
✓ Info panels updating with live data
✓ Alert sounds for each signal type
✓ Trade confirmations in Experts tab

PERFORMANCE METRICS:
--------------------
Crash 1000 System:
- Target: 65-75% win rate
- Risk/Reward: 1:2.14 (35 SL : 75 TP)
- Expected: 1-2 signals per hour

Jump 75 System:
- Target: 60-70% win rate
- Risk/Reward: 1:2 (25 SL : 50 TP)
- Expected: 3-5 signals per hour

TROUBLESHOOTING:
----------------
No Arrows Visible:
1. Check indicator compilation (0 errors)
2. Verify symbol names match broker exactly
3. Ensure dark chart background
4. Check if sufficient market volatility

EA Not Trading:
1. Verify "Allow automated trading" is enabled
2. Check symbol names in EA settings
3. Review account balance and margin
4. Monitor Experts tab for error messages

===============================================================================
                              ADVANCED OPTIMIZATION
===============================================================================

FOR MORE FREQUENT SIGNALS:
---------------------------
Crash 1000:
- Reduce RSI Level to 60-62
- Reduce Volatility Threshold to 2.0-2.3
- Reduce Signal Cooldown to 30-45 seconds

Jump 75:
- Reduce Volatility Threshold to 1.8-2.0
- Reduce Candle Size to 1.2-1.4
- Reduce Signal Cooldown to 15-30 seconds

FOR HIGHER QUALITY SIGNALS:
----------------------------
Crash 1000:
- Increase RSI Level to 70-75
- Increase Volatility Threshold to 3.0-3.5
- Increase Signal Cooldown to 120-180 seconds

Jump 75:
- Increase Volatility Threshold to 2.5-3.0
- Increase Candle Size to 2.0-2.5
- Increase Signal Cooldown to 60-90 seconds

===============================================================================
                              FINAL CHECKLIST
===============================================================================

BEFORE GOING LIVE:
------------------
□ Both indicators compiled successfully (0 errors)
□ Both EAs compiled successfully (0 errors)
□ Chart backgrounds are dark for arrow visibility
□ Symbol names match your broker exactly
□ "Allow automated trading" enabled for both EAs
□ Risk management settings appropriate for account size
□ Demo testing completed successfully
□ Alert systems configured and working
□ Internet connection stable

READY TO TRADE:
---------------
✓ Two independent systems running simultaneously
✓ Crash 1000: Red arrows → SELL orders
✓ Jump 75: Green arrows → BUY orders
✓ Real-time signal processing and execution
✓ Comprehensive risk management
✓ Professional-grade trading automation

===============================================================================
