//+------------------------------------------------------------------+
//|                        ARISE MAY DYNASTY Signal Engine.mq5      |
//|                        Copyright 2024, Arise <PERSON> |
//|                           Elite Trading Technology - www.ampd.com |
//+------------------------------------------------------------------+
#property copyright "Arise Morok<PERSON> Prince Dynasty - Elite Trading Technology"
#property link      "https://ampd.com"
#property description "ARISE-MAY-DYNASTY Precision Signal Engine with GOLD/LIME Arrows"
#property version   "4.0"
#property indicator_chart_window
#property indicator_buffers 7
#property indicator_plots   2

//--- Plot settings for GOLD BUY arrows and LIME SELL arrows
#property indicator_label1  "ARISE BUY Signals"
#property indicator_type1   DRAW_ARROW
#property indicator_color1  clrGold
#property indicator_style1  STYLE_SOLID
#property indicator_width1  3

#property indicator_label2  "ARISE SELL Signals"
#property indicator_type2   DRAW_ARROW
#property indicator_color2  clrLime
#property indicator_style2  STYLE_SOLID
#property indicator_width2  3

//--- Input parameters for precision signal generation
input group "=== ARISE-MAY-DYNASTY SIGNAL SETTINGS ==="
input int    InpStochasticK = 2;                   // Stochastic %K Period
input int    InpStochasticD = 2;                   // Stochastic %D Period
input int    InpStochasticSlowing = 1;             // Stochastic Slowing
input ENUM_MA_METHOD InpStochasticMethod = MODE_EMA; // Stochastic MA Method
input ENUM_STO_PRICE InpStochasticPrice = STO_CLOSECLOSE; // Stochastic Price Field

input group "=== ENTRY POINT PARAMETERS ==="
input double InpOverboughtLevel = 80.0;            // Stochastic Overbought Level
input double InpOversoldLevel = 20.0;              // Stochastic Oversold Level
input int    InpSignalConfirmationBars = 1;        // Signal Confirmation Bars
input double InpMinSignalStrength = 15.0;          // Minimum Signal Strength Threshold
input bool   InpUseSignalFilters = true;           // Use Entry Signal Validation Filters

input group "=== EXIT POINT PARAMETERS ==="
input double InpBuyTakeProfit = 40.0;              // BUY Take Profit (points)
input double InpBuyStopLoss = 20.0;                // BUY Stop Loss (points)
input double InpSellTakeProfit = 40.0;             // SELL Take Profit (points)
input double InpSellStopLoss = 20.0;               // SELL Stop Loss (points)
input bool   InpUseTrailingStop = false;           // Use Trailing Stop
input double InpTrailingStopDistance = 15.0;       // Trailing Stop Distance (points)
input int    InpMaxPositionTime = 60;              // Maximum Position Time (minutes)
input double InpProfitTargetMultiplier = 2.0;      // Profit Target Multiplier

input group "=== PRECISION SIGNAL FILTERS ==="
input bool   InpEnableRealTimeProcessing = true;   // Enable Real-Time Processing
input bool   InpAntiRepaintMode = false;           // Anti-Repaint Mode (disable for testing)
input bool   InpHighPrecisionMode = true;          // High Precision Mode

input group "=== VISUAL SIGNAL SETTINGS ==="
input bool   InpShowBuySignals = true;             // Show GOLD BUY Arrows
input bool   InpShowSellSignals = true;            // Show LIME SELL Arrows
input int    InpArrowDistance = 20;                // Arrow Distance from Candle (points)
input bool   InpShowSignalAlerts = true;           // Show Signal Alerts
input bool   InpShowBackgroundZones = true;        // Show Background Zones
input bool   InpShowSignalInfo = true;             // Show Signal Information
input bool   InpShowLevels = true;                 // Show TP/SL Levels
input bool   InpShowStatistics = true;             // Show Real-Time Statistics

//--- Enhanced indicator buffers for comprehensive trading
double BuySignalBuffer[];      // Buffer 0: BUY entry signals (GOLD arrows)
double SellSignalBuffer[];     // Buffer 1: SELL entry signals (LIME arrows)
double ExitSignalBuffer[];     // Buffer 2: EXIT signals for closing positions
double SignalStrengthBuffer[]; // Buffer 3: Signal strength values (0-100)
double EntryPriceBuffer[];     // Buffer 4: Entry price levels
double TakeProfitBuffer[];     // Buffer 5: Take profit levels
double StopLossBuffer[];       // Buffer 6: Stop loss levels

//--- Global variables for signal processing
int StochasticHandle;
double StochasticMain[];
double StochasticSignal[];

datetime LastSignalTime = 0;
datetime LastProcessTime = 0;
int ProcessedBars = 0;

//--- Enhanced signal tracking variables
double LastBuySignalValue = 0;
double LastSellSignalValue = 0;
datetime LastBuySignalTime = 0;
datetime LastSellSignalTime = 0;
int TotalSignalsGenerated = 0;
int BuySignalsCount = 0;
int SellSignalsCount = 0;
double CurrentSignalStrength = 0;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
   Print("ARISE-MAY-DYNASTY: Initializing Enhanced Signal Engine...");

   // Set all indicator buffers
   SetIndexBuffer(0, BuySignalBuffer, INDICATOR_DATA);
   SetIndexBuffer(1, SellSignalBuffer, INDICATOR_DATA);
   SetIndexBuffer(2, ExitSignalBuffer, INDICATOR_CALCULATIONS);
   SetIndexBuffer(3, SignalStrengthBuffer, INDICATOR_CALCULATIONS);
   SetIndexBuffer(4, EntryPriceBuffer, INDICATOR_CALCULATIONS);
   SetIndexBuffer(5, TakeProfitBuffer, INDICATOR_CALCULATIONS);
   SetIndexBuffer(6, StopLossBuffer, INDICATOR_CALCULATIONS);

   // Set buffer properties as series
   ArraySetAsSeries(BuySignalBuffer, true);
   ArraySetAsSeries(SellSignalBuffer, true);
   ArraySetAsSeries(ExitSignalBuffer, true);
   ArraySetAsSeries(SignalStrengthBuffer, true);
   ArraySetAsSeries(EntryPriceBuffer, true);
   ArraySetAsSeries(TakeProfitBuffer, true);
   ArraySetAsSeries(StopLossBuffer, true);

   // Initialize plot buffers with empty values
   PlotIndexSetDouble(0, PLOT_EMPTY_VALUE, EMPTY_VALUE);
   PlotIndexSetDouble(1, PLOT_EMPTY_VALUE, EMPTY_VALUE);
   
   // Set arrow codes and colors for GOLD BUY and LIME SELL signals
   PlotIndexSetInteger(0, PLOT_ARROW, 233); // Up arrow for BUY (GOLD)
   PlotIndexSetInteger(1, PLOT_ARROW, 234); // Down arrow for SELL (LIME)

   // Set arrow colors explicitly for maximum visibility
   PlotIndexSetInteger(0, PLOT_LINE_COLOR, clrGold);
   PlotIndexSetInteger(1, PLOT_LINE_COLOR, clrLime);

   // Set arrow width for better visibility
   PlotIndexSetInteger(0, PLOT_LINE_WIDTH, 4);
   PlotIndexSetInteger(1, PLOT_LINE_WIDTH, 4);

   // Set plot labels for legend
   PlotIndexSetString(0, PLOT_LABEL, "ARISE BUY (GOLD)");
   PlotIndexSetString(1, PLOT_LABEL, "ARISE SELL (LIME)");
   
   // Initialize Stochastic oscillator
   StochasticHandle = iStochastic(_Symbol, PERIOD_M1, 
                                  InpStochasticK, InpStochasticD, InpStochasticSlowing,
                                  InpStochasticMethod, InpStochasticPrice);
   
   if(StochasticHandle == INVALID_HANDLE)
   {
      Print("ARISE-MAY-DYNASTY: ERROR - Cannot create Stochastic indicator");
      return INIT_FAILED;
   }
   
   // Initialize arrays for Stochastic data
   ArraySetAsSeries(StochasticMain, true);
   ArraySetAsSeries(StochasticSignal, true);
   
   // Set indicator properties
   IndicatorSetString(INDICATOR_SHORTNAME, "ARISE-MAY-DYNASTY Signal Engine");
   IndicatorSetInteger(INDICATOR_DIGITS, _Digits);
   
   Print("ARISE-MAY-DYNASTY: Signal Engine initialized successfully");
   Print("ARISE-MAY-DYNASTY: Real-Time Processing: ", InpEnableRealTimeProcessing ? "ENABLED" : "DISABLED");
   Print("ARISE-MAY-DYNASTY: High Precision Mode: ", InpHighPrecisionMode ? "ACTIVE" : "INACTIVE");
   Print("ARISE-MAY-DYNASTY: Anti-Repaint Mode: ", InpAntiRepaintMode ? "ACTIVE" : "INACTIVE");
   
   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Custom indicator deinitialization function                      |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   if(StochasticHandle != INVALID_HANDLE)
      IndicatorRelease(StochasticHandle);
   
   Print("ARISE-MAY-DYNASTY: Signal Engine deinitialized");
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
   // Check if we have enough data
   if(rates_total < 10)
      return 0;
   
   // Set arrays as series
   ArraySetAsSeries(time, true);
   ArraySetAsSeries(open, true);
   ArraySetAsSeries(high, true);
   ArraySetAsSeries(low, true);
   ArraySetAsSeries(close, true);
   
   // Get Stochastic data
   if(!GetStochasticData(rates_total))
      return prev_calculated;
   
   // Calculate signals
   int limit = rates_total - prev_calculated;
   if(limit > rates_total - 10)
      limit = rates_total - 10;
   
   // Process signals with enhanced buffer initialization
   for(int i = limit; i >= 0; i--)
   {
      // Initialize all buffers with proper empty values
      BuySignalBuffer[i] = EMPTY_VALUE;
      SellSignalBuffer[i] = EMPTY_VALUE;
      ExitSignalBuffer[i] = EMPTY_VALUE;
      SignalStrengthBuffer[i] = 0.0;
      EntryPriceBuffer[i] = 0.0;
      TakeProfitBuffer[i] = 0.0;
      StopLossBuffer[i] = 0.0;

      // Skip current bar if anti-repaint mode is enabled
      if(InpAntiRepaintMode && i == 0)
         continue;

      // Generate enhanced precision signals
      GenerateEnhancedPrecisionSignals(i, high, low, close, time);
   }
   
   // Real-time processing for current bar
   if(InpEnableRealTimeProcessing && !InpAntiRepaintMode)
   {
      ProcessRealTimeSignals(0, high, low, close, time);
   }

   // Update statistics
   UpdateSignalStatistics();
   
   return rates_total;
}

//+------------------------------------------------------------------+
//| Get Stochastic oscillator data                                  |
//+------------------------------------------------------------------+
bool GetStochasticData(int rates_total)
{
   if(CopyBuffer(StochasticHandle, 0, 0, rates_total, StochasticMain) <= 0)
      return false;
   
   if(CopyBuffer(StochasticHandle, 1, 0, rates_total, StochasticSignal) <= 0)
      return false;
   
   return true;
}

//+------------------------------------------------------------------+
//| Generate enhanced precision signals with comprehensive data     |
//+------------------------------------------------------------------+
void GenerateEnhancedPrecisionSignals(int index, const double &high[], const double &low[],
                                     const double &close[], const datetime &time[])
{
   // Skip if not enough data
   if(index >= ArraySize(StochasticMain) - 2)
      return;
   
   // Get current and previous Stochastic values
   double currentMain = StochasticMain[index];
   double currentSignal = StochasticSignal[index];
   double prevMain = StochasticMain[index + 1];
   double prevSignal = StochasticSignal[index + 1];
   
   // Calculate signal strength
   double signalStrength = MathAbs(currentMain - currentSignal);
   
   // Check for BUY signal (GOLD arrows) with enhanced validation
   if(InpShowBuySignals && IsBuySignalValid(index, currentMain, currentSignal, prevMain, prevSignal, signalStrength))
   {
      double arrowPrice = low[index] - (InpArrowDistance * _Point);
      double entryPrice = close[index];
      double takeProfit = entryPrice + (InpBuyTakeProfit * _Point);
      double stopLoss = entryPrice - (InpBuyStopLoss * _Point);

      // Set all buffer values for comprehensive signal data
      BuySignalBuffer[index] = arrowPrice;
      SignalStrengthBuffer[index] = signalStrength;
      EntryPriceBuffer[index] = entryPrice;
      TakeProfitBuffer[index] = takeProfit;
      StopLossBuffer[index] = stopLoss;

      // Update tracking variables
      LastBuySignalValue = arrowPrice;
      LastBuySignalTime = time[index];
      BuySignalsCount++;
      TotalSignalsGenerated++;
      CurrentSignalStrength = signalStrength;

      // Generate enhanced alert with complete information
      if(InpShowSignalAlerts && index <= 1)
      {
         string alertMsg = StringFormat("ARISE-MAY-DYNASTY: 🟡 GOLD BUY SIGNAL at %.5f | TP: %.5f | SL: %.5f | Strength: %.1f%%",
                                       entryPrice, takeProfit, stopLoss, signalStrength);
         Alert(alertMsg);
         Print("ARISE-MAY-DYNASTY: Enhanced BUY signal generated at ", TimeToString(time[index], TIME_SECONDS));
         Print("ARISE-MAY-DYNASTY: Entry: ", DoubleToString(entryPrice, _Digits),
               " | TP: ", DoubleToString(takeProfit, _Digits),
               " | SL: ", DoubleToString(stopLoss, _Digits));
      }
   }
   
   // Check for SELL signal (LIME arrows) with enhanced validation
   if(InpShowSellSignals && IsSellSignalValid(index, currentMain, currentSignal, prevMain, prevSignal, signalStrength))
   {
      double arrowPrice = high[index] + (InpArrowDistance * _Point);
      double entryPrice = close[index];
      double takeProfit = entryPrice - (InpSellTakeProfit * _Point);
      double stopLoss = entryPrice + (InpSellStopLoss * _Point);

      // Set all buffer values for comprehensive signal data
      SellSignalBuffer[index] = arrowPrice;
      SignalStrengthBuffer[index] = signalStrength;
      EntryPriceBuffer[index] = entryPrice;
      TakeProfitBuffer[index] = takeProfit;
      StopLossBuffer[index] = stopLoss;

      // Update tracking variables
      LastSellSignalValue = arrowPrice;
      LastSellSignalTime = time[index];
      SellSignalsCount++;
      TotalSignalsGenerated++;
      CurrentSignalStrength = signalStrength;

      // Generate enhanced alert with complete information
      if(InpShowSignalAlerts && index <= 1)
      {
         string alertMsg = StringFormat("ARISE-MAY-DYNASTY: 🟢 LIME SELL SIGNAL at %.5f | TP: %.5f | SL: %.5f | Strength: %.1f%%",
                                       entryPrice, takeProfit, stopLoss, signalStrength);
         Alert(alertMsg);
         Print("ARISE-MAY-DYNASTY: Enhanced SELL signal generated at ", TimeToString(time[index], TIME_SECONDS));
         Print("ARISE-MAY-DYNASTY: Entry: ", DoubleToString(entryPrice, _Digits),
               " | TP: ", DoubleToString(takeProfit, _Digits),
               " | SL: ", DoubleToString(stopLoss, _Digits));
      }
   }
}

//+------------------------------------------------------------------+
//| Check if BUY signal is valid                                    |
//+------------------------------------------------------------------+
bool IsBuySignalValid(int index, double currentMain, double currentSignal,
                     double prevMain, double prevSignal, double signalStrength)
{
   // Enhanced signal validation with new parameters
   if(InpHighPrecisionMode)
   {
      // Check for Stochastic crossover from oversold with enhanced conditions
      bool crossoverCondition = (currentMain > currentSignal && prevMain <= prevSignal);
      bool oversoldCondition = (currentMain < InpOversoldLevel);
      bool strengthCondition = (signalStrength >= InpMinSignalStrength);

      if(crossoverCondition && oversoldCondition && strengthCondition)
      {
         // Additional filter validation if enabled
         if(InpUseSignalFilters)
         {
            // Check for confirmation bars
            if(InpSignalConfirmationBars > 1)
            {
               // Validate signal strength over multiple bars
               return ValidateSignalConfirmation(index, true);
            }
         }
         return true;
      }
   }
   else
   {
      // Standard signal validation with basic crossover
      if(currentMain > currentSignal && prevMain <= prevSignal && currentMain < InpOversoldLevel)
      {
         return true;
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| Check if SELL signal is valid                                   |
//+------------------------------------------------------------------+
bool IsSellSignalValid(int index, double currentMain, double currentSignal,
                      double prevMain, double prevSignal, double signalStrength)
{
   // Enhanced signal validation with new parameters
   if(InpHighPrecisionMode)
   {
      // Check for Stochastic crossover from overbought with enhanced conditions
      bool crossoverCondition = (currentMain < currentSignal && prevMain >= prevSignal);
      bool overboughtCondition = (currentMain > InpOverboughtLevel);
      bool strengthCondition = (signalStrength >= InpMinSignalStrength);

      if(crossoverCondition && overboughtCondition && strengthCondition)
      {
         // Additional filter validation if enabled
         if(InpUseSignalFilters)
         {
            // Check for confirmation bars
            if(InpSignalConfirmationBars > 1)
            {
               // Validate signal strength over multiple bars
               return ValidateSignalConfirmation(index, false);
            }
         }
         return true;
      }
   }
   else
   {
      // Standard signal validation with basic crossover
      if(currentMain < currentSignal && prevMain >= prevSignal && currentMain > InpOverboughtLevel)
      {
         return true;
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| Validate signal confirmation over multiple bars                 |
//+------------------------------------------------------------------+
bool ValidateSignalConfirmation(int index, bool isBuySignal)
{
   // Simple confirmation validation - can be enhanced further
   if(index + InpSignalConfirmationBars >= ArraySize(StochasticMain))
      return false;

   int confirmedBars = 0;
   for(int i = 0; i < InpSignalConfirmationBars; i++)
   {
      double main = StochasticMain[index + i];
      double signal = StochasticSignal[index + i];

      if(isBuySignal)
      {
         if(main > signal && main < InpOversoldLevel)
            confirmedBars++;
      }
      else
      {
         if(main < signal && main > InpOverboughtLevel)
            confirmedBars++;
      }
   }

   return (confirmedBars >= InpSignalConfirmationBars);
}

//+------------------------------------------------------------------+
//| Update signal statistics                                         |
//+------------------------------------------------------------------+
void UpdateSignalStatistics()
{
   // Update real-time statistics for display
   static datetime lastUpdate = 0;
   datetime currentTime = TimeCurrent();

   if(currentTime - lastUpdate >= 30) // Update every 30 seconds
   {
      lastUpdate = currentTime;

      if(InpShowStatistics)
      {
         string statsText = StringFormat("ARISE-MAY-DYNASTY Stats | Total: %d | BUY: %d | SELL: %d | Strength: %.1f%%",
                                        TotalSignalsGenerated, BuySignalsCount, SellSignalsCount, CurrentSignalStrength);
         Comment(statsText);
      }
   }
}

//+------------------------------------------------------------------+
//| Process real-time signals for current bar                       |
//+------------------------------------------------------------------+
void ProcessRealTimeSignals(int index, const double &high[], const double &low[],
                           const double &close[], const datetime &time[])
{
   static datetime lastProcessTime = 0;

   // Process every 100ms for real-time responsiveness
   if(TimeCurrent() - lastProcessTime < 0.1)
      return;

   lastProcessTime = TimeCurrent();

   // Generate signals for current bar
   GenerateEnhancedPrecisionSignals(index, high, low, close, time);
}
