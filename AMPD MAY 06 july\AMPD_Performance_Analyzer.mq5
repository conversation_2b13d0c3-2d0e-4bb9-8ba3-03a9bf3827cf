//+------------------------------------------------------------------+
//|                           AMPD Performance Analyzer.mq5         |
//|                        Copyright 2024, <PERSON><PERSON> |
//|                                             https://www.ampd.com |
//+------------------------------------------------------------------+
#property copyright "Arise Moroka Prince Dynasty"
#property link      "https://ampd.com"
#property description "Real-Time Performance Analysis Tool for AMPD High-Frequency Trading System"
#property version   "1.0"
#property indicator_separate_window
#property indicator_buffers 6
#property indicator_plots   4

//--- Plot definitions
#property indicator_label1  "Equity Curve"
#property indicator_type1   DRAW_LINE
#property indicator_color1  clrGreen
#property indicator_width1  2

#property indicator_label2  "Drawdown"
#property indicator_type2   DRAW_LINE
#property indicator_color2  clrRed
#property indicator_width2  2

#property indicator_label3  "Win Rate"
#property indicator_type3   DRAW_LINE
#property indicator_color3  clrBlue
#property indicator_width3  1

#property indicator_label4  "Signal Frequency"
#property indicator_type4   DRAW_HISTOGRAM
#property indicator_color4  clrGold
#property indicator_width4  3

//--- Input parameters
input group "=== ANALYSIS SETTINGS ==="
input int    InpAnalysisPeriod = 100;             // Analysis Period (bars)
input bool   InpRealTimeAnalysis = true;          // Real-Time Analysis
input bool   InpShowStatistics = true;            // Show Statistics Panel
input bool   InpTrackExecutionSpeed = true;       // Track Execution Speed
input string InpEAMagicNumber = "123456";          // EA Magic Number to Track

input group "=== PERFORMANCE TARGETS ==="
input double InpTargetWinRate = 60.0;             // Target Win Rate (%)
input double InpTargetSignalsPerHour = 15.0;      // Target Signals per Hour
input double InpTargetExecutionSpeed = 100.0;     // Target Execution Speed (ms)
input double InpTargetProfitFactor = 1.5;         // Target Profit Factor

//--- Indicator buffers
double EquityCurveBuffer[];
double DrawdownBuffer[];
double WinRateBuffer[];
double SignalFrequencyBuffer[];
double StatisticsBuffer[];
double ExecutionSpeedBuffer[];

//--- Global variables for analysis
struct PerformanceData
{
   datetime time;
   double   equity;
   double   drawdown;
   double   winRate;
   int      totalTrades;
   int      winningTrades;
   double   totalProfit;
   double   totalLoss;
   double   executionSpeed;
   int      signalsPerHour;
};

PerformanceData performanceHistory[];
int dataPoints = 0;
datetime lastAnalysisTime = 0;

//--- Statistics tracking
int totalSignals = 0;
int totalExecutions = 0;
double totalExecutionTime = 0;
datetime sessionStartTime = 0;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
   //--- Set indicator properties
   IndicatorSetString(INDICATOR_SHORTNAME, "AMPD Performance Analyzer");
   IndicatorSetInteger(INDICATOR_DIGITS, 2);
   
   //--- Map indicator buffers
   SetIndexBuffer(0, EquityCurveBuffer, INDICATOR_DATA);
   SetIndexBuffer(1, DrawdownBuffer, INDICATOR_DATA);
   SetIndexBuffer(2, WinRateBuffer, INDICATOR_DATA);
   SetIndexBuffer(3, SignalFrequencyBuffer, INDICATOR_DATA);
   SetIndexBuffer(4, StatisticsBuffer, INDICATOR_CALCULATIONS);
   SetIndexBuffer(5, ExecutionSpeedBuffer, INDICATOR_CALCULATIONS);
   
   //--- Set array properties
   ArraySetAsSeries(EquityCurveBuffer, true);
   ArraySetAsSeries(DrawdownBuffer, true);
   ArraySetAsSeries(WinRateBuffer, true);
   ArraySetAsSeries(SignalFrequencyBuffer, true);
   ArraySetAsSeries(StatisticsBuffer, true);
   ArraySetAsSeries(ExecutionSpeedBuffer, true);
   
   //--- Set empty values
   PlotIndexSetDouble(0, PLOT_EMPTY_VALUE, 0);
   PlotIndexSetDouble(1, PLOT_EMPTY_VALUE, 0);
   PlotIndexSetDouble(2, PLOT_EMPTY_VALUE, 0);
   PlotIndexSetDouble(3, PLOT_EMPTY_VALUE, 0);
   
   //--- Initialize performance tracking
   ArrayResize(performanceHistory, 1000);
   sessionStartTime = TimeCurrent();
   
   //--- Create statistics panel
   if(InpShowStatistics)
      CreateStatisticsPanel();
   
   Print("AMPD Performance Analyzer initialized successfully");
   Print("Tracking EA Magic Number: ", InpEAMagicNumber);
   Print("Analysis Period: ", InpAnalysisPeriod, " bars");
   
   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                             |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
   if(rates_total < InpAnalysisPeriod) return 0;
   
   //--- Set arrays as series
   ArraySetAsSeries(time, true);
   
   //--- Real-time analysis
   if(InpRealTimeAnalysis)
   {
      datetime currentTime = TimeCurrent();
      if(currentTime - lastAnalysisTime >= 60) // Update every minute
      {
         lastAnalysisTime = currentTime;
         PerformRealTimeAnalysis();
      }
   }
   
   //--- Calculate performance metrics for visible bars
   int limit = rates_total - prev_calculated;
   if(prev_calculated == 0)
      limit = InpAnalysisPeriod;
   
   for(int i = limit; i >= 0 && !IsStopped(); i--)
   {
      CalculatePerformanceMetrics(i, time);
   }
   
   //--- Update statistics panel
   if(InpShowStatistics)
      UpdateStatisticsPanel();
   
   return rates_total;
}

//+------------------------------------------------------------------+
//| Perform real-time performance analysis                          |
//+------------------------------------------------------------------+
void PerformRealTimeAnalysis()
{
   //--- Get current account information
   double currentEquity = AccountInfoDouble(ACCOUNT_EQUITY);
   double currentBalance = AccountInfoDouble(ACCOUNT_BALANCE);
   double currentDrawdown = currentBalance > 0 ? (currentBalance - currentEquity) / currentBalance * 100 : 0;
   
   //--- Analyze recent trades
   AnalyzeRecentTrades();
   
   //--- Calculate signal frequency
   CalculateSignalFrequency();
   
   //--- Track execution performance
   if(InpTrackExecutionSpeed)
      TrackExecutionPerformance();
   
   //--- Store performance data
   if(dataPoints < ArraySize(performanceHistory))
   {
      performanceHistory[dataPoints].time = TimeCurrent();
      performanceHistory[dataPoints].equity = currentEquity;
      performanceHistory[dataPoints].drawdown = currentDrawdown;
      performanceHistory[dataPoints].totalTrades = GetTotalTrades();
      performanceHistory[dataPoints].winningTrades = GetWinningTrades();
      performanceHistory[dataPoints].winRate = CalculateCurrentWinRate();
      performanceHistory[dataPoints].executionSpeed = CalculateAverageExecutionSpeed();
      performanceHistory[dataPoints].signalsPerHour = CalculateSignalsPerHour();
      
      dataPoints++;
   }
}

//+------------------------------------------------------------------+
//| Calculate performance metrics for specific bar                  |
//+------------------------------------------------------------------+
void CalculatePerformanceMetrics(int index, const datetime &time[])
{
   //--- Initialize buffers
   EquityCurveBuffer[index] = 0;
   DrawdownBuffer[index] = 0;
   WinRateBuffer[index] = 0;
   SignalFrequencyBuffer[index] = 0;
   
   //--- Find corresponding performance data
   for(int i = 0; i < dataPoints; i++)
   {
      if(performanceHistory[i].time <= time[index])
      {
         EquityCurveBuffer[index] = performanceHistory[i].equity;
         DrawdownBuffer[index] = performanceHistory[i].drawdown;
         WinRateBuffer[index] = performanceHistory[i].winRate;
         SignalFrequencyBuffer[index] = performanceHistory[i].signalsPerHour;
         break;
      }
   }
}

//+------------------------------------------------------------------+
//| Analyze recent trading activity                                 |
//+------------------------------------------------------------------+
void AnalyzeRecentTrades()
{
   //--- Select recent history
   if(!HistorySelect(TimeCurrent() - 3600, TimeCurrent())) // Last hour
      return;
   
   int recentTrades = 0;
   int recentWins = 0;
   double recentProfit = 0;
   double recentLoss = 0;
   
   //--- Analyze recent deals
   for(int i = 0; i < HistoryDealsTotal(); i++)
   {
      ulong ticket = HistoryDealGetTicket(i);
      if(HistoryDealGetString(ticket, DEAL_SYMBOL) == _Symbol &&
         IntegerToString(HistoryDealGetInteger(ticket, DEAL_MAGIC)) == InpEAMagicNumber)
      {
         if(HistoryDealGetInteger(ticket, DEAL_ENTRY) == DEAL_ENTRY_OUT)
         {
            recentTrades++;
            double profit = HistoryDealGetDouble(ticket, DEAL_PROFIT);
            
            if(profit > 0)
            {
               recentWins++;
               recentProfit += profit;
            }
            else
            {
               recentLoss += MathAbs(profit);
            }
         }
      }
   }
   
   //--- Update statistics
   if(recentTrades > 0)
   {
      double recentWinRate = (double)recentWins / recentTrades * 100;
      double recentProfitFactor = recentLoss > 0 ? recentProfit / recentLoss : 0;
      
      //--- Performance alerts
      if(recentWinRate < InpTargetWinRate)
      {
         Print("⚠️ Win rate below target: ", DoubleToString(recentWinRate, 1), "% (Target: ", 
               DoubleToString(InpTargetWinRate, 1), "%)");
      }
      
      if(recentProfitFactor < InpTargetProfitFactor)
      {
         Print("⚠️ Profit factor below target: ", DoubleToString(recentProfitFactor, 2), 
               " (Target: ", DoubleToString(InpTargetProfitFactor, 2), ")");
      }
   }
}

//+------------------------------------------------------------------+
//| Calculate current signal frequency                               |
//+------------------------------------------------------------------+
void CalculateSignalFrequency()
{
   datetime currentTime = TimeCurrent();
   double hoursElapsed = (double)(currentTime - sessionStartTime) / 3600;
   
   if(hoursElapsed > 0)
   {
      double signalsPerHour = totalSignals / hoursElapsed;
      
      if(signalsPerHour < InpTargetSignalsPerHour)
      {
         static datetime lastFrequencyWarning = 0;
         if(currentTime - lastFrequencyWarning > 1800) // Warn every 30 minutes
         {
            Print("⚠️ Signal frequency below target: ", DoubleToString(signalsPerHour, 1), 
                  " signals/hour (Target: ", DoubleToString(InpTargetSignalsPerHour, 1), ")");
            lastFrequencyWarning = currentTime;
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Track execution performance                                      |
//+------------------------------------------------------------------+
void TrackExecutionPerformance()
{
   //--- This would integrate with the EA's execution logging
   //--- For now, we'll simulate based on recent trade timing
   
   if(totalExecutions > 0)
   {
      double avgExecutionSpeed = totalExecutionTime / totalExecutions;
      
      if(avgExecutionSpeed > InpTargetExecutionSpeed)
      {
         static datetime lastSpeedWarning = 0;
         datetime currentTime = TimeCurrent();
         
         if(currentTime - lastSpeedWarning > 1800) // Warn every 30 minutes
         {
            Print("⚠️ Execution speed above target: ", DoubleToString(avgExecutionSpeed, 1), 
                  "ms (Target: <", DoubleToString(InpTargetExecutionSpeed, 1), "ms)");
            lastSpeedWarning = currentTime;
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Get total number of trades                                       |
//+------------------------------------------------------------------+
int GetTotalTrades()
{
   if(!HistorySelect(sessionStartTime, TimeCurrent()))
      return 0;
   
   int count = 0;
   for(int i = 0; i < HistoryDealsTotal(); i++)
   {
      ulong ticket = HistoryDealGetTicket(i);
      if(HistoryDealGetString(ticket, DEAL_SYMBOL) == _Symbol &&
         IntegerToString(HistoryDealGetInteger(ticket, DEAL_MAGIC)) == InpEAMagicNumber &&
         HistoryDealGetInteger(ticket, DEAL_ENTRY) == DEAL_ENTRY_OUT)
      {
         count++;
      }
   }
   return count;
}

//+------------------------------------------------------------------+
//| Get number of winning trades                                     |
//+------------------------------------------------------------------+
int GetWinningTrades()
{
   if(!HistorySelect(sessionStartTime, TimeCurrent()))
      return 0;
   
   int count = 0;
   for(int i = 0; i < HistoryDealsTotal(); i++)
   {
      ulong ticket = HistoryDealGetTicket(i);
      if(HistoryDealGetString(ticket, DEAL_SYMBOL) == _Symbol &&
         IntegerToString(HistoryDealGetInteger(ticket, DEAL_MAGIC)) == InpEAMagicNumber &&
         HistoryDealGetInteger(ticket, DEAL_ENTRY) == DEAL_ENTRY_OUT &&
         HistoryDealGetDouble(ticket, DEAL_PROFIT) > 0)
      {
         count++;
      }
   }
   return count;
}

//+------------------------------------------------------------------+
//| Calculate current win rate                                       |
//+------------------------------------------------------------------+
double CalculateCurrentWinRate()
{
   int totalTrades = GetTotalTrades();
   int winningTrades = GetWinningTrades();
   
   return totalTrades > 0 ? (double)winningTrades / totalTrades * 100 : 0;
}

//+------------------------------------------------------------------+
//| Calculate average execution speed                                |
//+------------------------------------------------------------------+
double CalculateAverageExecutionSpeed()
{
   return totalExecutions > 0 ? totalExecutionTime / totalExecutions : 0;
}

//+------------------------------------------------------------------+
//| Calculate signals per hour                                       |
//+------------------------------------------------------------------+
double CalculateSignalsPerHour()
{
   double hoursElapsed = (double)(TimeCurrent() - sessionStartTime) / 3600;
   return hoursElapsed > 0 ? totalSignals / hoursElapsed : 0;
}

//+------------------------------------------------------------------+
//| Create statistics panel                                          |
//+------------------------------------------------------------------+
void CreateStatisticsPanel()
{
   long chartID = ChartID();

   //--- Create background panel
   ObjectCreate(chartID, "AMPD_Stats_Panel", OBJ_RECTANGLE_LABEL, 0, 0, 0);
   ObjectSetInteger(chartID, "AMPD_Stats_Panel", OBJPROP_XDISTANCE, 10);
   ObjectSetInteger(chartID, "AMPD_Stats_Panel", OBJPROP_YDISTANCE, 30);
   ObjectSetInteger(chartID, "AMPD_Stats_Panel", OBJPROP_XSIZE, 300);
   ObjectSetInteger(chartID, "AMPD_Stats_Panel", OBJPROP_YSIZE, 200);
   ObjectSetInteger(chartID, "AMPD_Stats_Panel", OBJPROP_BGCOLOR, clrDarkSlateGray);
   ObjectSetInteger(chartID, "AMPD_Stats_Panel", OBJPROP_BORDER_COLOR, clrGold);

   //--- Create labels
   string labels[] = {
      "AMPD PERFORMANCE ANALYZER",
      "Win Rate: ",
      "Profit Factor: ",
      "Signals/Hour: ",
      "Avg Execution: ",
      "Total Trades: ",
      "Current Drawdown: ",
      "Session P&L: ",
      "Status: "
   };

   for(int i = 0; i < ArraySize(labels); i++)
   {
      string objName = "AMPD_Stats_Label_" + IntegerToString(i);
      ObjectCreate(chartID, objName, OBJ_LABEL, 0, 0, 0);
      ObjectSetInteger(chartID, objName, OBJPROP_XDISTANCE, 15);
      ObjectSetInteger(chartID, objName, OBJPROP_YDISTANCE, 40 + i * 20);
      ObjectSetString(chartID, objName, OBJPROP_TEXT, labels[i]);
      ObjectSetString(chartID, objName, OBJPROP_FONT, "Consolas");
      ObjectSetInteger(chartID, objName, OBJPROP_FONTSIZE, i == 0 ? 10 : 9);
      ObjectSetInteger(chartID, objName, OBJPROP_COLOR, i == 0 ? clrGold : clrWhite);
   }
}

//+------------------------------------------------------------------+
//| Update statistics panel                                          |
//+------------------------------------------------------------------+
void UpdateStatisticsPanel()
{
   long chartID = ChartID();

   //--- Calculate current statistics
   double currentWinRate = CalculateCurrentWinRate();
   int totalTrades = GetTotalTrades();
   double signalsPerHour = CalculateSignalsPerHour();
   double avgExecutionSpeed = CalculateAverageExecutionSpeed();
   double currentDrawdown = AccountInfoDouble(ACCOUNT_BALANCE) > 0 ?
                           (AccountInfoDouble(ACCOUNT_BALANCE) - AccountInfoDouble(ACCOUNT_EQUITY)) /
                           AccountInfoDouble(ACCOUNT_BALANCE) * 100 : 0;

   //--- Calculate session P&L
   double sessionPL = CalculateSessionPL();

   //--- Calculate profit factor
   double profitFactor = CalculateProfitFactor();

   //--- Determine status
   string status = "OPTIMAL";
   color statusColor = clrGreen;

   if(currentWinRate < InpTargetWinRate || signalsPerHour < InpTargetSignalsPerHour ||
      avgExecutionSpeed > InpTargetExecutionSpeed)
   {
      status = "SUBOPTIMAL";
      statusColor = clrOrange;
   }

   if(currentDrawdown > 10 || sessionPL < -100)
   {
      status = "RISK WARNING";
      statusColor = clrRed;
   }

   //--- Update labels
   ObjectSetString(chartID, "AMPD_Stats_Label_1", OBJPROP_TEXT,
                   "Win Rate: " + DoubleToString(currentWinRate, 1) + "%");
   ObjectSetString(chartID, "AMPD_Stats_Label_2", OBJPROP_TEXT,
                   "Profit Factor: " + DoubleToString(profitFactor, 2));
   ObjectSetString(chartID, "AMPD_Stats_Label_3", OBJPROP_TEXT,
                   "Signals/Hour: " + DoubleToString(signalsPerHour, 1));
   ObjectSetString(chartID, "AMPD_Stats_Label_4", OBJPROP_TEXT,
                   "Avg Execution: " + DoubleToString(avgExecutionSpeed, 1) + "ms");
   ObjectSetString(chartID, "AMPD_Stats_Label_5", OBJPROP_TEXT,
                   "Total Trades: " + IntegerToString(totalTrades));
   ObjectSetString(chartID, "AMPD_Stats_Label_6", OBJPROP_TEXT,
                   "Current Drawdown: " + DoubleToString(currentDrawdown, 2) + "%");
   ObjectSetString(chartID, "AMPD_Stats_Label_7", OBJPROP_TEXT,
                   "Session P&L: $" + DoubleToString(sessionPL, 2));
   ObjectSetString(chartID, "AMPD_Stats_Label_8", OBJPROP_TEXT,
                   "Status: " + status);
   ObjectSetInteger(chartID, "AMPD_Stats_Label_8", OBJPROP_COLOR, statusColor);
}

//+------------------------------------------------------------------+
//| Calculate session profit/loss                                   |
//+------------------------------------------------------------------+
double CalculateSessionPL()
{
   if(!HistorySelect(sessionStartTime, TimeCurrent()))
      return 0;

   double totalPL = 0;
   for(int i = 0; i < HistoryDealsTotal(); i++)
   {
      ulong ticket = HistoryDealGetTicket(i);
      if(HistoryDealGetString(ticket, DEAL_SYMBOL) == _Symbol &&
         IntegerToString(HistoryDealGetInteger(ticket, DEAL_MAGIC)) == InpEAMagicNumber)
      {
         totalPL += HistoryDealGetDouble(ticket, DEAL_PROFIT);
      }
   }
   return totalPL;
}

//+------------------------------------------------------------------+
//| Calculate profit factor                                          |
//+------------------------------------------------------------------+
double CalculateProfitFactor()
{
   if(!HistorySelect(sessionStartTime, TimeCurrent()))
      return 0;

   double totalProfit = 0;
   double totalLoss = 0;

   for(int i = 0; i < HistoryDealsTotal(); i++)
   {
      ulong ticket = HistoryDealGetTicket(i);
      if(HistoryDealGetString(ticket, DEAL_SYMBOL) == _Symbol &&
         IntegerToString(HistoryDealGetInteger(ticket, DEAL_MAGIC)) == InpEAMagicNumber &&
         HistoryDealGetInteger(ticket, DEAL_ENTRY) == DEAL_ENTRY_OUT)
      {
         double profit = HistoryDealGetDouble(ticket, DEAL_PROFIT);
         if(profit > 0)
            totalProfit += profit;
         else
            totalLoss += MathAbs(profit);
      }
   }

   return totalLoss > 0 ? totalProfit / totalLoss : 0;
}

//+------------------------------------------------------------------+
//| Indicator deinitialization function                             |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   //--- Remove all objects
   long chartID = ChartID();
   ObjectDelete(chartID, "AMPD_Stats_Panel");

   for(int i = 0; i < 9; i++)
   {
      ObjectDelete(chartID, "AMPD_Stats_Label_" + IntegerToString(i));
   }

   //--- Clean up arrays
   ArrayFree(performanceHistory);

   Print("AMPD Performance Analyzer deinitialized");
}
