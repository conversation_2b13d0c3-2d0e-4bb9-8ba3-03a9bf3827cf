//+------------------------------------------------------------------+
//|                                   AMPD Final Validation.mq5     |
//|                        Copyright 2024, <PERSON><PERSON> |
//|                                             https://www.ampd.com |
//+------------------------------------------------------------------+
#property copyright "Arise Moroka Prince Dynasty"
#property link      "https://ampd.com"
#property description "Final Validation Script for AMPD Trading System"
#property version   "1.0"
#property script_show_inputs

//--- Input parameters
input string InpValidationSymbol = "Jump 75 Index";  // Symbol for Validation
input bool   InpRunFullValidation = true;            // Run Full System Validation
input bool   InpShowDetailedLog = true;              // Show Detailed Validation Log

//--- Global variables
int ValidationsPassed = 0;
int ValidationsTotal = 0;

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
   Print("===============================================");
   Print("    AMPD TRADING SYSTEM FINAL VALIDATION");
   Print("===============================================");
   Print("Validation Symbol: ", InpValidationSymbol);
   Print("Full Validation: ", InpRunFullValidation ? "YES" : "NO");
   Print("===============================================");
   
   // Reset counters
   ValidationsPassed = 0;
   ValidationsTotal = 0;
   
   // Run validation tests
   ValidateTimeStructFunctions();
   ValidateIndicatorCreation();
   ValidateArrayOperations();
   ValidateSymbolFunctions();
   ValidateAccountFunctions();
   
   if(InpRunFullValidation)
   {
      ValidateCustomIndicatorLoading();
      ValidateTradingEnvironment();
      ValidateRiskCalculations();
   }
   
   // Display final results
   Print("===============================================");
   Print("    VALIDATION RESULTS");
   Print("===============================================");
   Print("Tests Passed: ", ValidationsPassed, " / ", ValidationsTotal);
   
   double successRate = ValidationsTotal > 0 ? (double)ValidationsPassed / ValidationsTotal * 100.0 : 0.0;
   Print("Success Rate: ", DoubleToString(successRate, 1), "%");
   
   if(ValidationsPassed == ValidationsTotal)
   {
      Print("STATUS: ALL VALIDATIONS PASSED ✓");
      Print("System is ready for deployment!");
   }
   else
   {
      Print("STATUS: SOME VALIDATIONS FAILED ✗");
      Print("Please review the errors above before deployment.");
   }
   
   Print("===============================================");
}

//+------------------------------------------------------------------+
//| Validate TimeToStruct functions (main compilation fix)          |
//+------------------------------------------------------------------+
void ValidateTimeStructFunctions()
{
   Print("Validating TimeToStruct functions...");
   ValidationsTotal++;
   
   try
   {
      // Test the fixed TimeToStruct implementation
      datetime currentTime = TimeCurrent();
      MqlDateTime timeStruct1, timeStruct2;
      
      bool result1 = TimeToStruct(currentTime, timeStruct1);
      bool result2 = TimeToStruct(D'2024.07.06 12:00:00', timeStruct2);
      
      if(!result1 || !result2)
      {
         Print("ERROR: TimeToStruct function failed");
         return;
      }
      
      // Test day comparison (this was causing the compilation error)
      if(timeStruct1.day != timeStruct2.day)
      {
         if(InpShowDetailedLog)
            Print("Day comparison working: ", timeStruct1.day, " != ", timeStruct2.day);
      }
      
      ValidationsPassed++;
      Print("✓ TimeToStruct functions validation PASSED");
      
      if(InpShowDetailedLog)
      {
         Print("  Current time structure:");
         Print("    Year: ", timeStruct1.year);
         Print("    Month: ", timeStruct1.mon);
         Print("    Day: ", timeStruct1.day);
         Print("    Hour: ", timeStruct1.hour);
         Print("    Minute: ", timeStruct1.min);
      }
   }
   catch(string error)
   {
      Print("✗ TimeToStruct functions validation FAILED: ", error);
   }
}

//+------------------------------------------------------------------+
//| Validate indicator creation                                     |
//+------------------------------------------------------------------+
void ValidateIndicatorCreation()
{
   Print("Validating indicator creation...");
   ValidationsTotal++;
   
   try
   {
      // Test built-in indicators
      int rsiHandle = iRSI(InpValidationSymbol, PERIOD_M1, 14, PRICE_CLOSE);
      int atrHandle = iATR(InpValidationSymbol, PERIOD_M1, 14);
      
      if(rsiHandle == INVALID_HANDLE)
      {
         Print("ERROR: Failed to create RSI indicator");
         return;
      }
      
      if(atrHandle == INVALID_HANDLE)
      {
         Print("ERROR: Failed to create ATR indicator");
         IndicatorRelease(rsiHandle);
         return;
      }
      
      // Test data retrieval
      double rsiData[], atrData[];
      ArraySetAsSeries(rsiData, true);
      ArraySetAsSeries(atrData, true);
      
      int rsiCopied = CopyBuffer(rsiHandle, 0, 0, 5, rsiData);
      int atrCopied = CopyBuffer(atrHandle, 0, 0, 5, atrData);
      
      // Clean up
      IndicatorRelease(rsiHandle);
      IndicatorRelease(atrHandle);
      
      if(rsiCopied <= 0 || atrCopied <= 0)
      {
         Print("ERROR: Failed to copy indicator data");
         return;
      }
      
      ValidationsPassed++;
      Print("✓ Indicator creation validation PASSED");
      
      if(InpShowDetailedLog)
      {
         Print("  RSI values copied: ", rsiCopied);
         Print("  ATR values copied: ", atrCopied);
         Print("  Current RSI: ", DoubleToString(rsiData[0], 2));
         Print("  Current ATR: ", DoubleToString(atrData[0], _Digits));
      }
   }
   catch(string error)
   {
      Print("✗ Indicator creation validation FAILED: ", error);
   }
}

//+------------------------------------------------------------------+
//| Validate array operations                                       |
//+------------------------------------------------------------------+
void ValidateArrayOperations()
{
   Print("Validating array operations...");
   ValidationsTotal++;
   
   try
   {
      // Test dynamic arrays
      double testArray[];
      ArrayResize(testArray, 50);
      ArraySetAsSeries(testArray, true);
      
      // Fill with test data
      for(int i = 0; i < 50; i++)
      {
         testArray[i] = i * 2.5;
      }
      
      // Test array access
      if(ArraySize(testArray) != 50)
      {
         Print("ERROR: Array size mismatch");
         return;
      }
      
      if(testArray[0] != 0.0 || testArray[49] != 122.5)
      {
         Print("ERROR: Array data mismatch");
         return;
      }
      
      ValidationsPassed++;
      Print("✓ Array operations validation PASSED");
      
      if(InpShowDetailedLog)
      {
         Print("  Array size: ", ArraySize(testArray));
         Print("  First element: ", testArray[0]);
         Print("  Last element: ", testArray[49]);
      }
   }
   catch(string error)
   {
      Print("✗ Array operations validation FAILED: ", error);
   }
}

//+------------------------------------------------------------------+
//| Validate symbol functions                                       |
//+------------------------------------------------------------------+
void ValidateSymbolFunctions()
{
   Print("Validating symbol functions...");
   ValidationsTotal++;
   
   try
   {
      // Test symbol info functions
      double point = SymbolInfoDouble(InpValidationSymbol, SYMBOL_POINT);
      double tickSize = SymbolInfoDouble(InpValidationSymbol, SYMBOL_TRADE_TICK_SIZE);
      double minLot = SymbolInfoDouble(InpValidationSymbol, SYMBOL_VOLUME_MIN);
      double maxLot = SymbolInfoDouble(InpValidationSymbol, SYMBOL_VOLUME_MAX);
      double lotStep = SymbolInfoDouble(InpValidationSymbol, SYMBOL_VOLUME_STEP);
      
      if(point <= 0 || minLot <= 0)
      {
         Print("ERROR: Invalid symbol information for ", InpValidationSymbol);
         Print("This symbol may not be available in your broker");
         return;
      }
      
      ValidationsPassed++;
      Print("✓ Symbol functions validation PASSED");
      
      if(InpShowDetailedLog)
      {
         Print("  Symbol: ", InpValidationSymbol);
         Print("  Point: ", point);
         Print("  Tick Size: ", tickSize);
         Print("  Min Lot: ", minLot);
         Print("  Max Lot: ", maxLot);
         Print("  Lot Step: ", lotStep);
      }
   }
   catch(string error)
   {
      Print("✗ Symbol functions validation FAILED: ", error);
   }
}

//+------------------------------------------------------------------+
//| Validate account functions                                      |
//+------------------------------------------------------------------+
void ValidateAccountFunctions()
{
   Print("Validating account functions...");
   ValidationsTotal++;
   
   try
   {
      // Test account info functions
      double balance = AccountInfoDouble(ACCOUNT_BALANCE);
      double equity = AccountInfoDouble(ACCOUNT_EQUITY);
      double margin = AccountInfoDouble(ACCOUNT_MARGIN);
      double freeMargin = AccountInfoDouble(ACCOUNT_MARGIN_FREE);
      string currency = AccountInfoString(ACCOUNT_CURRENCY);
      string company = AccountInfoString(ACCOUNT_COMPANY);
      
      if(balance < 0 || equity < 0)
      {
         Print("ERROR: Invalid account information");
         return;
      }
      
      ValidationsPassed++;
      Print("✓ Account functions validation PASSED");
      
      if(InpShowDetailedLog)
      {
         Print("  Balance: ", balance, " ", currency);
         Print("  Equity: ", equity, " ", currency);
         Print("  Margin: ", margin, " ", currency);
         Print("  Free Margin: ", freeMargin, " ", currency);
         Print("  Company: ", company);
      }
   }
   catch(string error)
   {
      Print("✗ Account functions validation FAILED: ", error);
   }
}

//+------------------------------------------------------------------+
//| Validate custom indicator loading                               |
//+------------------------------------------------------------------+
void ValidateCustomIndicatorLoading()
{
   Print("Validating custom indicator loading...");
   ValidationsTotal++;
   
   try
   {
      // Try to load the custom indicator
      int indicatorHandle = iCustom(InpValidationSymbol, PERIOD_M1, "AMPD_Jump_Crash_Indicator");
      
      if(indicatorHandle == INVALID_HANDLE)
      {
         Print("WARNING: Custom indicator not found or not compiled");
         Print("Please ensure AMPD_Jump_Crash_Indicator.mq5 is compiled");
         return;
      }
      
      // Try to get data from the indicator
      double jumpSignals[], crashSignals[], exitSignals[];
      ArraySetAsSeries(jumpSignals, true);
      ArraySetAsSeries(crashSignals, true);
      ArraySetAsSeries(exitSignals, true);
      
      int jumpCopied = CopyBuffer(indicatorHandle, 0, 0, 10, jumpSignals);
      int crashCopied = CopyBuffer(indicatorHandle, 1, 0, 10, crashSignals);
      int exitCopied = CopyBuffer(indicatorHandle, 2, 0, 10, exitSignals);
      
      IndicatorRelease(indicatorHandle);
      
      if(jumpCopied <= 0 || crashCopied <= 0 || exitCopied <= 0)
      {
         Print("ERROR: Failed to copy custom indicator data");
         return;
      }
      
      ValidationsPassed++;
      Print("✓ Custom indicator loading validation PASSED");
      
      if(InpShowDetailedLog)
      {
         Print("  Jump signals copied: ", jumpCopied);
         Print("  Crash signals copied: ", crashCopied);
         Print("  Exit signals copied: ", exitCopied);
      }
   }
   catch(string error)
   {
      Print("✗ Custom indicator loading validation FAILED: ", error);
   }
}

//+------------------------------------------------------------------+
//| Validate trading environment                                    |
//+------------------------------------------------------------------+
void ValidateTradingEnvironment()
{
   Print("Validating trading environment...");
   ValidationsTotal++;
   
   try
   {
      // Check if automated trading is allowed
      bool autoTradingAllowed = TerminalInfoInteger(TERMINAL_TRADE_ALLOWED);
      bool expertAllowed = MQLInfoInteger(MQL_TRADE_ALLOWED);
      
      if(!autoTradingAllowed)
      {
         Print("WARNING: Automated trading is not allowed in terminal");
      }
      
      if(!expertAllowed)
      {
         Print("WARNING: Expert Advisor trading is not allowed");
      }
      
      // Check market status
      bool marketOpen = SymbolInfoInteger(InpValidationSymbol, SYMBOL_TRADE_MODE) != SYMBOL_TRADE_MODE_DISABLED;
      
      ValidationsPassed++;
      Print("✓ Trading environment validation PASSED");
      
      if(InpShowDetailedLog)
      {
         Print("  Auto trading allowed: ", autoTradingAllowed ? "YES" : "NO");
         Print("  Expert trading allowed: ", expertAllowed ? "YES" : "NO");
         Print("  Market open: ", marketOpen ? "YES" : "NO");
      }
   }
   catch(string error)
   {
      Print("✗ Trading environment validation FAILED: ", error);
   }
}

//+------------------------------------------------------------------+
//| Validate risk calculations                                      |
//+------------------------------------------------------------------+
void ValidateRiskCalculations()
{
   Print("Validating risk calculations...");
   ValidationsTotal++;
   
   try
   {
      // Test risk calculation functions
      double balance = AccountInfoDouble(ACCOUNT_BALANCE);
      double riskPercent = 2.0;
      double stopLossPoints = 25.0;
      
      double riskAmount = balance * riskPercent / 100.0;
      double point = SymbolInfoDouble(InpValidationSymbol, SYMBOL_POINT);
      double tickValue = SymbolInfoDouble(InpValidationSymbol, SYMBOL_TRADE_TICK_VALUE);
      
      if(point <= 0 || tickValue <= 0)
      {
         Print("ERROR: Invalid symbol data for risk calculation");
         return;
      }
      
      double stopLossValue = stopLossPoints * point;
      double lotSize = riskAmount / (stopLossValue * tickValue / point);
      
      // Normalize lot size
      double minLot = SymbolInfoDouble(InpValidationSymbol, SYMBOL_VOLUME_MIN);
      double maxLot = SymbolInfoDouble(InpValidationSymbol, SYMBOL_VOLUME_MAX);
      double lotStep = SymbolInfoDouble(InpValidationSymbol, SYMBOL_VOLUME_STEP);
      
      lotSize = MathMax(minLot, MathMin(maxLot, lotSize));
      lotSize = NormalizeDouble(lotSize / lotStep, 0) * lotStep;
      
      ValidationsPassed++;
      Print("✓ Risk calculations validation PASSED");
      
      if(InpShowDetailedLog)
      {
         Print("  Account Balance: ", balance);
         Print("  Risk Amount (2%): ", riskAmount);
         Print("  Calculated Lot Size: ", lotSize);
         Print("  Min/Max Lot: ", minLot, " / ", maxLot);
      }
   }
   catch(string error)
   {
      Print("✗ Risk calculations validation FAILED: ", error);
   }
}
