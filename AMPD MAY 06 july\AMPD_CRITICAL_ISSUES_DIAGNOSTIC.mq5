//+------------------------------------------------------------------+
//|                           AMPD Critical Issues Diagnostic.mq5   |
//|                      Copyright 2025, <PERSON><PERSON> |
//|                                             https://www.ampd.com |
//+------------------------------------------------------------------+
#property copyright "Arise Moroka Prince Dynasty"
#property link      "https://ampd.com"
#property description "Comprehensive diagnostic tool for AMPD system issues"
#property version   "1.00"
#property script_show_inputs

//--- Input parameters
input string InpDiagnosticInfo = "=== AMPD SYSTEM DIAGNOSTIC ==="; // Diagnostic Information

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
   Print("================================================================");
   Print("🔍 AMPD CRITICAL ISSUES DIAGNOSTIC - COMPREHENSIVE ANALYSIS");
   Print("================================================================");
   Print("");
   
   //--- Test 1: Indicator Connection
   TestIndicatorConnection();
   
   //--- Test 2: Visual Elements Verification
   TestVisualElements();
   
   //--- Test 3: EA Activation Check
   TestEAActivation();
   
   //--- Test 4: Signal Detection Test
   TestSignalDetection();
   
   //--- Test 5: Trading Permissions
   TestTradingPermissions();
   
   //--- Display troubleshooting guide
   DisplayTroubleshootingGuide();
   
   Print("================================================================");
   Print("🎯 DIAGNOSTIC COMPLETE - FOLLOW TROUBLESHOOTING STEPS ABOVE");
   Print("================================================================");
}

//+------------------------------------------------------------------+
//| Test indicator connection and compilation                        |
//+------------------------------------------------------------------+
void TestIndicatorConnection()
{
   Print("🔍 TEST 1: INDICATOR CONNECTION & COMPILATION");
   Print("─────────────────────────────────────────────");
   
   string indicatorName = "AMPD_RealTime_Precision_Indicator";
   int handle = iCustom(_Symbol, PERIOD_M1, indicatorName);
   
   if(handle != INVALID_HANDLE)
   {
      Print("✅ Indicator Connection: SUCCESS");
      Print("   → Indicator Name: ", indicatorName);
      Print("   → Handle: ", handle);
      
      //--- Test buffer access
      double testBuffer[];
      if(CopyBuffer(handle, 0, 0, 1, testBuffer) == 1)
      {
         Print("✅ Buffer Access: SUCCESS");
      }
      else
      {
         Print("❌ Buffer Access: FAILED");
         Print("   → Cannot read indicator buffers");
      }
      
      IndicatorRelease(handle);
   }
   else
   {
      Print("❌ Indicator Connection: FAILED");
      Print("   → Indicator not found or not compiled");
      Print("   → Expected: ", indicatorName, ".ex5");
   }
   Print("");
}

//+------------------------------------------------------------------+
//| Test visual elements and colors                                 |
//+------------------------------------------------------------------+
void TestVisualElements()
{
   Print("🎨 TEST 2: VISUAL ELEMENTS VERIFICATION");
   Print("─────────────────────────────────────────");
   
   Print("Expected Visual Elements:");
   Print("✅ GOLD arrows (↑) below candles - BUY signals");
   Print("✅ LIME arrows (↓) above candles - SELL signals");
   Print("✅ WHITE dots (•) - Entry confirmation");
   Print("✅ YELLOW dots (•) - Exit signals");
   Print("");
   
   Print("Color Specifications:");
   Print("   → GOLD arrows: clrGold (#FFD700)");
   Print("   → LIME arrows: clrLime (#00FF00)");
   Print("   → WHITE dots: clrWhite (#FFFFFF)");
   Print("   → YELLOW dots: clrYellow (#FFFF00)");
   Print("");
   
   Print("If colors are wrong:");
   Print("1. Remove indicator from chart completely");
   Print("2. Recompile AMPD_RealTime_Precision_Indicator.mq5");
   Print("3. Restart MetaTrader 5");
   Print("4. Add fresh indicator to chart");
   Print("");
}

//+------------------------------------------------------------------+
//| Test EA activation and smiley face                              |
//+------------------------------------------------------------------+
void TestEAActivation()
{
   Print("😊 TEST 3: EA ACTIVATION CHECK");
   Print("─────────────────────────────────");
   
   Print("EA Smiley Face Troubleshooting:");
   Print("");
   
   if(!TerminalInfoInteger(TERMINAL_TRADE_ALLOWED))
   {
      Print("❌ Terminal Trading: DISABLED");
      Print("   → Enable 'Allow automated trading' in Tools → Options → Expert Advisors");
   }
   else
   {
      Print("✅ Terminal Trading: ENABLED");
   }
   
   if(!MQLInfoInteger(MQL_TRADE_ALLOWED))
   {
      Print("❌ EA Trading Permission: DISABLED");
      Print("   → Check EA properties → Allow live trading");
   }
   else
   {
      Print("✅ EA Trading Permission: ENABLED");
   }
   
   Print("");
   Print("Steps to fix missing smiley face:");
   Print("1. Remove EA from chart");
   Print("2. Recompile AMPD_HighFrequency_AutoTrader_EA.mq5");
   Print("3. Attach EA to chart again");
   Print("4. Enable 'Allow live trading' and 'Allow DLL imports'");
   Print("5. Look for initialization messages in Experts tab");
   Print("");
}

//+------------------------------------------------------------------+
//| Test signal detection logic                                     |
//+------------------------------------------------------------------+
void TestSignalDetection()
{
   Print("📡 TEST 4: SIGNAL DETECTION TEST");
   Print("─────────────────────────────────");
   
   string indicatorName = "AMPD_RealTime_Precision_Indicator";
   int handle = iCustom(_Symbol, PERIOD_M1, indicatorName);
   
   if(handle != INVALID_HANDLE)
   {
      double goldBuffer[], limeBuffer[], entryBuffer[], exitBuffer[];
      
      bool goldOK = (CopyBuffer(handle, 0, 0, 3, goldBuffer) == 3);
      bool limeOK = (CopyBuffer(handle, 1, 0, 3, limeBuffer) == 3);
      bool entryOK = (CopyBuffer(handle, 2, 0, 3, entryBuffer) == 3);
      bool exitOK = (CopyBuffer(handle, 3, 0, 3, exitBuffer) == 3);
      
      Print("Buffer Access Test:");
      Print("   → GOLD arrows (Buffer 0): ", goldOK ? "✅ SUCCESS" : "❌ FAILED");
      Print("   → LIME arrows (Buffer 1): ", limeOK ? "✅ SUCCESS" : "❌ FAILED");
      Print("   → Entry dots (Buffer 2): ", entryOK ? "✅ SUCCESS" : "❌ FAILED");
      Print("   → Exit dots (Buffer 3): ", exitOK ? "✅ SUCCESS" : "❌ FAILED");
      
      if(goldOK && limeOK && entryOK && exitOK)
      {
         Print("");
         Print("Current Signal Status:");
         Print("   → GOLD arrow present: ", (goldBuffer[0] != 0 && goldBuffer[0] != EMPTY_VALUE));
         Print("   → LIME arrow present: ", (limeBuffer[0] != 0 && limeBuffer[0] != EMPTY_VALUE));
         Print("   → Entry dot present: ", (entryBuffer[0] != 0 && entryBuffer[0] != EMPTY_VALUE));
         Print("   → Exit dot present: ", (exitBuffer[0] != 0 && exitBuffer[0] != EMPTY_VALUE));
      }
      
      IndicatorRelease(handle);
   }
   else
   {
      Print("❌ Cannot test signals - Indicator not accessible");
   }
   Print("");
}

//+------------------------------------------------------------------+
//| Test trading permissions and account settings                   |
//+------------------------------------------------------------------+
void TestTradingPermissions()
{
   Print("🔐 TEST 5: TRADING PERMISSIONS");
   Print("─────────────────────────────────");
   
   Print("Account Information:");
   Print("   → Account Number: ", AccountInfoInteger(ACCOUNT_LOGIN));
   Print("   → Account Balance: ", AccountInfoDouble(ACCOUNT_BALANCE));
   Print("   → Free Margin: ", AccountInfoDouble(ACCOUNT_MARGIN_FREE));
   Print("   → Trade Allowed: ", AccountInfoInteger(ACCOUNT_TRADE_ALLOWED) ? "✅ YES" : "❌ NO");
   Print("   → Trade Expert: ", AccountInfoInteger(ACCOUNT_TRADE_EXPERT) ? "✅ YES" : "❌ NO");
   
   Print("");
   Print("Symbol Information:");
   Print("   → Symbol: ", _Symbol);
   Print("   → Spread: ", SymbolInfoInteger(_Symbol, SYMBOL_SPREAD), " points");
   Print("   → Trade Mode: ", SymbolInfoInteger(_Symbol, SYMBOL_TRADE_MODE));
   Print("   → Min Lot: ", SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN));
   Print("   → Max Lot: ", SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX));
   Print("");
}

//+------------------------------------------------------------------+
//| Display comprehensive troubleshooting guide                     |
//+------------------------------------------------------------------+
void DisplayTroubleshootingGuide()
{
   Print("🛠️ COMPREHENSIVE TROUBLESHOOTING GUIDE");
   Print("════════════════════════════════════════");
   Print("");
   
   Print("🚨 ISSUE 1: EA SMILEY FACE NOT APPEARING");
   Print("SOLUTION:");
   Print("1. Tools → Options → Expert Advisors → Enable 'Allow automated trading'");
   Print("2. Remove EA from chart");
   Print("3. Recompile AMPD_HighFrequency_AutoTrader_EA.mq5 (F7)");
   Print("4. Attach EA to chart");
   Print("5. Enable 'Allow live trading' and 'Allow DLL imports'");
   Print("6. Check Experts tab for '🎯 AMPD HIGH-FREQUENCY AUTOTRADER EA - INITIALIZATION SUCCESS'");
   Print("");
   
   Print("🚨 ISSUE 2: WRONG ARROW/DOT COLORS");
   Print("SOLUTION:");
   Print("1. Remove ALL indicators from chart");
   Print("2. Recompile AMPD_RealTime_Precision_Indicator.mq5 (F7)");
   Print("3. Restart MetaTrader 5 completely");
   Print("4. Add fresh indicator to chart");
   Print("5. Verify colors: GOLD arrows, LIME arrows, WHITE/YELLOW dots");
   Print("");
   
   Print("🚨 ISSUE 3: ONLY BUY ORDERS, NO SELL ORDERS");
   Print("SOLUTION:");
   Print("1. Check Experts tab for '🔍 SIGNAL DEBUG' messages");
   Print("2. Verify LIME arrows are appearing on chart");
   Print("3. Ensure Entry dots appear with LIME arrows");
   Print("4. Check account allows SELL orders for this symbol");
   Print("5. Verify no position limits preventing SELL trades");
   Print("");
   
   Print("🚨 ISSUE 4: TRADES NOT AUTO-EXITING");
   Print("SOLUTION:");
   Print("1. Verify 'Signal-Based Exit Only' is enabled in EA settings");
   Print("2. Check for YELLOW exit dots appearing on chart");
   Print("3. Look for '🟡 EXIT SIGNAL DETECTED' messages in Experts tab");
   Print("4. Emergency exit after 100 bars is active as backup");
   Print("5. Ensure EA is not paused or disabled");
   Print("");
   
   Print("🚨 ISSUE 5: SYSTEM GETTING STUCK ON SINGLE POSITION");
   Print("SOLUTION:");
   Print("1. Check 'Max Positions' setting (default: 3)");
   Print("2. Verify exit signals are being generated");
   Print("3. Enable 'Close Opposite Positions' if needed");
   Print("4. Monitor emergency exit after max bars");
   Print("5. Check for sufficient free margin");
   Print("");
}
//+------------------------------------------------------------------+
