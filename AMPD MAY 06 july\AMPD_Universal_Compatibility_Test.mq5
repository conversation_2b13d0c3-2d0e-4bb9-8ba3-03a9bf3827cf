//+------------------------------------------------------------------+
//|                        AMPD Universal Compatibility Test.mq5   |
//|                        Copyright 2025, <PERSON><PERSON> |
//|                                             https://www.ampd.com |
//+------------------------------------------------------------------+
#property copyright "Arise Moroka Prince Dynasty"
#property link      "https://ampd.com"
#property description "Universal Compatibility Test for AMPD High-Frequency AutoTrader EA"
#property version   "1.0"
#property script_show_inputs

//--- Input parameters
input bool InpTestLotSizeCalculations = true;    // Test Lot Size Calculations
input bool InpTestSpreadCalculations = true;     // Test Spread Calculations
input bool InpTestInstrumentDetection = true;    // Test Instrument Detection
input bool InpShowDetailedInfo = true;           // Show Detailed Information

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
   Print("================================================================");
   Print("🧪 AMPD UNIVERSAL COMPATIBILITY TEST - STARTING");
   Print("================================================================");
   Print("📊 TESTING INSTRUMENT: ", _Symbol);
   
   bool allTestsPassed = true;
   
   //--- Test 1: Basic Symbol Information
   Print("📋 TEST 1: Basic Symbol Information");
   if(TestBasicSymbolInfo())
   {
      Print("✅ TEST 1 PASSED: Basic symbol information accessible");
   }
   else
   {
      Print("❌ TEST 1 FAILED: Cannot access basic symbol information");
      allTestsPassed = false;
   }
   
   //--- Test 2: Lot Size Calculations
   if(InpTestLotSizeCalculations)
   {
      Print("📋 TEST 2: Lot Size Calculations");
      if(TestLotSizeCalculations())
      {
         Print("✅ TEST 2 PASSED: Lot size calculations working");
      }
      else
      {
         Print("❌ TEST 2 FAILED: Lot size calculation issues");
         allTestsPassed = false;
      }
   }
   
   //--- Test 3: Spread Calculations
   if(InpTestSpreadCalculations)
   {
      Print("📋 TEST 3: Spread Calculations");
      if(TestSpreadCalculations())
      {
         Print("✅ TEST 3 PASSED: Spread calculations working");
      }
      else
      {
         Print("❌ TEST 3 FAILED: Spread calculation issues");
         allTestsPassed = false;
      }
   }
   
   //--- Test 4: Instrument Detection
   if(InpTestInstrumentDetection)
   {
      Print("📋 TEST 4: Instrument Type Detection");
      if(TestInstrumentDetection())
      {
         Print("✅ TEST 4 PASSED: Instrument detection working");
      }
      else
      {
         Print("❌ TEST 4 FAILED: Instrument detection issues");
         allTestsPassed = false;
      }
   }
   
   //--- Final Result
   Print("================================================================");
   if(allTestsPassed)
   {
      Print("🎉 ALL TESTS PASSED - EA SHOULD LOAD SUCCESSFULLY");
      Print("✅ Symbol: Compatible");
      Print("✅ Lot Sizes: Valid");
      Print("✅ Spreads: Calculable");
      Print("✅ Detection: Working");
      Print("🚀 READY TO ATTACH AMPD UNIVERSAL EA TO THIS CHART");
   }
   else
   {
      Print("⚠️ SOME TESTS FAILED - EA MAY HAVE ISSUES");
      Print("Please review the failed tests above");
   }
   Print("================================================================");
}

//+------------------------------------------------------------------+
//| Test basic symbol information access                             |
//+------------------------------------------------------------------+
bool TestBasicSymbolInfo()
{
   double minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
   double maxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
   double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
   double pointValue = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
   double tickSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
   
   if(InpShowDetailedInfo)
   {
      Print("   📊 Min Lot: ", minLot);
      Print("   📊 Max Lot: ", maxLot);
      Print("   📊 Lot Step: ", lotStep);
      Print("   📊 Point Value: ", pointValue);
      Print("   📊 Tick Size: ", tickSize);
   }
   
   // Validate critical values
   if(minLot <= 0 || maxLot <= 0 || lotStep <= 0)
   {
      Print("   ❌ Invalid lot parameters");
      return false;
   }
   
   if(pointValue <= 0 || tickSize <= 0)
   {
      Print("   ❌ Invalid point/tick parameters");
      return false;
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| Test lot size calculations                                       |
//+------------------------------------------------------------------+
bool TestLotSizeCalculations()
{
   double minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
   double maxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
   double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
   
   // Test various lot sizes
   double testLots[] = {0.01, 0.1, 1.0, 10.0};
   
   for(int i = 0; i < ArraySize(testLots); i++)
   {
      double testLot = testLots[i];
      double normalizedLot = NormalizeLotSize(testLot, minLot, maxLot, lotStep);
      
      if(InpShowDetailedInfo)
      {
         Print("   📊 Test Lot ", testLot, " → Normalized: ", normalizedLot);
      }
      
      // Validate normalized lot
      if(normalizedLot < minLot || normalizedLot > maxLot)
      {
         Print("   ❌ Normalized lot out of range: ", normalizedLot);
         return false;
      }
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| Test spread calculations                                         |
//+------------------------------------------------------------------+
bool TestSpreadCalculations()
{
   MqlTick tick;
   if(!SymbolInfoTick(_Symbol, tick))
   {
      Print("   ❌ Cannot get tick data");
      return false;
   }
   
   double pointValue = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
   double spread = (tick.ask - tick.bid) / pointValue;
   
   if(InpShowDetailedInfo)
   {
      Print("   📊 Ask: ", tick.ask);
      Print("   📊 Bid: ", tick.bid);
      Print("   📊 Spread: ", DoubleToString(spread, 1), " points");
   }
   
   if(spread < 0)
   {
      Print("   ❌ Invalid spread calculation");
      return false;
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| Test instrument type detection                                   |
//+------------------------------------------------------------------+
bool TestInstrumentDetection()
{
   string symbolName = _Symbol;
   string instrumentType = "UNKNOWN";
   
   // Auto-detect instrument type
   if(StringFind(symbolName, "USD") >= 0 || StringFind(symbolName, "EUR") >= 0 || 
      StringFind(symbolName, "GBP") >= 0 || StringFind(symbolName, "JPY") >= 0)
   {
      instrumentType = "FOREX";
   }
   else if(StringFind(symbolName, "BTC") >= 0 || StringFind(symbolName, "ETH") >= 0)
   {
      instrumentType = "CRYPTO";
   }
   else if(StringFind(symbolName, "XAU") >= 0 || StringFind(symbolName, "XAG") >= 0)
   {
      instrumentType = "COMMODITY";
   }
   else if(StringFind(symbolName, "US30") >= 0 || StringFind(symbolName, "NAS100") >= 0)
   {
      instrumentType = "INDEX";
   }
   else if(StringFind(symbolName, "Jump") >= 0 || StringFind(symbolName, "Crash") >= 0 ||
           StringFind(symbolName, "Step") >= 0 || StringFind(symbolName, "Boom") >= 0)
   {
      instrumentType = "SYNTHETIC";
   }
   else
   {
      instrumentType = "OTHER";
   }
   
   Print("   🌍 Detected Type: ", instrumentType);
   
   return true; // Detection always succeeds, just categorizes
}

//+------------------------------------------------------------------+
//| Normalize lot size helper function                               |
//+------------------------------------------------------------------+
double NormalizeLotSize(double lotSize, double minLot, double maxLot, double lotStep)
{
   // Ensure lot size is within broker limits
   lotSize = MathMax(minLot, MathMin(maxLot, lotSize));
   
   // Round to nearest lot step
   lotSize = MathRound(lotSize / lotStep) * lotStep;
   
   // Final validation
   if(lotSize < minLot) lotSize = minLot;
   if(lotSize > maxLot) lotSize = maxLot;
   
   return lotSize;
}
