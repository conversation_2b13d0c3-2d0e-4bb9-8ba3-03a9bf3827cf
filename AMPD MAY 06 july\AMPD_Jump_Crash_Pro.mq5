//+------------------------------------------------------------------+
//|                                    AMPD Jump Crash Pro.mq5      |
//|                        Copyright 2024, <PERSON><PERSON> |
//|                                             https://www.ampd.com |
//+------------------------------------------------------------------+
#property copyright "Arise Moroka Prince Dynasty"
#property link      "https://ampd.com"
#property description "Professional Jump 75 & Crash 1000 Trading Indicator"
#property version   "2.0"

#property indicator_chart_window
#property indicator_buffers 4
#property indicator_plots   4

//--- Plot definitions using proven working structure
#property indicator_label1  "Jump75_BUY"
#property indicator_type1   DRAW_ARROW
#property indicator_color1  clrLime
#property indicator_width1  3

#property indicator_label2  "Crash1000_SELL"
#property indicator_type2   DRAW_ARROW
#property indicator_color2  clrRed
#property indicator_width2  3

#property indicator_label3  "EXIT_Signal"
#property indicator_type3   DRAW_ARROW
#property indicator_color3  clrGold
#property indicator_width3  2

#property indicator_label4  "Trend_Reference"
#property indicator_type4   DRAW_LINE
#property indicator_color4  clrDodgerBlue
#property indicator_width4  1

//--- Strategy Selection
enum STRATEGY_TYPE
{
   STRATEGY_JUMP75,     // Jump 75 Index Strategy
   STRATEGY_CRASH1000,  // Crash 1000 Index Strategy
   STRATEGY_BOTH        // Both Strategies
};

//--- Signal Types
enum ENUM_SIGNAL
{
   SIGNAL_NONE,
   SIGNAL_JUMP_BUY,
   SIGNAL_CRASH_SELL,
   SIGNAL_EXIT
};

//--- Input parameters
input group "=== STRATEGY SELECTION ==="
input STRATEGY_TYPE InpStrategy = STRATEGY_BOTH;   // Trading Strategy

input group "=== JUMP 75 SETTINGS ==="
input double InpJumpVolatilityThreshold = 2.0;     // Jump Volatility Threshold
input int    InpJumpMomentumPeriod = 3;            // Jump Momentum Period
input double InpJumpCandleSize = 1.5;              // Jump Minimum Candle Size

input group "=== CRASH 1000 SETTINGS ==="
input double InpCrashVolatilityThreshold = 2.5;    // Crash Volatility Threshold
input int    InpCrashRSIPeriod = 14;               // Crash RSI Period
input double InpCrashRSILevel = 65.0;              // Crash RSI Overbought Level
input double InpCrashCandleSize = 2.0;             // Crash Minimum Candle Size

input group "=== GENERAL SETTINGS ==="
input int    InpATRPeriod = 14;                    // ATR Period
input bool   InpShowAlerts = true;                 // Show Alerts
input bool   InpSoundAlerts = true;                // Sound Alerts
input bool   InpEmailAlerts = false;               // Email Alerts
input bool   InpPushAlerts = false;                // Push Notifications
input bool   InpShowInfoPanel = true;              // Show Info Panel

//--- Global variables
double JumpBuyBuffer[];
double CrashSellBuffer[];
double ExitBuffer[];
double TrendBuffer[];

int atrHandle;
int rsiHandle;

long chartID = ChartID();
#define LabelBox "AMPD_BOX"
#define Label1 "AMPD_L1"
#define Label2 "AMPD_L2"
#define Label3 "AMPD_L3"
#define Label4 "AMPD_L4"

string label1 = "JUMP 75: ",
       label2 = "CRASH 1000: ",
       label3 = "SIGNALS: ",
       label4 = "STATUS: ";

int doubleToPip;
double pipToDouble;

MqlTick tick;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
   IndicatorSetString(INDICATOR_SHORTNAME, "AMPD Jump Crash Pro");
   
   //--- Set points & digits (from reference file)
   IndicatorSetInteger(INDICATOR_DIGITS, _Digits);   
   if(_Digits == 2 || _Digits == 3)  
      doubleToPip = 100;
   else                          
      doubleToPip = 10000;
   
   if(_Digits == 2 || _Digits == 4) 
      pipToDouble = _Point;
   else                       
      pipToDouble = _Point * 10;

   //--- Create indicator handles
   atrHandle = iATR(_Symbol, PERIOD_CURRENT, InpATRPeriod);
   rsiHandle = iRSI(_Symbol, PERIOD_CURRENT, InpCrashRSIPeriod, PRICE_CLOSE);
   
   if(atrHandle == INVALID_HANDLE || rsiHandle == INVALID_HANDLE)
   {
      Print("Error creating indicator handles");
      return INIT_FAILED;
   }

   //--- Create info panel (based on reference structure)
   if(InpShowInfoPanel)
      CreateInfoPanel();

   //--- Indicator buffers mapping (using proven structure)
   SetIndexBuffer(0, JumpBuyBuffer, INDICATOR_DATA);
   ArraySetAsSeries(JumpBuyBuffer, true);
   PlotIndexSetString(0, PLOT_LABEL, "Jump75_BUY");   
   PlotIndexSetInteger(0, PLOT_ARROW, 233);  // Up arrow
   PlotIndexSetDouble(0, PLOT_EMPTY_VALUE, 0);
   
   SetIndexBuffer(1, CrashSellBuffer, INDICATOR_DATA);
   ArraySetAsSeries(CrashSellBuffer, true);
   PlotIndexSetString(1, PLOT_LABEL, "Crash1000_SELL");   
   PlotIndexSetInteger(1, PLOT_ARROW, 234);  // Down arrow
   PlotIndexSetDouble(1, PLOT_EMPTY_VALUE, 0);
   
   SetIndexBuffer(2, ExitBuffer, INDICATOR_DATA);
   ArraySetAsSeries(ExitBuffer, true);
   PlotIndexSetString(2, PLOT_LABEL, "EXIT_Signal");   
   PlotIndexSetInteger(2, PLOT_ARROW, 159);  // Circle
   PlotIndexSetDouble(2, PLOT_EMPTY_VALUE, 0);
   
   SetIndexBuffer(3, TrendBuffer, INDICATOR_DATA);
   ArraySetAsSeries(TrendBuffer, true);
   PlotIndexSetString(3, PLOT_LABEL, "Trend_Reference");   
   PlotIndexSetDouble(3, PLOT_EMPTY_VALUE, 0);

   Print("AMPD Jump Crash Pro Indicator initialized successfully");
   Print("Strategy: ", EnumToString(InpStrategy));
   Print("Alerts: ", InpShowAlerts ? "Enabled" : "Disabled");
   
   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
   if(rates_total < 100)  
      return 0;

   //--- Set arrays as series (from reference structure)
   ArraySetAsSeries(time, true);
   ArraySetAsSeries(open, true);
   ArraySetAsSeries(high, true);
   ArraySetAsSeries(low, true);
   ArraySetAsSeries(close, true);

   //--- Update info panel
   if(InpShowInfoPanel)
      UpdateInfoPanel();

   int limit;
   //--- Calculate limit (from reference structure)
   limit = rates_total - prev_calculated;
   if(prev_calculated == 0)
   { 
      limit = (int)ChartGetInteger(chartID, CHART_VISIBLE_BARS) + 100;
      PlotIndexSetInteger(0, PLOT_DRAW_BEGIN, rates_total - limit);
      PlotIndexSetInteger(1, PLOT_DRAW_BEGIN, rates_total - limit);
      PlotIndexSetInteger(2, PLOT_DRAW_BEGIN, rates_total - limit);
      PlotIndexSetInteger(3, PLOT_DRAW_BEGIN, rates_total - limit);
   }

   //--- Main calculation loop
   for(int i = limit; i >= 0 && !IsStopped(); i--)
   {
      // Initialize buffers
      JumpBuyBuffer[i] = 0;
      CrashSellBuffer[i] = 0;
      ExitBuffer[i] = 0;
      TrendBuffer[i] = 0;
      
      // Get indicator values
      double atr = GetATR(i);
      double rsi = GetRSI(i);
      
      if(atr <= 0) continue;
      
      // Set trend reference line
      TrendBuffer[i] = close[i];
      
      // Analyze signals
      ENUM_SIGNAL signal = AnalyzeSignals(i, time, open, high, low, close, atr, rsi);
      
      // Plot signals based on analysis
      switch(signal)
      {
         case SIGNAL_JUMP_BUY:
            if(InpStrategy == STRATEGY_JUMP75 || InpStrategy == STRATEGY_BOTH)
            {
               JumpBuyBuffer[i] = low[i] - atr;
               HandleAlerts(time[i], SIGNAL_JUMP_BUY, close[i]);
            }
            break;
            
         case SIGNAL_CRASH_SELL:
            if(InpStrategy == STRATEGY_CRASH1000 || InpStrategy == STRATEGY_BOTH)
            {
               CrashSellBuffer[i] = high[i] + atr;
               HandleAlerts(time[i], SIGNAL_CRASH_SELL, close[i]);
            }
            break;
            
         case SIGNAL_EXIT:
            ExitBuffer[i] = close[i];
            HandleAlerts(time[i], SIGNAL_EXIT, close[i]);
            break;
      }
   }

   return rates_total;
}

//+------------------------------------------------------------------+
//| Custom indicator deinitialization function                      |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   //--- Delete info panel objects (from reference structure)
   if(ObjectFind(0, LabelBox) != -1)
      ObjectDelete(0, LabelBox);
   if(ObjectFind(0, Label1) != -1)
      ObjectDelete(0, Label1);
   if(ObjectFind(0, Label2) != -1)
      ObjectDelete(0, Label2);
   if(ObjectFind(0, Label3) != -1)
      ObjectDelete(0, Label3);
   if(ObjectFind(0, Label4) != -1)
      ObjectDelete(0, Label4);
   
   //--- Release indicator handles
   IndicatorRelease(atrHandle);
   IndicatorRelease(rsiHandle);
   
   Print("AMPD Jump Crash Pro Indicator deinitialized");
}

//+------------------------------------------------------------------+
//| Get ATR value                                                    |
//+------------------------------------------------------------------+
double GetATR(int index)
{
   static double atr[1];
   if(CopyBuffer(atrHandle, 0, index, 1, atr) != 1)
      return 0.0;
   return atr[0];
}

//+------------------------------------------------------------------+
//| Get RSI value                                                    |
//+------------------------------------------------------------------+
double GetRSI(int index)
{
   static double rsi[1];
   if(CopyBuffer(rsiHandle, 0, index, 1, rsi) != 1)
      return 50.0;
   return rsi[0];
}

//+------------------------------------------------------------------+
//| Analyze signals for Jump 75 and Crash 1000                     |
//+------------------------------------------------------------------+
ENUM_SIGNAL AnalyzeSignals(int index,
                          const datetime &time[],
                          const double &open[],
                          const double &high[],
                          const double &low[],
                          const double &close[],
                          double atr,
                          double rsi)
{
   // Calculate candle properties
   double candleSize = MathAbs(close[index] - open[index]);
   double candleRange = high[index] - low[index];

   if(candleRange <= 0 || atr <= 0) return SIGNAL_NONE;

   // Jump 75 Analysis (Momentum Burst Detection)
   if(InpStrategy == STRATEGY_JUMP75 || InpStrategy == STRATEGY_BOTH)
   {
      if(IsJumpSignal(index, candleSize, atr, close, open, high, low))
         return SIGNAL_JUMP_BUY;
   }

   // Crash 1000 Analysis (Spike Hunter Detection)
   if(InpStrategy == STRATEGY_CRASH1000 || InpStrategy == STRATEGY_BOTH)
   {
      if(IsCrashSignal(index, candleSize, atr, rsi, close, open, high, low))
         return SIGNAL_CRASH_SELL;
   }

   // Exit Signal Analysis
   if(IsExitSignal(index, close, open))
      return SIGNAL_EXIT;

   return SIGNAL_NONE;
}

//+------------------------------------------------------------------+
//| Check for Jump 75 signal (Momentum Burst Entry)                |
//+------------------------------------------------------------------+
bool IsJumpSignal(int index, double candleSize, double atr,
                  const double &close[], const double &open[],
                  const double &high[], const double &low[])
{
   // Must be bullish candle
   if(close[index] <= open[index]) return false;

   // Check candle size against ATR threshold
   if(candleSize < atr * InpJumpCandleSize) return false;

   // Check volatility expansion
   if(candleSize < atr * InpJumpVolatilityThreshold) return false;

   // Momentum confirmation
   bool momentumConfirmed = true;
   for(int i = 1; i <= InpJumpMomentumPeriod && (index + i) < ArraySize(close); i++)
   {
      if(close[index] <= close[index + i])
      {
         momentumConfirmed = false;
         break;
      }
   }

   return momentumConfirmed;
}

//+------------------------------------------------------------------+
//| Check for Crash 1000 signal (Spike Hunter Sell)               |
//+------------------------------------------------------------------+
bool IsCrashSignal(int index, double candleSize, double atr, double rsi,
                   const double &close[], const double &open[],
                   const double &high[], const double &low[])
{
   // Must be bearish candle
   if(close[index] >= open[index]) return false;

   // Check RSI overbought condition
   if(rsi < InpCrashRSILevel) return false;

   // Check candle size against ATR threshold
   if(candleSize < atr * InpCrashCandleSize) return false;

   // Check volatility expansion (crash detection)
   if(candleSize < atr * InpCrashVolatilityThreshold) return false;

   // Crash pattern validation
   double bodySize = MathAbs(close[index] - open[index]);
   double shadowSize = high[index] - MathMax(close[index], open[index]);

   // Crash candles typically have large bodies with small upper shadows
   if(shadowSize > bodySize * 0.3) return false;

   return true;
}

//+------------------------------------------------------------------+
//| Check for exit signals                                          |
//+------------------------------------------------------------------+
bool IsExitSignal(int index, const double &close[], const double &open[])
{
   if(index + 2 >= ArraySize(close)) return false;

   // Check for momentum reversal
   bool bullishReversal = (close[index] > open[index]) &&
                         (close[index + 1] < open[index + 1]) &&
                         (close[index + 2] < open[index + 2]);

   bool bearishReversal = (close[index] < open[index]) &&
                         (close[index + 1] > open[index + 1]) &&
                         (close[index + 2] > open[index + 2]);

   return (bullishReversal || bearishReversal);
}

//+------------------------------------------------------------------+
//| Create information panel                                         |
//+------------------------------------------------------------------+
void CreateInfoPanel()
{
   int xStart = 7;
   int yStart = 30;
   int yIncrement = 16;
   int ySize = 80;

   // Create background box (from reference structure)
   ObjectCreate(chartID, LabelBox, OBJ_RECTANGLE_LABEL, 0, 0, 0);
   ObjectSetInteger(chartID, LabelBox, OBJPROP_XDISTANCE, 5);
   ObjectSetInteger(chartID, LabelBox, OBJPROP_YDISTANCE, 25);
   ObjectSetInteger(chartID, LabelBox, OBJPROP_XSIZE, 200);
   ObjectSetInteger(chartID, LabelBox, OBJPROP_YSIZE, ySize);
   ObjectSetInteger(chartID, LabelBox, OBJPROP_BGCOLOR, clrNavy);
   ObjectSetInteger(chartID, LabelBox, OBJPROP_BORDER_TYPE, BORDER_FLAT);
   ObjectSetInteger(chartID, LabelBox, OBJPROP_BORDER_COLOR, clrGold);

   // Create labels
   ObjectCreate(chartID, Label1, OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(chartID, Label1, OBJPROP_XDISTANCE, xStart);
   ObjectSetInteger(chartID, Label1, OBJPROP_YDISTANCE, yStart);
   ObjectSetString(chartID, Label1, OBJPROP_FONT, "Arial Bold");
   ObjectSetInteger(chartID, Label1, OBJPROP_FONTSIZE, 9);
   ObjectSetInteger(chartID, Label1, OBJPROP_COLOR, clrWhite);

   ObjectCreate(chartID, Label2, OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(chartID, Label2, OBJPROP_XDISTANCE, xStart);
   ObjectSetInteger(chartID, Label2, OBJPROP_YDISTANCE, yStart + yIncrement);
   ObjectSetString(chartID, Label2, OBJPROP_FONT, "Arial Bold");
   ObjectSetInteger(chartID, Label2, OBJPROP_FONTSIZE, 9);
   ObjectSetInteger(chartID, Label2, OBJPROP_COLOR, clrWhite);

   ObjectCreate(chartID, Label3, OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(chartID, Label3, OBJPROP_XDISTANCE, xStart);
   ObjectSetInteger(chartID, Label3, OBJPROP_YDISTANCE, yStart + yIncrement * 2);
   ObjectSetString(chartID, Label3, OBJPROP_FONT, "Arial Bold");
   ObjectSetInteger(chartID, Label3, OBJPROP_FONTSIZE, 9);
   ObjectSetInteger(chartID, Label3, OBJPROP_COLOR, clrLime);

   ObjectCreate(chartID, Label4, OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(chartID, Label4, OBJPROP_XDISTANCE, xStart);
   ObjectSetInteger(chartID, Label4, OBJPROP_YDISTANCE, yStart + yIncrement * 3);
   ObjectSetString(chartID, Label4, OBJPROP_FONT, "Arial Bold");
   ObjectSetInteger(chartID, Label4, OBJPROP_FONTSIZE, 9);
   ObjectSetInteger(chartID, Label4, OBJPROP_COLOR, clrYellow);
}

//+------------------------------------------------------------------+
//| Update information panel                                         |
//+------------------------------------------------------------------+
void UpdateInfoPanel()
{
   if(!InpShowInfoPanel) return;

   // Update spread info (from reference structure)
   if(SymbolInfoTick(_Symbol, tick))
      ObjectSetString(chartID, Label1, OBJPROP_TEXT, label1 +
                      DoubleToString((tick.ask - tick.bid) * doubleToPip, 1) + " pips");

   // Update strategy info
   string strategyText = "Strategy: ";
   switch(InpStrategy)
   {
      case STRATEGY_JUMP75: strategyText += "Jump 75 Only"; break;
      case STRATEGY_CRASH1000: strategyText += "Crash 1000 Only"; break;
      case STRATEGY_BOTH: strategyText += "Both Strategies"; break;
   }
   ObjectSetString(chartID, Label2, OBJPROP_TEXT, strategyText);

   // Update signal status
   ObjectSetString(chartID, Label3, OBJPROP_TEXT, label3 + "Active");

   // Update time
   ObjectSetString(chartID, Label4, OBJPROP_TEXT, label4 + TimeToString(TimeCurrent(), TIME_SECONDS));
}

//+------------------------------------------------------------------+
//| Handle alerts for all signal types                              |
//+------------------------------------------------------------------+
void HandleAlerts(const datetime &time, const ENUM_SIGNAL signal_type, double price)
{
   static datetime timePrev;
   static ENUM_SIGNAL typePrev;

   if(timePrev != time || typePrev != signal_type)
   {
      timePrev = time;
      typePrev = signal_type;

      string alertMessage = "";
      string signalName = "";

      switch(signal_type)
      {
         case SIGNAL_JUMP_BUY:
            signalName = "JUMP 75 BUY";
            alertMessage = StringFormat("🚀 %s SIGNAL @ %s - Price: %s",
                          signalName, _Symbol, DoubleToString(price, _Digits));
            break;

         case SIGNAL_CRASH_SELL:
            signalName = "CRASH 1000 SELL";
            alertMessage = StringFormat("💥 %s SIGNAL @ %s - Price: %s",
                          signalName, _Symbol, DoubleToString(price, _Digits));
            break;

         case SIGNAL_EXIT:
            signalName = "EXIT";
            alertMessage = StringFormat("🔄 %s SIGNAL @ %s - Price: %s",
                          signalName, _Symbol, DoubleToString(price, _Digits));
            break;
      }

      // Show alerts based on settings
      if(InpShowAlerts && alertMessage != "")
      {
         Alert(alertMessage);
         Print(alertMessage);
      }

      if(InpSoundAlerts)
      {
         PlaySound("alert.wav");
      }

      if(InpEmailAlerts)
      {
         SendMail("AMPD Signal Alert", alertMessage);
      }

      if(InpPushAlerts)
      {
         SendNotification(alertMessage);
      }
   }
}

//+------------------------------------------------------------------+
//| Get current signal for EA integration                           |
//+------------------------------------------------------------------+
ENUM_SIGNAL GetCurrentSignal()
{
   if(JumpBuyBuffer[0] != 0.0) return SIGNAL_JUMP_BUY;
   if(CrashSellBuffer[0] != 0.0) return SIGNAL_CRASH_SELL;
   if(ExitBuffer[0] != 0.0) return SIGNAL_EXIT;
   return SIGNAL_NONE;
}

//+------------------------------------------------------------------+
//| Check if Jump 75 signal is active                              |
//+------------------------------------------------------------------+
bool IsJumpSignalActive()
{
   return (JumpBuyBuffer[0] != 0.0);
}

//+------------------------------------------------------------------+
//| Check if Crash 1000 signal is active                           |
//+------------------------------------------------------------------+
bool IsCrashSignalActive()
{
   return (CrashSellBuffer[0] != 0.0);
}

//+------------------------------------------------------------------+
//| Check if Exit signal is active                                  |
//+------------------------------------------------------------------+
bool IsExitSignalActive()
{
   return (ExitBuffer[0] != 0.0);
}

//+------------------------------------------------------------------+
//| Get signal strength (for advanced analysis)                     |
//+------------------------------------------------------------------+
double GetSignalStrength(ENUM_SIGNAL signal_type)
{
   double strength = 0.0;

   switch(signal_type)
   {
      case SIGNAL_JUMP_BUY:
         if(JumpBuyBuffer[0] != 0.0)
         {
            double atr = GetATR(0);
            double candleSize = MathAbs(iClose(_Symbol, PERIOD_CURRENT, 0) - iOpen(_Symbol, PERIOD_CURRENT, 0));
            strength = atr > 0 ? (candleSize / atr) : 0.0;
         }
         break;

      case SIGNAL_CRASH_SELL:
         if(CrashSellBuffer[0] != 0.0)
         {
            double rsi = GetRSI(0);
            strength = (rsi - 50.0) / 50.0; // Normalize RSI strength
         }
         break;

      case SIGNAL_EXIT:
         if(ExitBuffer[0] != 0.0)
            strength = 1.0;
         break;
   }

   return MathMax(0.0, MathMin(1.0, strength)); // Normalize between 0-1
}
