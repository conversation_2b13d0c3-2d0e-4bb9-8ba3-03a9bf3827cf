//+------------------------------------------------------------------+
//|                           AMPD Master Dashboard.mq5             |
//|                        Copyright 2024, <PERSON>se <PERSON> |
//|                                             https://www.ampd.com |
//+------------------------------------------------------------------+
#property copyright "Arise Moroka Prince Dynasty"
#property link      "https://ampd.com"
#property description "Master Control Dashboard for AMPD High-Frequency Trading System"
#property version   "1.0"
#property indicator_chart_window

//--- Input parameters
input group "=== DASHBOARD SETTINGS ==="
input bool   InpShowMasterPanel = true;           // Show Master Control Panel
input bool   InpRealTimeMonitoring = true;        // Real-Time System Monitoring
input bool   InpAutoValidation = true;            // Automatic System Validation
input int    InpUpdateInterval = 5;               // Update Interval (seconds)
input bool   InpSoundAlerts = true;               // Sound Alerts for Issues

input group "=== MONITORING TARGETS ==="
input double InpTargetSignalsPerHour = 15.0;      // Target Signals per Hour
input double InpTargetExecutionSpeed = 100.0;     // Target Execution Speed (ms)
input double InpTargetWinRate = 60.0;             // Target Win Rate (%)
input double InpMaxDrawdown = 10.0;               // Maximum Drawdown (%)

//--- Dashboard components
long chartID;
datetime lastUpdate = 0;
bool systemHealthy = true;
string lastAlert = "";

//--- System status tracking
struct SystemStatus
{
   bool indicatorActive;
   bool eaActive;
   bool signalsGenerating;
   bool tradesExecuting;
   double currentSignalFreq;
   double currentExecutionSpeed;
   double currentWinRate;
   double currentDrawdown;
   int totalTrades;
   double sessionPL;
   string lastIssue;
   datetime lastTradeTime;
};

SystemStatus currentStatus;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
   chartID = ChartID();
   
   //--- Initialize dashboard
   if(InpShowMasterPanel)
      CreateMasterDashboard();
   
   //--- Initialize system monitoring
   InitializeSystemMonitoring();
   
   //--- Set timer for regular updates
   EventSetTimer(InpUpdateInterval);
   
   Print("=== AMPD MASTER DASHBOARD INITIALIZED ===");
   Print("Real-Time Monitoring: ", InpRealTimeMonitoring ? "ENABLED" : "DISABLED");
   Print("Auto Validation: ", InpAutoValidation ? "ENABLED" : "DISABLED");
   Print("Update Interval: ", InpUpdateInterval, " seconds");
   
   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                             |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
   //--- Update dashboard on new bar
   if(InpRealTimeMonitoring && rates_total > prev_calculated)
   {
      UpdateSystemStatus();
      UpdateMasterDashboard();
   }
   
   return rates_total;
}

//+------------------------------------------------------------------+
//| Timer function for regular updates                              |
//+------------------------------------------------------------------+
void OnTimer()
{
   if(InpRealTimeMonitoring)
   {
      UpdateSystemStatus();
      UpdateMasterDashboard();
      
      //--- Perform automatic validation if enabled
      if(InpAutoValidation)
         PerformQuickValidation();
      
      //--- Check for system issues
      CheckSystemHealth();
   }
}

//+------------------------------------------------------------------+
//| Create master control dashboard                                 |
//+------------------------------------------------------------------+
void CreateMasterDashboard()
{
   //--- Main dashboard panel
   ObjectCreate(chartID, "AMPD_Master_Panel", OBJ_RECTANGLE_LABEL, 0, 0, 0);
   ObjectSetInteger(chartID, "AMPD_Master_Panel", OBJPROP_XDISTANCE, 10);
   ObjectSetInteger(chartID, "AMPD_Master_Panel", OBJPROP_YDISTANCE, 10);
   ObjectSetInteger(chartID, "AMPD_Master_Panel", OBJPROP_XSIZE, 400);
   ObjectSetInteger(chartID, "AMPD_Master_Panel", OBJPROP_YSIZE, 300);
   ObjectSetInteger(chartID, "AMPD_Master_Panel", OBJPROP_BGCOLOR, clrDarkSlateGray);
   ObjectSetInteger(chartID, "AMPD_Master_Panel", OBJPROP_BORDER_COLOR, clrGold);
   ObjectSetInteger(chartID, "AMPD_Master_Panel", OBJPROP_BORDER_TYPE, BORDER_FLAT);
   
   //--- Dashboard title
   ObjectCreate(chartID, "AMPD_Title", OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(chartID, "AMPD_Title", OBJPROP_XDISTANCE, 20);
   ObjectSetInteger(chartID, "AMPD_Title", OBJPROP_YDISTANCE, 20);
   ObjectSetString(chartID, "AMPD_Title", OBJPROP_TEXT, "AMPD HIGH-FREQUENCY TRADING SYSTEM");
   ObjectSetString(chartID, "AMPD_Title", OBJPROP_FONT, "Arial Bold");
   ObjectSetInteger(chartID, "AMPD_Title", OBJPROP_FONTSIZE, 12);
   ObjectSetInteger(chartID, "AMPD_Title", OBJPROP_COLOR, clrGold);
   
   //--- Create status labels
   string labels[] = {
      "System Status:",
      "Indicator:",
      "Expert Advisor:",
      "Signal Generation:",
      "Trade Execution:",
      "Signals/Hour:",
      "Execution Speed:",
      "Win Rate:",
      "Drawdown:",
      "Session P&L:",
      "Total Trades:",
      "Last Trade:",
      "System Health:",
      "Last Alert:"
   };
   
   for(int i = 0; i < ArraySize(labels); i++)
   {
      string objName = "AMPD_Label_" + IntegerToString(i);
      ObjectCreate(chartID, objName, OBJ_LABEL, 0, 0, 0);
      ObjectSetInteger(chartID, objName, OBJPROP_XDISTANCE, 20);
      ObjectSetInteger(chartID, objName, OBJPROP_YDISTANCE, 50 + i * 18);
      ObjectSetString(chartID, objName, OBJPROP_TEXT, labels[i]);
      ObjectSetString(chartID, objName, OBJPROP_FONT, "Consolas");
      ObjectSetInteger(chartID, objName, OBJPROP_FONTSIZE, 9);
      ObjectSetInteger(chartID, objName, OBJPROP_COLOR, clrWhite);
   }
   
   //--- Create value labels
   for(int i = 0; i < ArraySize(labels); i++)
   {
      string objName = "AMPD_Value_" + IntegerToString(i);
      ObjectCreate(chartID, objName, OBJ_LABEL, 0, 0, 0);
      ObjectSetInteger(chartID, objName, OBJPROP_XDISTANCE, 200);
      ObjectSetInteger(chartID, objName, OBJPROP_YDISTANCE, 50 + i * 18);
      ObjectSetString(chartID, objName, OBJPROP_TEXT, "Initializing...");
      ObjectSetString(chartID, objName, OBJPROP_FONT, "Consolas");
      ObjectSetInteger(chartID, objName, OBJPROP_FONTSIZE, 9);
      ObjectSetInteger(chartID, objName, OBJPROP_COLOR, clrLightGray);
   }
   
   //--- Control buttons
   CreateControlButtons();
}

//+------------------------------------------------------------------+
//| Create control buttons                                           |
//+------------------------------------------------------------------+
void CreateControlButtons()
{
   //--- Validate System button
   ObjectCreate(chartID, "AMPD_Validate_Btn", OBJ_BUTTON, 0, 0, 0);
   ObjectSetInteger(chartID, "AMPD_Validate_Btn", OBJPROP_XDISTANCE, 20);
   ObjectSetInteger(chartID, "AMPD_Validate_Btn", OBJPROP_YDISTANCE, 270);
   ObjectSetInteger(chartID, "AMPD_Validate_Btn", OBJPROP_XSIZE, 80);
   ObjectSetInteger(chartID, "AMPD_Validate_Btn", OBJPROP_YSIZE, 25);
   ObjectSetString(chartID, "AMPD_Validate_Btn", OBJPROP_TEXT, "Validate");
   ObjectSetInteger(chartID, "AMPD_Validate_Btn", OBJPROP_COLOR, clrWhite);
   ObjectSetInteger(chartID, "AMPD_Validate_Btn", OBJPROP_BGCOLOR, clrDarkGreen);
   
   //--- Run Backtest button
   ObjectCreate(chartID, "AMPD_Backtest_Btn", OBJ_BUTTON, 0, 0, 0);
   ObjectSetInteger(chartID, "AMPD_Backtest_Btn", OBJPROP_XDISTANCE, 110);
   ObjectSetInteger(chartID, "AMPD_Backtest_Btn", OBJPROP_YDISTANCE, 270);
   ObjectSetInteger(chartID, "AMPD_Backtest_Btn", OBJPROP_XSIZE, 80);
   ObjectSetInteger(chartID, "AMPD_Backtest_Btn", OBJPROP_YSIZE, 25);
   ObjectSetString(chartID, "AMPD_Backtest_Btn", OBJPROP_TEXT, "Backtest");
   ObjectSetInteger(chartID, "AMPD_Backtest_Btn", OBJPROP_COLOR, clrWhite);
   ObjectSetInteger(chartID, "AMPD_Backtest_Btn", OBJPROP_BGCOLOR, clrDarkBlue);
   
   //--- Emergency Stop button
   ObjectCreate(chartID, "AMPD_Stop_Btn", OBJ_BUTTON, 0, 0, 0);
   ObjectSetInteger(chartID, "AMPD_Stop_Btn", OBJPROP_XDISTANCE, 200);
   ObjectSetInteger(chartID, "AMPD_Stop_Btn", OBJPROP_YDISTANCE, 270);
   ObjectSetInteger(chartID, "AMPD_Stop_Btn", OBJPROP_XSIZE, 80);
   ObjectSetInteger(chartID, "AMPD_Stop_Btn", OBJPROP_YSIZE, 25);
   ObjectSetString(chartID, "AMPD_Stop_Btn", OBJPROP_TEXT, "STOP");
   ObjectSetInteger(chartID, "AMPD_Stop_Btn", OBJPROP_COLOR, clrWhite);
   ObjectSetInteger(chartID, "AMPD_Stop_Btn", OBJPROP_BGCOLOR, clrDarkRed);
   
   //--- Reset button
   ObjectCreate(chartID, "AMPD_Reset_Btn", OBJ_BUTTON, 0, 0, 0);
   ObjectSetInteger(chartID, "AMPD_Reset_Btn", OBJPROP_XDISTANCE, 290);
   ObjectSetInteger(chartID, "AMPD_Reset_Btn", OBJPROP_YDISTANCE, 270);
   ObjectSetInteger(chartID, "AMPD_Reset_Btn", OBJPROP_XSIZE, 80);
   ObjectSetInteger(chartID, "AMPD_Reset_Btn", OBJPROP_YSIZE, 25);
   ObjectSetString(chartID, "AMPD_Reset_Btn", OBJPROP_TEXT, "Reset");
   ObjectSetInteger(chartID, "AMPD_Reset_Btn", OBJPROP_COLOR, clrWhite);
   ObjectSetInteger(chartID, "AMPD_Reset_Btn", OBJPROP_BGCOLOR, clrDarkGoldenrod);
}

//+------------------------------------------------------------------+
//| Initialize system monitoring                                     |
//+------------------------------------------------------------------+
void InitializeSystemMonitoring()
{
   //--- Initialize status structure
   currentStatus.indicatorActive = false;
   currentStatus.eaActive = false;
   currentStatus.signalsGenerating = false;
   currentStatus.tradesExecuting = false;
   currentStatus.currentSignalFreq = 0;
   currentStatus.currentExecutionSpeed = 0;
   currentStatus.currentWinRate = 0;
   currentStatus.currentDrawdown = 0;
   currentStatus.totalTrades = 0;
   currentStatus.sessionPL = 0;
   currentStatus.lastIssue = "None";
   currentStatus.lastTradeTime = 0;
   
   systemHealthy = true;
   lastAlert = "";
}

//+------------------------------------------------------------------+
//| Update system status                                             |
//+------------------------------------------------------------------+
void UpdateSystemStatus()
{
   //--- Check indicator status
   int indicatorHandle = iCustom(_Symbol, PERIOD_M1, "AMPD_RealTime_Precision_Indicator");
   currentStatus.indicatorActive = (indicatorHandle != INVALID_HANDLE);
   if(indicatorHandle != INVALID_HANDLE)
   {
      //--- Check for recent signals
      double buyBuffer[5], sellBuffer[5];
      if(CopyBuffer(indicatorHandle, 0, 0, 5, buyBuffer) == 5 &&
         CopyBuffer(indicatorHandle, 1, 0, 5, sellBuffer) == 5)
      {
         currentStatus.signalsGenerating = false;
         for(int i = 0; i < 5; i++)
         {
            if((buyBuffer[i] != 0 && buyBuffer[i] != EMPTY_VALUE) ||
               (sellBuffer[i] != 0 && sellBuffer[i] != EMPTY_VALUE))
            {
               currentStatus.signalsGenerating = true;
               break;
            }
         }
      }
      IndicatorRelease(indicatorHandle);
   }
   
   //--- Check EA status (by looking for recent trades)
   currentStatus.eaActive = false;
   currentStatus.tradesExecuting = false;
   
   if(HistorySelect(TimeCurrent() - 3600, TimeCurrent())) // Last hour
   {
      for(int i = 0; i < HistoryDealsTotal(); i++)
      {
         ulong ticket = HistoryDealGetTicket(i);
         if(HistoryDealGetInteger(ticket, DEAL_MAGIC) == 123456) // EA magic number
         {
            currentStatus.eaActive = true;
            if(HistoryDealGetInteger(ticket, DEAL_TIME) > TimeCurrent() - 300) // Last 5 minutes
            {
               currentStatus.tradesExecuting = true;
               currentStatus.lastTradeTime = (datetime)HistoryDealGetInteger(ticket, DEAL_TIME);
            }
            break;
         }
      }
   }
   
   //--- Calculate performance metrics
   CalculatePerformanceMetrics();
   
   //--- Update signal frequency
   static datetime sessionStart = TimeCurrent();
   double hoursElapsed = (double)(TimeCurrent() - sessionStart) / 3600;
   if(hoursElapsed > 0)
   {
      // This would need to be tracked from the indicator
      currentStatus.currentSignalFreq = currentStatus.totalTrades / hoursElapsed;
   }
}

//+------------------------------------------------------------------+
//| Calculate performance metrics                                    |
//+------------------------------------------------------------------+
void CalculatePerformanceMetrics()
{
   if(!HistorySelect(iTime(_Symbol, PERIOD_D1, 0), TimeCurrent()))
      return;
   
   int totalTrades = 0;
   int winningTrades = 0;
   double totalProfit = 0;
   double totalLoss = 0;
   double sessionPL = 0;
   
   for(int i = 0; i < HistoryDealsTotal(); i++)
   {
      ulong ticket = HistoryDealGetTicket(i);
      if(HistoryDealGetString(ticket, DEAL_SYMBOL) == _Symbol &&
         HistoryDealGetInteger(ticket, DEAL_MAGIC) == 123456 &&
         HistoryDealGetInteger(ticket, DEAL_ENTRY) == DEAL_ENTRY_OUT)
      {
         totalTrades++;
         double profit = HistoryDealGetDouble(ticket, DEAL_PROFIT);
         sessionPL += profit;
         
         if(profit > 0)
         {
            winningTrades++;
            totalProfit += profit;
         }
         else
         {
            totalLoss += MathAbs(profit);
         }
      }
   }
   
   currentStatus.totalTrades = totalTrades;
   currentStatus.currentWinRate = totalTrades > 0 ? (double)winningTrades / totalTrades * 100 : 0;
   currentStatus.sessionPL = sessionPL;
   
   //--- Calculate drawdown
   double currentEquity = AccountInfoDouble(ACCOUNT_EQUITY);
   double currentBalance = AccountInfoDouble(ACCOUNT_BALANCE);
   currentStatus.currentDrawdown = currentBalance > 0 ? (currentBalance - currentEquity) / currentBalance * 100 : 0;
}

//+------------------------------------------------------------------+
//| Update master dashboard display                                  |
//+------------------------------------------------------------------+
void UpdateMasterDashboard()
{
   if(!InpShowMasterPanel) return;
   
   //--- Update status values
   ObjectSetString(chartID, "AMPD_Value_0", OBJPROP_TEXT, systemHealthy ? "✅ HEALTHY" : "❌ ISSUES");
   ObjectSetInteger(chartID, "AMPD_Value_0", OBJPROP_COLOR, systemHealthy ? clrGreen : clrRed);
   
   ObjectSetString(chartID, "AMPD_Value_1", OBJPROP_TEXT, currentStatus.indicatorActive ? "✅ ACTIVE" : "❌ INACTIVE");
   ObjectSetInteger(chartID, "AMPD_Value_1", OBJPROP_COLOR, currentStatus.indicatorActive ? clrGreen : clrRed);
   
   ObjectSetString(chartID, "AMPD_Value_2", OBJPROP_TEXT, currentStatus.eaActive ? "✅ ACTIVE" : "❌ INACTIVE");
   ObjectSetInteger(chartID, "AMPD_Value_2", OBJPROP_COLOR, currentStatus.eaActive ? clrGreen : clrRed);
   
   ObjectSetString(chartID, "AMPD_Value_3", OBJPROP_TEXT, currentStatus.signalsGenerating ? "✅ GENERATING" : "⚠️ NO SIGNALS");
   ObjectSetInteger(chartID, "AMPD_Value_3", OBJPROP_COLOR, currentStatus.signalsGenerating ? clrGreen : clrOrange);
   
   ObjectSetString(chartID, "AMPD_Value_4", OBJPROP_TEXT, currentStatus.tradesExecuting ? "✅ EXECUTING" : "⏸️ WAITING");
   ObjectSetInteger(chartID, "AMPD_Value_4", OBJPROP_COLOR, currentStatus.tradesExecuting ? clrGreen : clrGray);
   
   ObjectSetString(chartID, "AMPD_Value_5", OBJPROP_TEXT, DoubleToString(currentStatus.currentSignalFreq, 1));
   ObjectSetInteger(chartID, "AMPD_Value_5", OBJPROP_COLOR, 
                   currentStatus.currentSignalFreq >= InpTargetSignalsPerHour ? clrGreen : clrOrange);
   
   ObjectSetString(chartID, "AMPD_Value_6", OBJPROP_TEXT, DoubleToString(currentStatus.currentExecutionSpeed, 1) + "ms");
   ObjectSetInteger(chartID, "AMPD_Value_6", OBJPROP_COLOR, 
                   currentStatus.currentExecutionSpeed <= InpTargetExecutionSpeed ? clrGreen : clrOrange);
   
   ObjectSetString(chartID, "AMPD_Value_7", OBJPROP_TEXT, DoubleToString(currentStatus.currentWinRate, 1) + "%");
   ObjectSetInteger(chartID, "AMPD_Value_7", OBJPROP_COLOR, 
                   currentStatus.currentWinRate >= InpTargetWinRate ? clrGreen : clrOrange);
   
   ObjectSetString(chartID, "AMPD_Value_8", OBJPROP_TEXT, DoubleToString(currentStatus.currentDrawdown, 2) + "%");
   ObjectSetInteger(chartID, "AMPD_Value_8", OBJPROP_COLOR, 
                   currentStatus.currentDrawdown <= InpMaxDrawdown ? clrGreen : clrRed);
   
   ObjectSetString(chartID, "AMPD_Value_9", OBJPROP_TEXT, "$" + DoubleToString(currentStatus.sessionPL, 2));
   ObjectSetInteger(chartID, "AMPD_Value_9", OBJPROP_COLOR, 
                   currentStatus.sessionPL >= 0 ? clrGreen : clrRed);
   
   ObjectSetString(chartID, "AMPD_Value_10", OBJPROP_TEXT, IntegerToString(currentStatus.totalTrades));
   ObjectSetInteger(chartID, "AMPD_Value_10", OBJPROP_COLOR, clrWhite);
   
   ObjectSetString(chartID, "AMPD_Value_11", OBJPROP_TEXT, 
                   currentStatus.lastTradeTime > 0 ? TimeToString(currentStatus.lastTradeTime, TIME_MINUTES) : "None");
   ObjectSetInteger(chartID, "AMPD_Value_11", OBJPROP_COLOR, clrLightGray);
   
   ObjectSetString(chartID, "AMPD_Value_12", OBJPROP_TEXT, systemHealthy ? "✅ OPTIMAL" : "⚠️ CHECK ISSUES");
   ObjectSetInteger(chartID, "AMPD_Value_12", OBJPROP_COLOR, systemHealthy ? clrGreen : clrOrange);
   
   ObjectSetString(chartID, "AMPD_Value_13", OBJPROP_TEXT, lastAlert != "" ? lastAlert : "None");
   ObjectSetInteger(chartID, "AMPD_Value_13", OBJPROP_COLOR, lastAlert != "" ? clrYellow : clrGray);
}

//+------------------------------------------------------------------+
//| Perform quick system validation                                  |
//+------------------------------------------------------------------+
void PerformQuickValidation()
{
   static datetime lastValidation = 0;
   if(TimeCurrent() - lastValidation < 300) return; // Validate every 5 minutes
   lastValidation = TimeCurrent();

   bool validationPassed = true;
   string issues = "";

   //--- Check critical components
   if(!currentStatus.indicatorActive)
   {
      validationPassed = false;
      issues += "Indicator not active; ";
   }

   if(!currentStatus.eaActive)
   {
      validationPassed = false;
      issues += "EA not active; ";
   }

   if(!currentStatus.signalsGenerating)
   {
      validationPassed = false;
      issues += "No signals generating; ";
   }

   if(currentStatus.currentDrawdown > InpMaxDrawdown)
   {
      validationPassed = false;
      issues += "Drawdown exceeded; ";
   }

   //--- Update system health
   systemHealthy = validationPassed;
   if(!validationPassed)
   {
      currentStatus.lastIssue = issues;
      lastAlert = "System issues detected";

      if(InpSoundAlerts)
         PlaySound("alert.wav");

      Print("⚠️ SYSTEM VALIDATION FAILED: ", issues);
   }
}

//+------------------------------------------------------------------+
//| Check overall system health                                      |
//+------------------------------------------------------------------+
void CheckSystemHealth()
{
   static datetime lastHealthCheck = 0;
   if(TimeCurrent() - lastHealthCheck < 60) return; // Check every minute
   lastHealthCheck = TimeCurrent();

   //--- Performance-based health checks
   bool performanceHealthy = true;
   string healthIssues = "";

   if(currentStatus.currentSignalFreq < InpTargetSignalsPerHour * 0.5) // 50% of target
   {
      performanceHealthy = false;
      healthIssues += "Low signal frequency; ";
   }

   if(currentStatus.currentWinRate < InpTargetWinRate * 0.7) // 70% of target
   {
      performanceHealthy = false;
      healthIssues += "Low win rate; ";
   }

   if(currentStatus.currentExecutionSpeed > InpTargetExecutionSpeed * 1.5) // 150% of target
   {
      performanceHealthy = false;
      healthIssues += "Slow execution; ";
   }

   //--- Update health status
   if(!performanceHealthy)
   {
      lastAlert = "Performance issues: " + healthIssues;
      Print("⚠️ PERFORMANCE WARNING: ", healthIssues);
   }

   //--- Check for trading halt conditions
   if(currentStatus.currentDrawdown > InpMaxDrawdown * 1.5)
   {
      lastAlert = "CRITICAL: Excessive drawdown - Consider stopping trading";
      Print("🚨 CRITICAL ALERT: Drawdown ", DoubleToString(currentStatus.currentDrawdown, 2),
            "% exceeds safe limits");

      if(InpSoundAlerts)
         PlaySound("stops.wav");
   }
}

//+------------------------------------------------------------------+
//| Handle chart events (button clicks)                             |
//+------------------------------------------------------------------+
void OnChartEvent(const int id,
                  const long &lparam,
                  const double &dparam,
                  const string &sparam)
{
   if(id == CHARTEVENT_OBJECT_CLICK)
   {
      if(sparam == "AMPD_Validate_Btn")
      {
         Print("🔍 Running system validation...");
         // This would call the validation script
         lastAlert = "Validation initiated";
         ObjectSetInteger(chartID, "AMPD_Validate_Btn", OBJPROP_STATE, false);
      }
      else if(sparam == "AMPD_Backtest_Btn")
      {
         Print("📊 Starting backtest...");
         // This would call the backtesting script
         lastAlert = "Backtest initiated";
         ObjectSetInteger(chartID, "AMPD_Backtest_Btn", OBJPROP_STATE, false);
      }
      else if(sparam == "AMPD_Stop_Btn")
      {
         Print("🛑 EMERGENCY STOP ACTIVATED");
         lastAlert = "EMERGENCY STOP - Trading halted";
         systemHealthy = false;

         // Send critical alert
         Alert("🚨 AMPD EMERGENCY STOP ACTIVATED - All trading halted!");
         if(InpSoundAlerts)
            PlaySound("stops.wav");

         ObjectSetInteger(chartID, "AMPD_Stop_Btn", OBJPROP_STATE, false);
      }
      else if(sparam == "AMPD_Reset_Btn")
      {
         Print("🔄 Resetting system status...");
         InitializeSystemMonitoring();
         lastAlert = "System reset completed";
         ObjectSetInteger(chartID, "AMPD_Reset_Btn", OBJPROP_STATE, false);
      }
   }
}

//+------------------------------------------------------------------+
//| Indicator deinitialization function                             |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   //--- Remove timer
   EventKillTimer();

   //--- Clean up dashboard objects
   ObjectDelete(chartID, "AMPD_Master_Panel");
   ObjectDelete(chartID, "AMPD_Title");

   for(int i = 0; i < 14; i++)
   {
      ObjectDelete(chartID, "AMPD_Label_" + IntegerToString(i));
      ObjectDelete(chartID, "AMPD_Value_" + IntegerToString(i));
   }

   ObjectDelete(chartID, "AMPD_Validate_Btn");
   ObjectDelete(chartID, "AMPD_Backtest_Btn");
   ObjectDelete(chartID, "AMPD_Stop_Btn");
   ObjectDelete(chartID, "AMPD_Reset_Btn");

   Print("AMPD Master Dashboard deinitialized");
}
