//+------------------------------------------------------------------+
//|                           AMPD System Validator.mq5             |
//|                        Copyright 2024, <PERSON>se <PERSON>rok<PERSON> Prince <PERSON> |
//|                                             https://www.ampd.com |
//+------------------------------------------------------------------+
#property copyright "Arise Moroka Prince Dynasty"
#property link      "https://ampd.com"
#property description "Comprehensive System Validation and Debugging Tool for AMPD High-Frequency Trading System"
#property version   "1.0"
#property script_show_inputs

//--- Input parameters
input group "=== VALIDATION SETTINGS ==="
input bool   InpValidateIndicator = true;         // Validate Indicator Functionality
input bool   InpValidateEA = true;                // Validate EA Functionality
input bool   InpTestSignalGeneration = true;      // Test Signal Generation
input bool   InpTestExecutionSpeed = true;        // Test Execution Speed
input bool   InpTestVisualElements = true;        // Test Visual Elements
input bool   InpGenerateReport = true;            // Generate Validation Report

input group "=== TEST PARAMETERS ==="
input int    InpTestBars = 100;                   // Number of Bars to Test
input int    InpSpeedTestIterations = 50;         // Speed Test Iterations
input double InpExpectedSignalFreq = 15.0;        // Expected Signals per Hour
input double InpMaxExecutionTime = 100.0;         // Max Execution Time (ms)

//--- Validation results structure
struct ValidationResult
{
   string testName;
   bool   passed;
   string details;
   double value;
   string recommendation;
};

ValidationResult validationResults[];
int totalTests = 0;
int passedTests = 0;

//+------------------------------------------------------------------+
//| Script program start function                                   |
//+------------------------------------------------------------------+
void OnStart()
{
   Print("=== AMPD SYSTEM VALIDATION STARTED ===");
   Print("Validating High-Frequency Trading System Components...");
   
   //--- Initialize validation
   ArrayResize(validationResults, 20);
   totalTests = 0;
   passedTests = 0;
   
   //--- Run comprehensive validation tests
   if(InpValidateIndicator)
      ValidateIndicatorFunctionality();
   
   if(InpValidateEA)
      ValidateEAFunctionality();
   
   if(InpTestSignalGeneration)
      TestSignalGeneration();
   
   if(InpTestExecutionSpeed)
      TestExecutionSpeed();
   
   if(InpTestVisualElements)
      TestVisualElements();
   
   //--- Additional system checks
   ValidateSystemRequirements();
   ValidateMarketConditions();
   
   //--- Generate comprehensive report
   if(InpGenerateReport)
      GenerateValidationReport();
   
   //--- Display summary
   DisplayValidationSummary();
   
   Print("=== AMPD SYSTEM VALIDATION COMPLETED ===");
}

//+------------------------------------------------------------------+
//| Validate indicator functionality                                |
//+------------------------------------------------------------------+
void ValidateIndicatorFunctionality()
{
   Print("\n--- VALIDATING INDICATOR FUNCTIONALITY ---");
   
   //--- Test 1: Check if indicator exists and loads
   int indicatorHandle = iCustom(_Symbol, PERIOD_M1, "AMPD_RealTime_Precision_Indicator");
   bool indicatorExists = (indicatorHandle != INVALID_HANDLE);
   
   AddValidationResult("Indicator Loading", indicatorExists, 
                      indicatorExists ? "Indicator loads successfully" : "Indicator failed to load",
                      0, indicatorExists ? "None" : "Check indicator compilation and placement");
   
   if(!indicatorExists)
   {
      Print("ERROR: Cannot validate indicator - not found or failed to load");
      return;
   }
   
   //--- Test 2: Check buffer data availability
   double buyBuffer[10], sellBuffer[10];
   bool buffersAvailable = (CopyBuffer(indicatorHandle, 0, 0, 10, buyBuffer) == 10 &&
                           CopyBuffer(indicatorHandle, 1, 0, 10, sellBuffer) == 10);
   
   AddValidationResult("Indicator Buffers", buffersAvailable,
                      buffersAvailable ? "All buffers accessible" : "Buffer access failed",
                      0, buffersAvailable ? "None" : "Check indicator buffer initialization");
   
   //--- Test 3: Check for signal generation
   int signalCount = 0;
   for(int i = 0; i < 10; i++)
   {
      if(buyBuffer[i] != 0 && buyBuffer[i] != EMPTY_VALUE) signalCount++;
      if(sellBuffer[i] != 0 && sellBuffer[i] != EMPTY_VALUE) signalCount++;
   }
   
   bool signalsGenerated = (signalCount > 0);
   AddValidationResult("Signal Generation", signalsGenerated,
                      "Signals found in last 10 bars: " + IntegerToString(signalCount),
                      signalCount, signalsGenerated ? "None" : "Check signal logic and parameters");
   
   //--- Test 4: Validate high-frequency mode
   // This would require checking indicator's internal state
   // For now, we'll assume it's working if signals are generated
   bool highFreqMode = signalsGenerated;
   AddValidationResult("High-Frequency Mode", highFreqMode,
                      highFreqMode ? "High-frequency signals detected" : "No high-frequency signals",
                      0, highFreqMode ? "None" : "Enable high-frequency mode in indicator settings");
   
   IndicatorRelease(indicatorHandle);
}

//+------------------------------------------------------------------+
//| Validate EA functionality                                       |
//+------------------------------------------------------------------+
void ValidateEAFunctionality()
{
   Print("\n--- VALIDATING EA FUNCTIONALITY ---");
   
   //--- Test 1: Check if auto trading is enabled
   bool autoTradingEnabled = TerminalInfoInteger(TERMINAL_TRADE_ALLOWED) && 
                            MQLInfoInteger(MQL_TRADE_ALLOWED);
   
   AddValidationResult("Auto Trading", autoTradingEnabled,
                      autoTradingEnabled ? "Auto trading is enabled" : "Auto trading is disabled",
                      0, autoTradingEnabled ? "None" : "Enable auto trading in terminal and EA settings");
   
   //--- Test 2: Check account permissions
   bool tradingAllowed = AccountInfoInteger(ACCOUNT_TRADE_ALLOWED);
   AddValidationResult("Account Trading", tradingAllowed,
                      tradingAllowed ? "Account allows trading" : "Account trading restricted",
                      0, tradingAllowed ? "None" : "Contact broker to enable trading");
   
   //--- Test 3: Check for EA presence (by looking for its trades)
   bool eaActive = false;
   if(HistorySelect(TimeCurrent() - 3600, TimeCurrent())) // Last hour
   {
      for(int i = 0; i < HistoryDealsTotal(); i++)
      {
         ulong ticket = HistoryDealGetTicket(i);
         if(HistoryDealGetInteger(ticket, DEAL_MAGIC) == 123456) // EA magic number
         {
            eaActive = true;
            break;
         }
      }
   }
   
   AddValidationResult("EA Activity", eaActive,
                      eaActive ? "EA has executed trades recently" : "No recent EA activity",
                      0, eaActive ? "None" : "Ensure EA is attached and running");
   
   //--- Test 4: Check spread conditions
   MqlTick tick;
   bool spreadOK = false;
   if(SymbolInfoTick(_Symbol, tick))
   {
      double spread = (tick.ask - tick.bid) / _Point;
      spreadOK = (spread <= 10); // Reasonable spread for Jump 75
      
      AddValidationResult("Spread Conditions", spreadOK,
                         "Current spread: " + DoubleToString(spread, 1) + " points",
                         spread, spreadOK ? "None" : "Wait for better spread conditions");
   }
}

//+------------------------------------------------------------------+
//| Test signal generation performance                               |
//+------------------------------------------------------------------+
void TestSignalGeneration()
{
   Print("\n--- TESTING SIGNAL GENERATION ---");
   
   //--- Test signal frequency over recent bars
   int indicatorHandle = iCustom(_Symbol, PERIOD_M1, "AMPD_RealTime_Precision_Indicator");
   if(indicatorHandle == INVALID_HANDLE)
   {
      AddValidationResult("Signal Frequency Test", false, "Cannot access indicator", 0, 
                         "Fix indicator loading issues first");
      return;
   }
   
   double buyBuffer[], sellBuffer[];
   int barsToTest = MathMin(InpTestBars, Bars(_Symbol, PERIOD_M1));
   
   if(CopyBuffer(indicatorHandle, 0, 0, barsToTest, buyBuffer) == barsToTest &&
      CopyBuffer(indicatorHandle, 1, 0, barsToTest, sellBuffer) == barsToTest)
   {
      int totalSignals = 0;
      for(int i = 0; i < barsToTest; i++)
      {
         if((buyBuffer[i] != 0 && buyBuffer[i] != EMPTY_VALUE) ||
            (sellBuffer[i] != 0 && sellBuffer[i] != EMPTY_VALUE))
         {
            totalSignals++;
         }
      }
      
      //--- Calculate signals per hour
      double signalsPerHour = (double)totalSignals / barsToTest * 60; // 1-minute bars
      bool frequencyOK = (signalsPerHour >= InpExpectedSignalFreq * 0.8); // 80% of expected
      
      AddValidationResult("Signal Frequency", frequencyOK,
                         "Signals per hour: " + DoubleToString(signalsPerHour, 1) + 
                         " (Expected: " + DoubleToString(InpExpectedSignalFreq, 1) + ")",
                         signalsPerHour, 
                         frequencyOK ? "None" : "Adjust signal sensitivity or aggressive mode");
   }
   
   IndicatorRelease(indicatorHandle);
}

//+------------------------------------------------------------------+
//| Test execution speed performance                                 |
//+------------------------------------------------------------------+
void TestExecutionSpeed()
{
   Print("\n--- TESTING EXECUTION SPEED ---");
   
   //--- Simulate execution speed test
   double totalTime = 0;
   int successfulTests = 0;
   
   for(int i = 0; i < InpSpeedTestIterations; i++)
   {
      datetime startTime = GetMicrosecondCount();
      
      //--- Simulate trade preparation (price checks, calculations)
      MqlTick tick;
      if(SymbolInfoTick(_Symbol, tick))
      {
         double price = tick.ask;
         double sl = price - 20 * _Point;
         double tp = price + 40 * _Point;
         
         //--- Simulate validation checks
         bool validSpread = (tick.ask - tick.bid) / _Point <= 10;
         bool validPrice = price > 0;
         
         if(validSpread && validPrice)
         {
            successfulTests++;
         }
      }
      
      datetime endTime = GetMicrosecondCount();
      totalTime += (endTime - startTime) / 1000.0; // Convert to milliseconds
      
      Sleep(1); // Small delay between tests
   }
   
   double avgExecutionTime = totalTime / InpSpeedTestIterations;
   bool speedOK = (avgExecutionTime <= InpMaxExecutionTime);
   
   AddValidationResult("Execution Speed", speedOK,
                      "Average execution time: " + DoubleToString(avgExecutionTime, 1) + "ms" +
                      " (Target: <" + DoubleToString(InpMaxExecutionTime, 1) + "ms)",
                      avgExecutionTime,
                      speedOK ? "None" : "Optimize execution logic or improve connection");
}

//+------------------------------------------------------------------+
//| Test visual elements display                                     |
//+------------------------------------------------------------------+
void TestVisualElements()
{
   Print("\n--- TESTING VISUAL ELEMENTS ---");
   
   //--- Test chart objects creation
   long chartID = ChartID();
   
   //--- Test arrow creation
   bool arrowTest = ObjectCreate(chartID, "TestArrow", OBJ_ARROW, 0, TimeCurrent(), SymbolInfoDouble(_Symbol, SYMBOL_ASK));
   if(arrowTest)
   {
      ObjectSetInteger(chartID, "TestArrow", OBJPROP_ARROWCODE, 233);
      ObjectSetInteger(chartID, "TestArrow", OBJPROP_COLOR, clrGold);
      ObjectDelete(chartID, "TestArrow");
   }
   
   AddValidationResult("Arrow Display", arrowTest,
                      arrowTest ? "Arrows can be created and displayed" : "Arrow creation failed",
                      0, arrowTest ? "None" : "Check chart permissions and object limits");
   
   //--- Test info panel creation
   bool panelTest = ObjectCreate(chartID, "TestPanel", OBJ_RECTANGLE_LABEL, 0, 0, 0);
   if(panelTest)
   {
      ObjectSetInteger(chartID, "TestPanel", OBJPROP_XDISTANCE, 10);
      ObjectSetInteger(chartID, "TestPanel", OBJPROP_YDISTANCE, 10);
      ObjectSetInteger(chartID, "TestPanel", OBJPROP_XSIZE, 100);
      ObjectSetInteger(chartID, "TestPanel", OBJPROP_YSIZE, 50);
      ObjectDelete(chartID, "TestPanel");
   }
   
   AddValidationResult("Info Panel", panelTest,
                      panelTest ? "Info panels can be created" : "Panel creation failed",
                      0, panelTest ? "None" : "Check chart object permissions");
   
   //--- Test color scheme
   bool colorTest = true; // Colors are always available
   AddValidationResult("Color Scheme", colorTest,
                      "GOLD/LIME color scheme available",
                      0, "None");
}

//+------------------------------------------------------------------+
//| Validate system requirements                                     |
//+------------------------------------------------------------------+
void ValidateSystemRequirements()
{
   Print("\n--- VALIDATING SYSTEM REQUIREMENTS ---");
   
   //--- Test 1: Check symbol availability
   bool symbolAvailable = SymbolSelect(_Symbol, true);
   AddValidationResult("Symbol Availability", symbolAvailable,
                      symbolAvailable ? "Symbol " + _Symbol + " is available" : "Symbol not available",
                      0, symbolAvailable ? "None" : "Add symbol to Market Watch");
   
   //--- Test 2: Check timeframe data
   int bars = Bars(_Symbol, PERIOD_M1);
   bool dataAvailable = (bars >= 100);
   AddValidationResult("Historical Data", dataAvailable,
                      "Available bars: " + IntegerToString(bars),
                      bars, dataAvailable ? "None" : "Download more historical data");
   
   //--- Test 3: Check account type
   ENUM_ACCOUNT_TRADE_MODE tradeMode = (ENUM_ACCOUNT_TRADE_MODE)AccountInfoInteger(ACCOUNT_TRADE_MODE);
   bool accountOK = (tradeMode == ACCOUNT_TRADE_MODE_DEMO || tradeMode == ACCOUNT_TRADE_MODE_REAL);
   AddValidationResult("Account Type", accountOK,
                      "Account mode: " + EnumToString(tradeMode),
                      0, accountOK ? "None" : "Use demo or real account");
}

//+------------------------------------------------------------------+
//| Validate current market conditions                              |
//+------------------------------------------------------------------+
void ValidateMarketConditions()
{
   Print("\n--- VALIDATING MARKET CONDITIONS ---");
   
   //--- Check if market is open
   bool marketOpen = SymbolInfoInteger(_Symbol, SYMBOL_TRADE_MODE) == SYMBOL_TRADE_MODE_FULL;
   AddValidationResult("Market Status", marketOpen,
                      marketOpen ? "Market is open for trading" : "Market is closed",
                      0, marketOpen ? "None" : "Wait for market opening hours");
   
   //--- Check volatility (using ATR)
   int atrHandle = iATR(_Symbol, PERIOD_M1, 14);
   if(atrHandle != INVALID_HANDLE)
   {
      double atr[1];
      if(CopyBuffer(atrHandle, 0, 0, 1, atr) == 1)
      {
         double volatility = atr[0] / _Point;
         bool volatilityOK = (volatility >= 5 && volatility <= 50); // Reasonable range for Jump 75
         
         AddValidationResult("Market Volatility", volatilityOK,
                            "Current ATR: " + DoubleToString(volatility, 1) + " points",
                            volatility, 
                            volatilityOK ? "None" : "Consider adjusting TP/SL for current volatility");
      }
      IndicatorRelease(atrHandle);
   }
}

//+------------------------------------------------------------------+
//| Add validation result to array                                  |
//+------------------------------------------------------------------+
void AddValidationResult(string testName, bool passed, string details, double value, string recommendation)
{
   if(totalTests >= ArraySize(validationResults))
      ArrayResize(validationResults, totalTests + 10);

   validationResults[totalTests].testName = testName;
   validationResults[totalTests].passed = passed;
   validationResults[totalTests].details = details;
   validationResults[totalTests].value = value;
   validationResults[totalTests].recommendation = recommendation;

   totalTests++;
   if(passed) passedTests++;

   //--- Print immediate result
   string status = passed ? "✅ PASS" : "❌ FAIL";
   Print(status + " | " + testName + " | " + details);
   if(!passed && recommendation != "None")
      Print("   💡 Recommendation: " + recommendation);
}

//+------------------------------------------------------------------+
//| Generate comprehensive validation report                        |
//+------------------------------------------------------------------+
void GenerateValidationReport()
{
   string filename = "AMPD_Validation_Report_" + TimeToString(TimeCurrent(), TIME_DATE) + ".txt";
   int fileHandle = FileOpen(filename, FILE_WRITE|FILE_TXT);

   if(fileHandle == INVALID_HANDLE)
   {
      Print("ERROR: Failed to create validation report file");
      return;
   }

   //--- Write report header
   FileWrite(fileHandle, "=== AMPD HIGH-FREQUENCY TRADING SYSTEM VALIDATION REPORT ===");
   FileWrite(fileHandle, "Generated: " + TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES));
   FileWrite(fileHandle, "Symbol: " + _Symbol);
   FileWrite(fileHandle, "Account: " + IntegerToString(AccountInfoInteger(ACCOUNT_LOGIN)));
   FileWrite(fileHandle, "");

   //--- Write summary
   double successRate = totalTests > 0 ? (double)passedTests / totalTests * 100 : 0;
   FileWrite(fileHandle, "VALIDATION SUMMARY:");
   FileWrite(fileHandle, "Total Tests: " + IntegerToString(totalTests));
   FileWrite(fileHandle, "Passed: " + IntegerToString(passedTests));
   FileWrite(fileHandle, "Failed: " + IntegerToString(totalTests - passedTests));
   FileWrite(fileHandle, "Success Rate: " + DoubleToString(successRate, 1) + "%");
   FileWrite(fileHandle, "");

   //--- Write detailed results
   FileWrite(fileHandle, "DETAILED TEST RESULTS:");
   FileWrite(fileHandle, "=====================");

   for(int i = 0; i < totalTests; i++)
   {
      string status = validationResults[i].passed ? "PASS" : "FAIL";
      FileWrite(fileHandle, "");
      FileWrite(fileHandle, "Test: " + validationResults[i].testName);
      FileWrite(fileHandle, "Status: " + status);
      FileWrite(fileHandle, "Details: " + validationResults[i].details);
      if(validationResults[i].value != 0)
         FileWrite(fileHandle, "Value: " + DoubleToString(validationResults[i].value, 2));
      if(!validationResults[i].passed && validationResults[i].recommendation != "None")
         FileWrite(fileHandle, "Recommendation: " + validationResults[i].recommendation);
   }

   //--- Write system readiness assessment
   FileWrite(fileHandle, "");
   FileWrite(fileHandle, "SYSTEM READINESS ASSESSMENT:");
   FileWrite(fileHandle, "============================");

   if(successRate >= 90)
   {
      FileWrite(fileHandle, "✅ SYSTEM READY FOR LIVE TRADING");
      FileWrite(fileHandle, "All critical components are functioning correctly.");
   }
   else if(successRate >= 70)
   {
      FileWrite(fileHandle, "⚠️ SYSTEM PARTIALLY READY");
      FileWrite(fileHandle, "Some issues detected. Review failed tests before live trading.");
   }
   else
   {
      FileWrite(fileHandle, "❌ SYSTEM NOT READY");
      FileWrite(fileHandle, "Critical issues detected. Do not use for live trading until resolved.");
   }

   //--- Write recommendations
   FileWrite(fileHandle, "");
   FileWrite(fileHandle, "PRIORITY RECOMMENDATIONS:");
   FileWrite(fileHandle, "========================");

   int criticalIssues = 0;
   for(int i = 0; i < totalTests; i++)
   {
      if(!validationResults[i].passed)
      {
         criticalIssues++;
         FileWrite(fileHandle, IntegerToString(criticalIssues) + ". " + validationResults[i].testName +
                   " - " + validationResults[i].recommendation);
      }
   }

   if(criticalIssues == 0)
   {
      FileWrite(fileHandle, "No critical issues found. System is optimally configured.");
   }

   FileClose(fileHandle);
   Print("✅ Validation report saved to: " + filename);
}

//+------------------------------------------------------------------+
//| Display validation summary                                       |
//+------------------------------------------------------------------+
void DisplayValidationSummary()
{
   Print("\n=== VALIDATION SUMMARY ===");

   double successRate = totalTests > 0 ? (double)passedTests / totalTests * 100 : 0;

   Print("Total Tests Performed: ", totalTests);
   Print("Tests Passed: ", passedTests);
   Print("Tests Failed: ", totalTests - passedTests);
   Print("Success Rate: ", DoubleToString(successRate, 1), "%");

   //--- System readiness assessment
   Print("\n=== SYSTEM READINESS ===");

   if(successRate >= 90)
   {
      Print("✅ SYSTEM READY FOR LIVE TRADING");
      Print("All critical components validated successfully.");
      Print("Recommendation: Proceed with live trading with confidence.");
   }
   else if(successRate >= 70)
   {
      Print("⚠️ SYSTEM PARTIALLY READY");
      Print("Some issues detected but system is functional.");
      Print("Recommendation: Review failed tests and consider demo trading first.");
   }
   else
   {
      Print("❌ SYSTEM NOT READY FOR LIVE TRADING");
      Print("Critical issues detected that must be resolved.");
      Print("Recommendation: Fix all failed tests before proceeding.");
   }

   //--- List critical failures
   Print("\n=== CRITICAL ISSUES TO ADDRESS ===");
   bool hasCriticalIssues = false;

   for(int i = 0; i < totalTests; i++)
   {
      if(!validationResults[i].passed)
      {
         hasCriticalIssues = true;
         Print("❌ ", validationResults[i].testName, ": ", validationResults[i].details);
         if(validationResults[i].recommendation != "None")
            Print("   💡 Fix: ", validationResults[i].recommendation);
      }
   }

   if(!hasCriticalIssues)
   {
      Print("✅ No critical issues found!");
   }

   //--- Performance metrics summary
   Print("\n=== PERFORMANCE VALIDATION ===");

   for(int i = 0; i < totalTests; i++)
   {
      if(validationResults[i].testName == "Signal Frequency" && validationResults[i].value > 0)
      {
         Print("📊 Signal Frequency: ", DoubleToString(validationResults[i].value, 1), " signals/hour");
      }
      if(validationResults[i].testName == "Execution Speed" && validationResults[i].value > 0)
      {
         Print("⚡ Execution Speed: ", DoubleToString(validationResults[i].value, 1), "ms average");
      }
   }

   Print("\n=== NEXT STEPS ===");
   if(successRate >= 90)
   {
      Print("1. ✅ System validation complete");
      Print("2. 🚀 Ready for live trading on Jump 75 Index");
      Print("3. 📊 Monitor performance with AMPD Performance Analyzer");
      Print("4. 📈 Use AMPD Backtester for historical validation");
   }
   else
   {
      Print("1. 🔧 Fix all failed validation tests");
      Print("2. 🔄 Re-run system validation");
      Print("3. 📊 Test with demo account first");
      Print("4. 📈 Run backtesting before live trading");
   }
}
