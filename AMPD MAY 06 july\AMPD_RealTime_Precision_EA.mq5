//+------------------------------------------------------------------+
//|                              AMPD RealTime Precision EA.mq5     |
//|                        Copyright 2024, Arise <PERSON> |
//|                                             https://www.ampd.com |
//+------------------------------------------------------------------+
#property copyright "Arise Moroka Prince Dynasty"
#property link      "https://ampd.com"
#property description "Real-Time Precision Expert Advisor - Immediate Execution"
#property version   "3.0"

#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\OrderInfo.mqh>

//--- Input parameters for maximum precision
input group "=== TRADING SETTINGS ==="
input double InpLotSize = 0.01;                    // Lot Size
input int    InpMagicNumber = 999001;              // Magic Number
input string InpComment = "AMPD_Precision";        // Order Comment

input group "=== PRECISION STRATEGY ==="
input double InpBuyTakeProfit = 40.0;              // BUY Take Profit (points)
input double InpBuyStopLoss = 20.0;                // BUY Stop Loss (points)
input double InpSellTakeProfit = 40.0;             // SELL Take Profit (points)
input double InpSellStopLoss = 20.0;               // SELL Stop Loss (points)
input int    InpMaxConcurrentTrades = 3;           // Maximum Concurrent Trades
input bool   InpBidirectionalTrading = true;       // Enable Bidirectional Trading

input group "=== REAL-TIME EXECUTION ==="
input bool   InpImmediateExecution = true;         // Immediate Signal Execution
input bool   InpTradeEveryBar = true;              // Trade on Every Valid Signal
input bool   InpInstantExit = true;                // Instant Exit on Signal Change
input int    InpSignalValidationMS = 500;          // Signal Validation (milliseconds)

input group "=== RISK MANAGEMENT ==="
input double InpMaxRiskPercent = 2.0;              // Maximum Risk Per Trade (%)
input bool   InpUseTrailingStop = true;            // Use Trailing Stop
input double InpTrailingStopDistance = 15.0;       // Trailing Stop Distance (points)
input int    InpMaxDailyTrades = 10;               // Maximum Daily Trades
input double InpMinSignalStrength = 0.6;           // Minimum Signal Strength (0-1)

input group "=== TIME SETTINGS ==="
input bool   InpUseTradingHours = false;           // Use Trading Hours
input string InpStartTime = "00:00";               // Trading Start Time
input string InpEndTime = "23:59";                 // Trading End Time

//--- Global variables
CTrade trade;
CPositionInfo position;
COrderInfo order;

int IndicatorHandle;
datetime LastSignalTime = 0;
datetime LastTradeTime = 0;
int DailyTradeCount = 0;
datetime LastDayReset = 0;

//--- Indicator buffers for real-time signal reading
double PrecisionBuySignals[];
double PrecisionSellSignals[];
double PrecisionExitSignals[];

//--- Signal types (must match indicator)
enum ENUM_SIGNAL
{
   SIGNAL_NONE = 0,
   SIGNAL_BUY = 1,
   SIGNAL_SELL = 2,
   SIGNAL_EXIT = 3
};

//--- Real-time processing variables
datetime LastProcessTime = 0;
ENUM_SIGNAL LastSignal = SIGNAL_NONE;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   // Set trade parameters
   trade.SetExpertMagicNumber(InpMagicNumber);
   trade.SetMarginMode();
   trade.SetTypeFillingBySymbol(_Symbol);
   
   // Load the precision indicator
   IndicatorHandle = iCustom(_Symbol, PERIOD_M1, "AMPD_RealTime_Precision_Indicator");
   
   if(IndicatorHandle == INVALID_HANDLE)
   {
      Print("Error loading AMPD Real-Time Precision Indicator");
      return INIT_FAILED;
   }
   
   // Initialize arrays for real-time processing
   ArraySetAsSeries(PrecisionBuySignals, true);
   ArraySetAsSeries(PrecisionSellSignals, true);
   ArraySetAsSeries(PrecisionExitSignals, true);
   
   // Reset daily counters
   ResetDailyCounters();
   
   Print("AMPD Real-Time Precision EA initialized successfully");
   Print("Immediate Execution: ", InpImmediateExecution ? "ENABLED" : "DISABLED");
   Print("Bidirectional Trading: ", InpBidirectionalTrading ? "ENABLED" : "DISABLED");
   Print("Max Concurrent Trades: ", InpMaxConcurrentTrades);
   Print("Target: 3 high-quality trades per hour");
   
   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   if(IndicatorHandle != INVALID_HANDLE)
      IndicatorRelease(IndicatorHandle);
   
   Print("AMPD Real-Time Precision EA deinitialized");
   Print("Final Daily Trades: ", DailyTradeCount);
}

//+------------------------------------------------------------------+
//| Expert tick function with real-time processing                  |
//+------------------------------------------------------------------+
void OnTick()
{
   // Real-time processing for immediate execution
   if(InpImmediateExecution)
   {
      datetime currentTime = TimeCurrent();
      if(currentTime - LastProcessTime >= InpSignalValidationMS / 1000.0)
      {
         LastProcessTime = currentTime;
         ProcessRealTimeSignals();
      }
   }
   
   // Standard bar-based processing
   static datetime lastBarTime = 0;
   datetime currentBarTime = iTime(_Symbol, PERIOD_M1, 0);
   
   if(currentBarTime != lastBarTime || InpTradeEveryBar)
   {
      lastBarTime = currentBarTime;
      
      // Reset daily counters if new day
      CheckDailyReset();
      
      // Check trading conditions
      if(!IsTradingAllowed())
         return;
      
      // Get indicator signals
      if(!GetIndicatorSignals())
         return;
      
      // Process signals for execution
      ProcessSignalExecution();
      
      // Manage existing positions
      ManagePositions();
   }
}

//+------------------------------------------------------------------+
//| Process real-time signals for immediate execution               |
//+------------------------------------------------------------------+
void ProcessRealTimeSignals()
{
   // Get current signals from indicator
   if(!GetIndicatorSignals())
      return;
   
   // Check for new signals
   ENUM_SIGNAL currentSignal = GetCurrentSignalType();
   
   if(currentSignal != LastSignal && currentSignal != SIGNAL_NONE)
   {
      LastSignal = currentSignal;
      
      // Validate signal strength if available
      // Note: This would require additional indicator buffer for signal strength
      
      // Execute immediate trade
      if(InpImmediateExecution && IsTradingAllowed())
      {
         switch(currentSignal)
         {
            case SIGNAL_BUY:
               if(CountPositions(POSITION_TYPE_BUY) < InpMaxConcurrentTrades)
               {
                  Print("Real-Time BUY Signal Detected - Executing immediately");
                  ExecutePrecisionBuyTrade();
               }
               break;
               
            case SIGNAL_SELL:
               if(InpBidirectionalTrading && CountPositions(POSITION_TYPE_SELL) < InpMaxConcurrentTrades)
               {
                  Print("Real-Time SELL Signal Detected - Executing immediately");
                  ExecutePrecisionSellTrade();
               }
               break;
               
            case SIGNAL_EXIT:
               if(InpInstantExit)
               {
                  Print("Real-Time EXIT Signal Detected - Closing all positions");
                  CloseAllPositions();
               }
               break;
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Get indicator signals for processing                            |
//+------------------------------------------------------------------+
bool GetIndicatorSignals()
{
   // Copy indicator buffers with error checking
   if(CopyBuffer(IndicatorHandle, 0, 0, 3, PrecisionBuySignals) <= 0)
      return false;
   
   if(CopyBuffer(IndicatorHandle, 1, 0, 3, PrecisionSellSignals) <= 0)
      return false;
   
   if(CopyBuffer(IndicatorHandle, 2, 0, 3, PrecisionExitSignals) <= 0)
      return false;
   
   return true;
}

//+------------------------------------------------------------------+
//| Get current signal type from buffers                            |
//+------------------------------------------------------------------+
ENUM_SIGNAL GetCurrentSignalType()
{
   if(PrecisionBuySignals[0] != 0.0 && PrecisionBuySignals[0] != EMPTY_VALUE)
      return SIGNAL_BUY;
   
   if(PrecisionSellSignals[0] != 0.0 && PrecisionSellSignals[0] != EMPTY_VALUE)
      return SIGNAL_SELL;
   
   if(PrecisionExitSignals[0] != 0.0 && PrecisionExitSignals[0] != EMPTY_VALUE)
      return SIGNAL_EXIT;
   
   return SIGNAL_NONE;
}

//+------------------------------------------------------------------+
//| Process signal execution with validation                        |
//+------------------------------------------------------------------+
void ProcessSignalExecution()
{
   ENUM_SIGNAL signal = GetCurrentSignalType();
   
   if(signal == SIGNAL_BUY)
   {
      if(CountPositions(POSITION_TYPE_BUY) < InpMaxConcurrentTrades)
      {
         Print("Precision BUY Signal Confirmed - Executing trade");
         ExecutePrecisionBuyTrade();
      }
   }
   else if(signal == SIGNAL_SELL && InpBidirectionalTrading)
   {
      if(CountPositions(POSITION_TYPE_SELL) < InpMaxConcurrentTrades)
      {
         Print("Precision SELL Signal Confirmed - Executing trade");
         ExecutePrecisionSellTrade();
      }
   }
   else if(signal == SIGNAL_EXIT)
   {
      Print("Precision EXIT Signal Confirmed - Closing positions");
      CloseAllPositions();
   }
}

//+------------------------------------------------------------------+
//| Execute precision BUY trade                                     |
//+------------------------------------------------------------------+
void ExecutePrecisionBuyTrade()
{
   double price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   double sl = price - InpBuyStopLoss * SymbolInfoDouble(_Symbol, SYMBOL_POINT);
   double tp = price + InpBuyTakeProfit * SymbolInfoDouble(_Symbol, SYMBOL_POINT);
   
   // Calculate lot size based on risk
   double lotSize = CalculateLotSize(InpBuyStopLoss);
   
   if(trade.Buy(lotSize, _Symbol, price, sl, tp, InpComment + "_BUY"))
   {
      Print("Precision BUY order executed: ", trade.ResultOrder());
      Print("Price: ", price, " | SL: ", sl, " | TP: ", tp, " | Lot: ", lotSize);
      DailyTradeCount++;
      LastTradeTime = TimeCurrent();
   }
   else
   {
      Print("Precision BUY order failed: ", trade.ResultRetcodeDescription());
   }
}

//+------------------------------------------------------------------+
//| Execute precision SELL trade                                    |
//+------------------------------------------------------------------+
void ExecutePrecisionSellTrade()
{
   double price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
   double sl = price + InpSellStopLoss * SymbolInfoDouble(_Symbol, SYMBOL_POINT);
   double tp = price - InpSellTakeProfit * SymbolInfoDouble(_Symbol, SYMBOL_POINT);
   
   // Calculate lot size based on risk
   double lotSize = CalculateLotSize(InpSellStopLoss);
   
   if(trade.Sell(lotSize, _Symbol, price, sl, tp, InpComment + "_SELL"))
   {
      Print("Precision SELL order executed: ", trade.ResultOrder());
      Print("Price: ", price, " | SL: ", sl, " | TP: ", tp, " | Lot: ", lotSize);
      DailyTradeCount++;
      LastTradeTime = TimeCurrent();
   }
   else
   {
      Print("Precision SELL order failed: ", trade.ResultRetcodeDescription());
   }
}

//+------------------------------------------------------------------+
//| Calculate lot size based on risk management                     |
//+------------------------------------------------------------------+
double CalculateLotSize(double stopLossPoints)
{
   double accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
   double riskAmount = accountBalance * InpMaxRiskPercent / 100.0;

   double pointValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
   double pointSize = SymbolInfoDouble(_Symbol, SYMBOL_POINT);

   if(pointValue == 0 || pointSize == 0)
      return InpLotSize;

   double stopLossValue = stopLossPoints * pointSize;
   double lotSize = riskAmount / (stopLossValue * pointValue / pointSize);

   // Normalize lot size
   double minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
   double maxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
   double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

   lotSize = MathMax(minLot, MathMin(maxLot, lotSize));
   lotSize = NormalizeDouble(lotSize / lotStep, 0) * lotStep;

   return lotSize;
}

//+------------------------------------------------------------------+
//| Count positions by type                                         |
//+------------------------------------------------------------------+
int CountPositions(ENUM_POSITION_TYPE posType)
{
   int count = 0;

   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(position.SelectByIndex(i))
      {
         if(position.Magic() == InpMagicNumber && position.Symbol() == _Symbol)
         {
            if(position.PositionType() == posType)
               count++;
         }
      }
   }

   return count;
}

//+------------------------------------------------------------------+
//| Close all positions immediately                                 |
//+------------------------------------------------------------------+
void CloseAllPositions()
{
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(position.SelectByIndex(i))
      {
         if(position.Magic() == InpMagicNumber && position.Symbol() == _Symbol)
         {
            trade.PositionClose(position.Ticket());
            Print("Precision position closed: ", position.Ticket());
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Check if trading is allowed                                     |
//+------------------------------------------------------------------+
bool IsTradingAllowed()
{
   // Check daily trade limit
   if(DailyTradeCount >= InpMaxDailyTrades)
      return false;

   // Check trading hours
   if(InpUseTradingHours)
   {
      MqlDateTime currentTime;
      TimeToStruct(TimeCurrent(), currentTime);

      int currentMinutes = currentTime.hour * 60 + currentTime.min;

      string startParts[];
      string endParts[];
      StringSplit(InpStartTime, ':', startParts);
      StringSplit(InpEndTime, ':', endParts);

      int startMinutes = (int)StringToInteger(startParts[0]) * 60 + (int)StringToInteger(startParts[1]);
      int endMinutes = (int)StringToInteger(endParts[0]) * 60 + (int)StringToInteger(endParts[1]);

      if(currentMinutes < startMinutes || currentMinutes > endMinutes)
         return false;
   }

   return true;
}

//+------------------------------------------------------------------+
//| Manage existing positions with trailing stops                   |
//+------------------------------------------------------------------+
void ManagePositions()
{
   if(!InpUseTrailingStop)
      return;

   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(position.SelectByIndex(i))
      {
         if(position.Magic() == InpMagicNumber && position.Symbol() == _Symbol)
         {
            UpdateTrailingStop();
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Update trailing stop for current position                       |
//+------------------------------------------------------------------+
void UpdateTrailingStop()
{
   double currentPrice;
   double newStopLoss;
   double point = SymbolInfoDouble(position.Symbol(), SYMBOL_POINT);
   double trailingDistance = InpTrailingStopDistance * point;

   if(position.PositionType() == POSITION_TYPE_BUY)
   {
      currentPrice = SymbolInfoDouble(position.Symbol(), SYMBOL_BID);
      newStopLoss = currentPrice - trailingDistance;

      if(newStopLoss > position.StopLoss() + point)
      {
         trade.PositionModify(position.Ticket(), newStopLoss, position.TakeProfit());
         Print("Trailing stop updated for BUY position: ", position.Ticket());
      }
   }
   else if(position.PositionType() == POSITION_TYPE_SELL)
   {
      currentPrice = SymbolInfoDouble(position.Symbol(), SYMBOL_ASK);
      newStopLoss = currentPrice + trailingDistance;

      if(newStopLoss < position.StopLoss() - point || position.StopLoss() == 0)
      {
         trade.PositionModify(position.Ticket(), newStopLoss, position.TakeProfit());
         Print("Trailing stop updated for SELL position: ", position.Ticket());
      }
   }
}

//+------------------------------------------------------------------+
//| Check and reset daily counters                                  |
//+------------------------------------------------------------------+
void CheckDailyReset()
{
   MqlDateTime currentTime, lastResetTime;
   TimeToStruct(TimeCurrent(), currentTime);
   TimeToStruct(LastDayReset, lastResetTime);

   if(currentTime.day != lastResetTime.day)
      ResetDailyCounters();
}

//+------------------------------------------------------------------+
//| Reset daily counters                                            |
//+------------------------------------------------------------------+
void ResetDailyCounters()
{
   DailyTradeCount = 0;
   LastDayReset = TimeCurrent();
   Print("Daily counters reset for Precision EA");
}
