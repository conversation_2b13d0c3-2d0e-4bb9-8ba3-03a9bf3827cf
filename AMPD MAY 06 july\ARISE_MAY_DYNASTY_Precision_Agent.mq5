//+------------------------------------------------------------------+
//|                          ARISE MAY DYNASTY Precision Agent.mq5  |
//|                        Copyright 2024, Arise <PERSON> |
//|                           Elite Trading Technology - www.ampd.com |
//+------------------------------------------------------------------+
#property copyright "Arise Moroka Prince Dynasty - Elite Trading Technology"
#property link      "https://ampd.com"
#property description "ARISE-MAY-DYNASTY Autonomous Precision Trading Agent"
#property version   "4.0"

#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\OrderInfo.mqh>

//--- Input parameters for autonomous operation
input group "=== ARISE-MAY-DYNASTY CORE SETTINGS ==="
input double InpLotSize = 0.01;                    // Base Lot Size
input int    InpMagicNumber = 777001;              // Magic Number
input string InpComment = "ARISE-MAY-DYNASTY";     // Order Comment

input group "=== AUTONOMOUS EXECUTION SETTINGS ==="
input bool   InpGuaranteedExecution = true;        // Guaranteed Trade Execution (100%)
input int    InpExecutionSpeedMS = 50;             // Max Execution Speed (milliseconds)
input bool   InpAutoRefresh = true;                // Auto-Refresh System (50ms)
input bool   InpIntelligentPositionMgmt = true;    // Intelligent Position Management
input bool   InpOppositeSignalClose = true;        // Close on Opposite Signal

input group "=== PRECISION STRATEGY SETTINGS ==="
input double InpBuyTakeProfit = 40.0;              // BUY Take Profit (points)
input double InpBuyStopLoss = 20.0;                // BUY Stop Loss (points)
input double InpSellTakeProfit = 40.0;             // SELL Take Profit (points)
input double InpSellStopLoss = 20.0;               // SELL Stop Loss (points)
input int    InpMaxConcurrentTrades = 3;           // Maximum Concurrent Trades

input group "=== AUTONOMOUS RISK MANAGEMENT ==="
input double InpMaxRiskPercent = 2.0;              // Maximum Risk Per Trade (%)
input bool   InpAutoRiskAdjustment = true;         // Auto-Risk Adjustment
input bool   InpAutoDrawdownProtection = true;     // Auto-Drawdown Protection
input double InpMaxDrawdownPercent = 5.0;          // Maximum Drawdown (%)
input bool   InpAutoProfitScaling = true;          // Auto-Profit Scaling
input int    InpWinStreakThreshold = 3;            // Win Streak for Scaling

input group "=== AUTONOMOUS MONITORING ==="
input bool   InpAutoSpreadMonitoring = true;       // Auto-Spread Monitoring
input double InpMaxSpreadPoints = 3.0;             // Maximum Spread (points)
input bool   InpAutoSessionManagement = true;      // Auto-Session Management
input bool   InpAutoMarginMonitoring = true;       // Auto-Margin Monitoring
input double InpMinMarginLevel = 200.0;            // Minimum Margin Level (%)

input group "=== INTELLIGENT ALERTS ==="
input bool   InpSmartAlerts = true;                // Smart Alert System
input bool   InpVoiceAlerts = false;               // Voice Alerts
input bool   InpConsoleLogging = true;             // Real-Time Console Logging
input bool   InpPerformanceDashboard = true;       // Performance Dashboard

//--- Global variables for autonomous operation
CTrade trade;
CPositionInfo position;
COrderInfo order;

int IndicatorHandle;
datetime LastRefreshTime = 0;
datetime LastSignalTime = 0;
datetime LastTradeTime = 0;
int DailyTradeCount = 0;
datetime LastDayReset = 0;

//--- Autonomous monitoring variables
double CurrentDrawdown = 0;
int ConsecutiveWins = 0;
int ConsecutiveLosses = 0;
double DynamicLotSize = 0;
bool TradingPaused = false;
string PauseReason = "";

//--- Performance tracking variables
int TotalTrades = 0;
int WinningTrades = 0;
double TotalProfit = 0;
double CurrentWinRate = 0;
double SignalsPerHour = 0;
datetime SessionStartTime = 0;

//--- Enhanced signal buffers for comprehensive trading data
double PrecisionBuySignals[];      // Buffer 0: BUY entry signals
double PrecisionSellSignals[];     // Buffer 1: SELL entry signals
double PrecisionExitSignals[];     // Buffer 2: EXIT signals
double SignalStrengthData[];       // Buffer 3: Signal strength values
double EntryPriceData[];           // Buffer 4: Entry price levels
double TakeProfitData[];           // Buffer 5: Take profit levels
double StopLossData[];             // Buffer 6: Stop loss levels

//--- Signal types (must match indicator)
enum ENUM_SIGNAL
{
   SIGNAL_NONE = 0,
   SIGNAL_BUY = 1,
   SIGNAL_SELL = 2,
   SIGNAL_EXIT = 3
};

//--- Autonomous state tracking
ENUM_SIGNAL LastDetectedSignal = SIGNAL_NONE;
datetime LastSignalDetectionTime = 0;
bool SignalExecutionInProgress = false;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   Print("ARISE-MAY-DYNASTY: Initializing Autonomous Precision Trading Agent...");
   
   // Set trade parameters for guaranteed execution
   trade.SetExpertMagicNumber(InpMagicNumber);
   trade.SetMarginMode();
   trade.SetTypeFillingBySymbol(_Symbol);
   trade.SetDeviationInPoints(10); // Allow slippage for guaranteed execution
   
   // Load the precision indicator
   IndicatorHandle = iCustom(_Symbol, PERIOD_M1, "ARISE_MAY_DYNASTY_Signal_Engine");
   
   if(IndicatorHandle == INVALID_HANDLE)
   {
      Print("ARISE-MAY-DYNASTY: ERROR - Cannot load precision indicator");
      return INIT_FAILED;
   }
   
   // Initialize arrays for enhanced signal detection
   ArraySetAsSeries(PrecisionBuySignals, true);
   ArraySetAsSeries(PrecisionSellSignals, true);
   ArraySetAsSeries(PrecisionExitSignals, true);
   ArraySetAsSeries(SignalStrengthData, true);
   ArraySetAsSeries(EntryPriceData, true);
   ArraySetAsSeries(TakeProfitData, true);
   ArraySetAsSeries(StopLossData, true);
   
   // Initialize autonomous systems
   InitializeAutonomousSystems();
   
   // Reset performance tracking
   SessionStartTime = TimeCurrent();
   ResetDailyCounters();
   
   Print("ARISE-MAY-DYNASTY: Autonomous Trading Agent initialized successfully");
   Print("ARISE-MAY-DYNASTY: Guaranteed Execution: ", InpGuaranteedExecution ? "ENABLED" : "DISABLED");
   Print("ARISE-MAY-DYNASTY: Auto-Refresh Rate: ", InpExecutionSpeedMS, "ms");
   Print("ARISE-MAY-DYNASTY: Intelligent Position Management: ", InpIntelligentPositionMgmt ? "ACTIVE" : "INACTIVE");
   Print("ARISE-MAY-DYNASTY: System Status: FULLY AUTONOMOUS");
   
   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   if(IndicatorHandle != INVALID_HANDLE)
      IndicatorRelease(IndicatorHandle);
   
   // Generate final performance report
   GeneratePerformanceReport();
   
   Print("ARISE-MAY-DYNASTY: Autonomous Trading Agent deinitialized");
   Print("ARISE-MAY-DYNASTY: Final Session Statistics:");
   Print("ARISE-MAY-DYNASTY: Total Trades: ", TotalTrades);
   Print("ARISE-MAY-DYNASTY: Win Rate: ", DoubleToString(CurrentWinRate, 2), "%");
   Print("ARISE-MAY-DYNASTY: Total Profit: ", DoubleToString(TotalProfit, 2));
}

//+------------------------------------------------------------------+
//| Expert tick function with autonomous processing                  |
//+------------------------------------------------------------------+
void OnTick()
{
   // Autonomous refresh system for guaranteed signal detection
   if(InpAutoRefresh)
   {
      datetime currentTime = TimeCurrent();
      if(currentTime - LastRefreshTime >= InpExecutionSpeedMS / 1000.0)
      {
         LastRefreshTime = currentTime;
         ProcessAutonomousSignalDetection();
      }
   }
   
   // Standard processing for additional validation
   static datetime lastBarTime = 0;
   datetime currentBarTime = iTime(_Symbol, PERIOD_M1, 0);
   
   if(currentBarTime != lastBarTime)
   {
      lastBarTime = currentBarTime;
      
      // Autonomous system maintenance
      PerformAutonomousMaintenance();
      
      // Check if trading is allowed
      if(!IsAutonomousTradingAllowed())
         return;
      
      // Get and process signals
      if(GetIndicatorSignals())
      {
         ProcessSignalExecution();
      }
      
      // Manage existing positions
      ManageAutonomousPositions();
      
      // Update performance dashboard
      if(InpPerformanceDashboard)
         UpdatePerformanceDashboard();
   }
}

//+------------------------------------------------------------------+
//| Initialize autonomous systems                                    |
//+------------------------------------------------------------------+
void InitializeAutonomousSystems()
{
   // Calculate dynamic lot size based on account
   DynamicLotSize = CalculateOptimalLotSize();
   
   // Initialize performance tracking
   TotalTrades = 0;
   WinningTrades = 0;
   TotalProfit = 0;
   CurrentWinRate = 0;
   
   // Reset autonomous state
   TradingPaused = false;
   PauseReason = "";
   SignalExecutionInProgress = false;
   
   Print("ARISE-MAY-DYNASTY: Autonomous systems initialized");
   Print("ARISE-MAY-DYNASTY: Dynamic lot size calculated: ", DoubleToString(DynamicLotSize, 2));
}

//+------------------------------------------------------------------+
//| Process autonomous signal detection with guaranteed execution    |
//+------------------------------------------------------------------+
void ProcessAutonomousSignalDetection()
{
   if(SignalExecutionInProgress) return; // Prevent concurrent execution
   
   // Get real-time signals
   if(!GetIndicatorSignals()) return;
   
   // Detect current signal
   ENUM_SIGNAL currentSignal = GetCurrentSignalType();
   
   if(currentSignal != SIGNAL_NONE && currentSignal != LastDetectedSignal)
   {
      LastDetectedSignal = currentSignal;
      LastSignalDetectionTime = TimeCurrent();
      
      if(InpConsoleLogging)
      {
         Print("ARISE-MAY-DYNASTY: Signal detected - Type: ", EnumToString(currentSignal), 
               " at ", TimeToString(TimeCurrent(), TIME_SECONDS));
      }
      
      // Execute with guaranteed execution
      if(InpGuaranteedExecution && IsAutonomousTradingAllowed())
      {
         ExecuteGuaranteedTrade(currentSignal);
      }
   }
}

//+------------------------------------------------------------------+
//| Execute guaranteed trade with bulletproof execution             |
//+------------------------------------------------------------------+
void ExecuteGuaranteedTrade(ENUM_SIGNAL signal)
{
   SignalExecutionInProgress = true;
   
   switch(signal)
   {
      case SIGNAL_BUY:
         if(CountPositions(POSITION_TYPE_BUY) < InpMaxConcurrentTrades)
         {
            // Close opposite positions if enabled
            if(InpOppositeSignalClose)
               ClosePositionsByType(POSITION_TYPE_SELL);
            
            ExecuteGuaranteedBuyOrder();
         }
         break;
         
      case SIGNAL_SELL:
         if(CountPositions(POSITION_TYPE_SELL) < InpMaxConcurrentTrades)
         {
            // Close opposite positions if enabled
            if(InpOppositeSignalClose)
               ClosePositionsByType(POSITION_TYPE_BUY);
            
            ExecuteGuaranteedSellOrder();
         }
         break;
         
      case SIGNAL_EXIT:
         if(InpIntelligentPositionMgmt)
         {
            CloseAllPositions();
            Print("ARISE-MAY-DYNASTY: All positions closed on EXIT signal");
         }
         break;
   }
   
   SignalExecutionInProgress = false;
}

//+------------------------------------------------------------------+
//| Execute guaranteed BUY order with bulletproof execution         |
//+------------------------------------------------------------------+
void ExecuteGuaranteedBuyOrder()
{
   double price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   double sl, tp;

   // Use indicator's enhanced TP/SL data if available
   if(ArraySize(TakeProfitData) > 0 && TakeProfitData[0] != 0.0 && TakeProfitData[0] != EMPTY_VALUE)
   {
      tp = TakeProfitData[0];
      if(InpConsoleLogging)
         Print("ARISE-MAY-DYNASTY: Using indicator TP: ", DoubleToString(tp, _Digits));
   }
   else
   {
      tp = price + InpBuyTakeProfit * SymbolInfoDouble(_Symbol, SYMBOL_POINT);
   }

   if(ArraySize(StopLossData) > 0 && StopLossData[0] != 0.0 && StopLossData[0] != EMPTY_VALUE)
   {
      sl = StopLossData[0];
      if(InpConsoleLogging)
         Print("ARISE-MAY-DYNASTY: Using indicator SL: ", DoubleToString(sl, _Digits));
   }
   else
   {
      sl = price - InpBuyStopLoss * SymbolInfoDouble(_Symbol, SYMBOL_POINT);
   }

   // Use dynamic lot size for autonomous risk management
   double lotSize = InpAutoRiskAdjustment ? DynamicLotSize : InpLotSize;

   // Attempt execution with multiple retries for guaranteed execution
   int attempts = 0;
   bool success = false;

   while(!success && attempts < 5)
   {
      attempts++;

      if(trade.Buy(lotSize, _Symbol, price, sl, tp, InpComment + "_BUY"))
      {
         success = true;
         TotalTrades++;
         DailyTradeCount++;
         LastTradeTime = TimeCurrent();

         if(InpConsoleLogging)
         {
            Print("ARISE-MAY-DYNASTY: BUY order executed at ", DoubleToString(price, _Digits));
            Print("ARISE-MAY-DYNASTY: Order ID: ", trade.ResultOrder(), " | Lot: ", DoubleToString(lotSize, 2));
            Print("ARISE-MAY-DYNASTY: SL: ", DoubleToString(sl, _Digits), " | TP: ", DoubleToString(tp, _Digits));
         }

         // Smart alerts
         if(InpSmartAlerts)
         {
            string alertMsg = "ARISE-MAY-DYNASTY: 🟡 GOLD ARROW BUY EXECUTED at " + DoubleToString(price, _Digits);
            Alert(alertMsg);
            if(InpVoiceAlerts) PlaySound("alert2.wav");
         }
      }
      else
      {
         Print("ARISE-MAY-DYNASTY: BUY order attempt ", attempts, " failed: ", trade.ResultRetcodeDescription());
         Sleep(50); // Brief pause before retry
         price = SymbolInfoDouble(_Symbol, SYMBOL_ASK); // Refresh price
      }
   }

   if(!success)
   {
      Print("ARISE-MAY-DYNASTY: CRITICAL ERROR - Failed to execute BUY order after 5 attempts");
      TriggerEmergencyProtocol();
   }
}

//+------------------------------------------------------------------+
//| Execute guaranteed SELL order with bulletproof execution        |
//+------------------------------------------------------------------+
void ExecuteGuaranteedSellOrder()
{
   double price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
   double sl, tp;

   // Use indicator's enhanced TP/SL data if available
   if(ArraySize(TakeProfitData) > 0 && TakeProfitData[0] != 0.0 && TakeProfitData[0] != EMPTY_VALUE)
   {
      tp = TakeProfitData[0];
      if(InpConsoleLogging)
         Print("ARISE-MAY-DYNASTY: Using indicator TP: ", DoubleToString(tp, _Digits));
   }
   else
   {
      tp = price - InpSellTakeProfit * SymbolInfoDouble(_Symbol, SYMBOL_POINT);
   }

   if(ArraySize(StopLossData) > 0 && StopLossData[0] != 0.0 && StopLossData[0] != EMPTY_VALUE)
   {
      sl = StopLossData[0];
      if(InpConsoleLogging)
         Print("ARISE-MAY-DYNASTY: Using indicator SL: ", DoubleToString(sl, _Digits));
   }
   else
   {
      sl = price + InpSellStopLoss * SymbolInfoDouble(_Symbol, SYMBOL_POINT);
   }

   // Use dynamic lot size for autonomous risk management
   double lotSize = InpAutoRiskAdjustment ? DynamicLotSize : InpLotSize;

   // Attempt execution with multiple retries for guaranteed execution
   int attempts = 0;
   bool success = false;

   while(!success && attempts < 5)
   {
      attempts++;

      if(trade.Sell(lotSize, _Symbol, price, sl, tp, InpComment + "_SELL"))
      {
         success = true;
         TotalTrades++;
         DailyTradeCount++;
         LastTradeTime = TimeCurrent();

         if(InpConsoleLogging)
         {
            Print("ARISE-MAY-DYNASTY: SELL order executed at ", DoubleToString(price, _Digits));
            Print("ARISE-MAY-DYNASTY: Order ID: ", trade.ResultOrder(), " | Lot: ", DoubleToString(lotSize, 2));
            Print("ARISE-MAY-DYNASTY: SL: ", DoubleToString(sl, _Digits), " | TP: ", DoubleToString(tp, _Digits));
         }

         // Smart alerts
         if(InpSmartAlerts)
         {
            string alertMsg = "ARISE-MAY-DYNASTY: 🟢 LIME ARROW SELL EXECUTED at " + DoubleToString(price, _Digits);
            Alert(alertMsg);
            if(InpVoiceAlerts) PlaySound("alert2.wav");
         }
      }
      else
      {
         Print("ARISE-MAY-DYNASTY: SELL order attempt ", attempts, " failed: ", trade.ResultRetcodeDescription());
         Sleep(50); // Brief pause before retry
         price = SymbolInfoDouble(_Symbol, SYMBOL_BID); // Refresh price
      }
   }

   if(!success)
   {
      Print("ARISE-MAY-DYNASTY: CRITICAL ERROR - Failed to execute SELL order after 5 attempts");
      TriggerEmergencyProtocol();
   }
}

//+------------------------------------------------------------------+
//| Get indicator signals for bulletproof detection                 |
//+------------------------------------------------------------------+
bool GetIndicatorSignals()
{
   // Copy all enhanced indicator buffers with comprehensive error checking
   if(CopyBuffer(IndicatorHandle, 0, 0, 3, PrecisionBuySignals) <= 0)
   {
      Print("ARISE-MAY-DYNASTY: ERROR - Failed to copy BUY signals buffer");
      return false;
   }

   if(CopyBuffer(IndicatorHandle, 1, 0, 3, PrecisionSellSignals) <= 0)
   {
      Print("ARISE-MAY-DYNASTY: ERROR - Failed to copy SELL signals buffer");
      return false;
   }

   if(CopyBuffer(IndicatorHandle, 2, 0, 3, PrecisionExitSignals) <= 0)
   {
      Print("ARISE-MAY-DYNASTY: ERROR - Failed to copy EXIT signals buffer");
      return false;
   }

   // Copy additional enhanced data buffers
   if(CopyBuffer(IndicatorHandle, 3, 0, 3, SignalStrengthData) <= 0)
   {
      Print("ARISE-MAY-DYNASTY: WARNING - Failed to copy signal strength buffer");
   }

   if(CopyBuffer(IndicatorHandle, 4, 0, 3, EntryPriceData) <= 0)
   {
      Print("ARISE-MAY-DYNASTY: WARNING - Failed to copy entry price buffer");
   }

   if(CopyBuffer(IndicatorHandle, 5, 0, 3, TakeProfitData) <= 0)
   {
      Print("ARISE-MAY-DYNASTY: WARNING - Failed to copy take profit buffer");
   }

   if(CopyBuffer(IndicatorHandle, 6, 0, 3, StopLossData) <= 0)
   {
      Print("ARISE-MAY-DYNASTY: WARNING - Failed to copy stop loss buffer");
   }

   return true;
}

//+------------------------------------------------------------------+
//| Get current signal type from buffers                            |
//+------------------------------------------------------------------+
ENUM_SIGNAL GetCurrentSignalType()
{
   // Enhanced signal detection with proper EMPTY_VALUE checking
   // Check for BUY signal (GOLD arrows)
   if(PrecisionBuySignals[0] != EMPTY_VALUE && PrecisionBuySignals[0] != 0.0)
   {
      if(InpConsoleLogging)
         Print("ARISE-MAY-DYNASTY: BUY signal detected - Value: ", DoubleToString(PrecisionBuySignals[0], _Digits));
      return SIGNAL_BUY;
   }

   // Check for SELL signal (LIME arrows)
   if(PrecisionSellSignals[0] != EMPTY_VALUE && PrecisionSellSignals[0] != 0.0)
   {
      if(InpConsoleLogging)
         Print("ARISE-MAY-DYNASTY: SELL signal detected - Value: ", DoubleToString(PrecisionSellSignals[0], _Digits));
      return SIGNAL_SELL;
   }

   // Check for EXIT signal
   if(PrecisionExitSignals[0] != EMPTY_VALUE && PrecisionExitSignals[0] != 0.0)
   {
      if(InpConsoleLogging)
         Print("ARISE-MAY-DYNASTY: EXIT signal detected - Value: ", DoubleToString(PrecisionExitSignals[0], _Digits));
      return SIGNAL_EXIT;
   }

   return SIGNAL_NONE;
}

//+------------------------------------------------------------------+
//| Process signal execution with validation                        |
//+------------------------------------------------------------------+
void ProcessSignalExecution()
{
   ENUM_SIGNAL signal = GetCurrentSignalType();

   if(signal != SIGNAL_NONE)
   {
      if(InpConsoleLogging)
      {
         Print("ARISE-MAY-DYNASTY: Processing signal execution - Type: ", EnumToString(signal));
      }

      ExecuteGuaranteedTrade(signal);
   }
}

//+------------------------------------------------------------------+
//| Calculate optimal lot size for autonomous risk management       |
//+------------------------------------------------------------------+
double CalculateOptimalLotSize()
{
   double accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
   double riskAmount = accountBalance * InpMaxRiskPercent / 100.0;

   // Adjust for current drawdown if auto-protection is enabled
   if(InpAutoDrawdownProtection && CurrentDrawdown > 0)
   {
      double drawdownFactor = 1.0 - (CurrentDrawdown / 100.0);
      riskAmount *= drawdownFactor;
   }

   // Scale up for winning streaks if enabled
   if(InpAutoProfitScaling && ConsecutiveWins >= InpWinStreakThreshold)
   {
      double scaleFactor = 1.0 + (ConsecutiveWins - InpWinStreakThreshold) * 0.1;
      scaleFactor = MathMin(scaleFactor, 2.0); // Cap at 2x
      riskAmount *= scaleFactor;
   }

   double pointValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
   double pointSize = SymbolInfoDouble(_Symbol, SYMBOL_POINT);

   if(pointValue == 0 || pointSize == 0)
      return InpLotSize;

   double stopLossValue = InpBuyStopLoss * pointSize;
   double lotSize = riskAmount / (stopLossValue * pointValue / pointSize);

   // Normalize lot size
   double minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
   double maxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
   double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

   lotSize = MathMax(minLot, MathMin(maxLot, lotSize));
   lotSize = NormalizeDouble(lotSize / lotStep, 0) * lotStep;

   return lotSize;
}

//+------------------------------------------------------------------+
//| Check if autonomous trading is allowed                          |
//+------------------------------------------------------------------+
bool IsAutonomousTradingAllowed()
{
   // Check if trading is paused
   if(TradingPaused)
   {
      if(InpConsoleLogging)
         Print("ARISE-MAY-DYNASTY: Trading paused - Reason: ", PauseReason);
      return false;
   }

   // Check spread conditions
   if(InpAutoSpreadMonitoring)
   {
      double spread = SymbolInfoInteger(_Symbol, SYMBOL_SPREAD) * SymbolInfoDouble(_Symbol, SYMBOL_POINT);
      if(spread > InpMaxSpreadPoints * SymbolInfoDouble(_Symbol, SYMBOL_POINT))
      {
         if(InpConsoleLogging)
            Print("ARISE-MAY-DYNASTY: Trading paused - Spread too high: ", DoubleToString(spread, _Digits));
         return false;
      }
   }

   // Check margin level
   if(InpAutoMarginMonitoring)
   {
      double marginLevel = AccountInfoDouble(ACCOUNT_MARGIN_LEVEL);
      if(marginLevel < InpMinMarginLevel && marginLevel > 0)
      {
         TradingPaused = true;
         PauseReason = "Low margin level: " + DoubleToString(marginLevel, 2) + "%";
         Print("ARISE-MAY-DYNASTY: Trading paused - ", PauseReason);
         return false;
      }
   }

   // Check drawdown protection
   if(InpAutoDrawdownProtection)
   {
      UpdateCurrentDrawdown();
      if(CurrentDrawdown > InpMaxDrawdownPercent)
      {
         TradingPaused = true;
         PauseReason = "Maximum drawdown exceeded: " + DoubleToString(CurrentDrawdown, 2) + "%";
         Print("ARISE-MAY-DYNASTY: Trading paused - ", PauseReason);
         return false;
      }
   }

   return true;
}

//+------------------------------------------------------------------+
//| Manage autonomous positions                                      |
//+------------------------------------------------------------------+
void ManageAutonomousPositions()
{
   // Update position statistics
   UpdatePositionStatistics();

   // Check for positions to close based on intelligent management
   if(InpIntelligentPositionMgmt)
   {
      for(int i = PositionsTotal() - 1; i >= 0; i--)
      {
         if(position.SelectByIndex(i))
         {
            if(position.Symbol() == _Symbol && position.Magic() == InpMagicNumber)
            {
               // Check for time-based closure or other intelligent criteria
               CheckIntelligentPositionClosure(position.Ticket());
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Check intelligent position closure criteria                     |
//+------------------------------------------------------------------+
void CheckIntelligentPositionClosure(ulong ticket)
{
   if(!position.SelectByTicket(ticket)) return;

   datetime openTime = position.Time();
   datetime currentTime = TimeCurrent();

   // Time-based closure (optional - can be configured)
   int positionAgeMinutes = (int)((currentTime - openTime) / 60);

   // Example: Close positions older than 60 minutes if not profitable
   if(positionAgeMinutes > 60 && position.Profit() <= 0)
   {
      if(trade.PositionClose(ticket))
      {
         Print("ARISE-MAY-DYNASTY: Position closed due to time limit - Ticket: ", ticket);
      }
   }
}

//+------------------------------------------------------------------+
//| Count positions by type                                         |
//+------------------------------------------------------------------+
int CountPositions(ENUM_POSITION_TYPE type)
{
   int count = 0;
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(position.SelectByIndex(i))
      {
         if(position.Symbol() == _Symbol &&
            position.Magic() == InpMagicNumber &&
            position.PositionType() == type)
         {
            count++;
         }
      }
   }
   return count;
}

//+------------------------------------------------------------------+
//| Close positions by type                                         |
//+------------------------------------------------------------------+
void ClosePositionsByType(ENUM_POSITION_TYPE type)
{
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(position.SelectByIndex(i))
      {
         if(position.Symbol() == _Symbol &&
            position.Magic() == InpMagicNumber &&
            position.PositionType() == type)
         {
            if(trade.PositionClose(position.Ticket()))
            {
               Print("ARISE-MAY-DYNASTY: ", EnumToString(type), " position closed - Ticket: ", position.Ticket());
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Close all positions                                             |
//+------------------------------------------------------------------+
void CloseAllPositions()
{
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(position.SelectByIndex(i))
      {
         if(position.Symbol() == _Symbol && position.Magic() == InpMagicNumber)
         {
            if(trade.PositionClose(position.Ticket()))
            {
               Print("ARISE-MAY-DYNASTY: Position closed - Ticket: ", position.Ticket(),
                     " | Profit: ", DoubleToString(position.Profit(), 2));
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Perform autonomous maintenance                                   |
//+------------------------------------------------------------------+
void PerformAutonomousMaintenance()
{
   // Reset daily counters if new day
   CheckDailyReset();

   // Update dynamic lot size
   if(InpAutoRiskAdjustment)
   {
      DynamicLotSize = CalculateOptimalLotSize();
   }

   // Check if trading should be resumed
   if(TradingPaused && ShouldResumeTradingAutomatically())
   {
      TradingPaused = false;
      PauseReason = "";
      Print("ARISE-MAY-DYNASTY: Trading resumed automatically");
   }

   // Update performance metrics
   UpdatePerformanceMetrics();
}

//+------------------------------------------------------------------+
//| Check if trading should resume automatically                    |
//+------------------------------------------------------------------+
bool ShouldResumeTradingAutomatically()
{
   // Check margin level recovery
   if(InpAutoMarginMonitoring)
   {
      double marginLevel = AccountInfoDouble(ACCOUNT_MARGIN_LEVEL);
      if(marginLevel < InpMinMarginLevel * 1.2) // 20% buffer
         return false;
   }

   // Check drawdown recovery
   if(InpAutoDrawdownProtection)
   {
      UpdateCurrentDrawdown();
      if(CurrentDrawdown > InpMaxDrawdownPercent * 0.8) // 20% recovery buffer
         return false;
   }

   return true;
}

//+------------------------------------------------------------------+
//| Update current drawdown                                         |
//+------------------------------------------------------------------+
void UpdateCurrentDrawdown()
{
   double accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
   double accountEquity = AccountInfoDouble(ACCOUNT_EQUITY);

   if(accountBalance > 0)
   {
      CurrentDrawdown = ((accountBalance - accountEquity) / accountBalance) * 100.0;
      CurrentDrawdown = MathMax(0, CurrentDrawdown);
   }
}

//+------------------------------------------------------------------+
//| Update position statistics                                       |
//+------------------------------------------------------------------+
void UpdatePositionStatistics()
{
   // This function can be expanded to track detailed position statistics
   // For now, it maintains basic counters
}

//+------------------------------------------------------------------+
//| Update performance metrics                                       |
//+------------------------------------------------------------------+
void UpdatePerformanceMetrics()
{
   // Calculate win rate
   if(TotalTrades > 0)
   {
      CurrentWinRate = (double)WinningTrades / TotalTrades * 100.0;
   }

   // Calculate signals per hour
   datetime currentTime = TimeCurrent();
   double hoursElapsed = (currentTime - SessionStartTime) / 3600.0;
   if(hoursElapsed > 0)
   {
      SignalsPerHour = TotalTrades / hoursElapsed;
   }
}

//+------------------------------------------------------------------+
//| Update performance dashboard                                     |
//+------------------------------------------------------------------+
void UpdatePerformanceDashboard()
{
   static datetime lastDashboardUpdate = 0;
   datetime currentTime = TimeCurrent();

   // Update dashboard every 30 seconds
   if(currentTime - lastDashboardUpdate >= 30)
   {
      lastDashboardUpdate = currentTime;

      Comment("\n" +
              "╔══════════════════════════════════════════════════════════════╗\n" +
              "║                ARISE-MAY-DYNASTY PRECISION AGENT             ║\n" +
              "╠══════════════════════════════════════════════════════════════╣\n" +
              "║ STATUS: " + (TradingPaused ? "PAUSED (" + PauseReason + ")" : "ACTIVE") + "\n" +
              "║ TOTAL TRADES: " + IntegerToString(TotalTrades) + "\n" +
              "║ WIN RATE: " + DoubleToString(CurrentWinRate, 1) + "%\n" +
              "║ SIGNALS/HOUR: " + DoubleToString(SignalsPerHour, 1) + "\n" +
              "║ TOTAL PROFIT: " + DoubleToString(TotalProfit, 2) + "\n" +
              "║ DRAWDOWN: " + DoubleToString(CurrentDrawdown, 2) + "%\n" +
              "║ DYNAMIC LOT: " + DoubleToString(DynamicLotSize, 2) + "\n" +
              "║ DAILY TRADES: " + IntegerToString(DailyTradeCount) + "\n" +
              "║ CONSECUTIVE WINS: " + IntegerToString(ConsecutiveWins) + "\n" +
              "║ LAST SIGNAL: " + TimeToString(LastSignalDetectionTime, TIME_SECONDS) + "\n" +
              "╚══════════════════════════════════════════════════════════════╝");
   }
}

//+------------------------------------------------------------------+
//| Reset daily counters                                            |
//+------------------------------------------------------------------+
void ResetDailyCounters()
{
   datetime currentDay = (datetime)(TimeCurrent() / 86400) * 86400;
   if(currentDay != LastDayReset)
   {
      LastDayReset = currentDay;
      DailyTradeCount = 0;
      Print("ARISE-MAY-DYNASTY: Daily counters reset for new trading day");
   }
}

//+------------------------------------------------------------------+
//| Check daily reset                                               |
//+------------------------------------------------------------------+
void CheckDailyReset()
{
   ResetDailyCounters();
}

//+------------------------------------------------------------------+
//| Trigger emergency protocol                                       |
//+------------------------------------------------------------------+
void TriggerEmergencyProtocol()
{
   Print("ARISE-MAY-DYNASTY: EMERGENCY PROTOCOL ACTIVATED");

   // Pause all trading
   TradingPaused = true;
   PauseReason = "Emergency protocol - execution failure";

   // Send emergency alert
   if(InpSmartAlerts)
   {
      Alert("ARISE-MAY-DYNASTY: EMERGENCY - Trading system requires attention!");
      if(InpVoiceAlerts) PlaySound("stops.wav");
   }

   // Log emergency details
   Print("ARISE-MAY-DYNASTY: Emergency triggered at ", TimeToString(TimeCurrent(), TIME_SECONDS));
   Print("ARISE-MAY-DYNASTY: Account Balance: ", DoubleToString(AccountInfoDouble(ACCOUNT_BALANCE), 2));
   Print("ARISE-MAY-DYNASTY: Account Equity: ", DoubleToString(AccountInfoDouble(ACCOUNT_EQUITY), 2));
   Print("ARISE-MAY-DYNASTY: Free Margin: ", DoubleToString(AccountInfoDouble(ACCOUNT_MARGIN_FREE), 2));
}

//+------------------------------------------------------------------+
//| Generate performance report                                      |
//+------------------------------------------------------------------+
void GeneratePerformanceReport()
{
   Print("ARISE-MAY-DYNASTY: ═══════════════════════════════════════");
   Print("ARISE-MAY-DYNASTY: FINAL PERFORMANCE REPORT");
   Print("ARISE-MAY-DYNASTY: ═══════════════════════════════════════");
   Print("ARISE-MAY-DYNASTY: Session Duration: ", (TimeCurrent() - SessionStartTime) / 3600.0, " hours");
   Print("ARISE-MAY-DYNASTY: Total Trades Executed: ", TotalTrades);
   Print("ARISE-MAY-DYNASTY: Winning Trades: ", WinningTrades);
   Print("ARISE-MAY-DYNASTY: Win Rate: ", DoubleToString(CurrentWinRate, 2), "%");
   Print("ARISE-MAY-DYNASTY: Total Profit: ", DoubleToString(TotalProfit, 2));
   Print("ARISE-MAY-DYNASTY: Signals Per Hour: ", DoubleToString(SignalsPerHour, 2));
   Print("ARISE-MAY-DYNASTY: Maximum Drawdown: ", DoubleToString(CurrentDrawdown, 2), "%");
   Print("ARISE-MAY-DYNASTY: Final Dynamic Lot Size: ", DoubleToString(DynamicLotSize, 2));
   Print("ARISE-MAY-DYNASTY: ═══════════════════════════════════════");
   Print("ARISE-MAY-DYNASTY: Autonomous Agent Performance: ",
         (CurrentWinRate > 60 ? "EXCELLENT" : CurrentWinRate > 50 ? "GOOD" : "NEEDS OPTIMIZATION"));
   Print("ARISE-MAY-DYNASTY: ═══════════════════════════════════════");
}

//+------------------------------------------------------------------+
//| OnTrade event handler for tracking trade results                |
//+------------------------------------------------------------------+
void OnTrade()
{
   // Update trade statistics when positions are closed
   for(int i = 0; i < HistoryDealsTotal(); i++)
   {
      ulong ticket = HistoryDealGetTicket(i);
      if(ticket > 0)
      {
         if(HistoryDealGetInteger(ticket, DEAL_MAGIC) == InpMagicNumber)
         {
            double profit = HistoryDealGetDouble(ticket, DEAL_PROFIT);
            if(profit != 0) // Only count closed positions
            {
               TotalProfit += profit;

               if(profit > 0)
               {
                  WinningTrades++;
                  ConsecutiveWins++;
                  ConsecutiveLosses = 0;

                  if(InpConsoleLogging)
                     Print("ARISE-MAY-DYNASTY: Winning trade closed - Profit: ", DoubleToString(profit, 2));
               }
               else
               {
                  ConsecutiveLosses++;
                  ConsecutiveWins = 0;

                  if(InpConsoleLogging)
                     Print("ARISE-MAY-DYNASTY: Losing trade closed - Loss: ", DoubleToString(profit, 2));
               }

               // Update performance metrics
               UpdatePerformanceMetrics();
            }
         }
      }
   }
}
