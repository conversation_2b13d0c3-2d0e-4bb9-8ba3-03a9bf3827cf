# AMPD High-Frequency AutoTrader EA - Micro Account Risk Management Guide

## 💰 **MICRO ACCOUNT PROTECTION FOR $10 ACCOUNTS**

The AMPD EA now includes comprehensive risk management specifically designed for micro accounts with very small balances like $10.

---

## 🔧 **MICRO ACCOUNT SETTINGS**

### **Input Parameters**
```mql5
input group "=== MICRO ACCOUNT RISK MANAGEMENT ($10 Account) ==="
input bool   InpMicroAccountMode = true;          // Enable Micro Account Protection
input double InpAccountBalance = 10.0;            // Account Balance ($10 default)
input double InpMaxRiskPerTrade = 1.0;            // Max Risk Per Trade (% of balance)
input double InpMaxDailyRisk = 5.0;               // Max Daily Risk (% of balance)
input int    InpMaxTradesPerDay = 5;              // Max Trades Per Day (micro account limit)
input bool   InpUseFixedMicroLot = true;          // Use Fixed Micro Lot Size
input double InpFixedMicroLot = 0.01;             // Fixed Micro Lot (0.01 = $0.10 per pip typically)
```

### **Recommended Settings for $10 Account**
- **Account Balance**: `10.0` (Your actual balance)
- **Max Risk Per Trade**: `1.0%` (Risk only $0.10 per trade)
- **Max Daily Risk**: `5.0%` (Maximum $0.50 loss per day)
- **Max Trades Per Day**: `5` (Limit overtrading)
- **Use Fixed Micro Lot**: `true` (Consistent position sizing)
- **Fixed Micro Lot**: `0.01` (Smallest possible lot size)

---

## 🛡️ **PROTECTION MECHANISMS**

### **1. Lot Size Protection**
- **Maximum Lot Size**: Automatically calculated based on account balance and risk percentage
- **Fixed Micro Lot Mode**: Uses 0.01 lot (smallest possible) for maximum safety
- **Dynamic Calculation**: For $10 account with 1% risk = maximum $0.10 risk per trade

### **2. Daily Risk Limits**
- **Daily Risk Tracking**: Monitors total risk exposure per day
- **Automatic Reset**: Counters reset at start of each trading day
- **Risk Accumulation**: Prevents exceeding daily risk limit across multiple trades

### **3. Trade Frequency Control**
- **Daily Trade Limit**: Maximum 5 trades per day for micro accounts
- **Overtrading Prevention**: Blocks additional trades once limit reached
- **Smart Pacing**: Encourages quality over quantity trading

### **4. Real-Time Monitoring**
```
📊 MICRO ACCOUNT TRACKING UPDATED:
   Daily trades: 3/5
   Daily risk used: $0.30
   Remaining risk: $0.20
```

---

## 📊 **RISK CALCULATIONS**

### **Per-Trade Risk Calculation**
```
Max Risk Per Trade = Account Balance × (Risk Percentage / 100)
Example: $10 × (1% / 100) = $0.10 maximum risk per trade
```

### **Daily Risk Calculation**
```
Max Daily Risk = Account Balance × (Daily Risk Percentage / 100)
Example: $10 × (5% / 100) = $0.50 maximum daily risk
```

### **Lot Size Calculation**
```
Conservative Approach:
- Assume 10 pip risk per trade
- Pip value for 0.01 lot ≈ $0.10 per pip
- Max lot size = $0.10 risk ÷ (10 pips × $0.01 per pip) = 0.01 lot
```

---

## ⚠️ **SAFETY FEATURES**

### **Trade Blocking Conditions**
The EA will **BLOCK** trades if:
1. **Daily trade limit reached** (e.g., 5/5 trades used)
2. **Lot size exceeds maximum** (e.g., trying to use 0.02 when max is 0.01)
3. **Daily risk limit would be exceeded** (e.g., $0.50 daily limit reached)
4. **Account balance insufficient** for minimum risk requirements

### **Error Messages**
```
❌ MICRO ACCOUNT: Daily trade limit reached (5/5)
❌ MICRO ACCOUNT: Lot size too large (0.02 > 0.01)
❌ MICRO ACCOUNT: Daily risk limit would be exceeded
```

### **Approval Messages**
```
✅ MICRO ACCOUNT: Trade approved
   Lot size: 0.01
   Daily trades: 3/5
   Daily risk: $0.30/$0.50
```

---

## 🎯 **EXPECTED PERFORMANCE**

### **Conservative Projections for $10 Account**
- **Risk Per Trade**: $0.10 (1% of $10)
- **Potential Profit Per Trade**: $0.20-$0.50 (2-5% of account)
- **Daily Risk Limit**: $0.50 (5% of $10)
- **Maximum Daily Trades**: 5 trades
- **Monthly Growth Target**: 10-20% ($1-$2 profit)

### **Account Growth Simulation**
```
Starting Balance: $10.00
Month 1: $11.50 (+15%)
Month 2: $13.25 (+15%)
Month 3: $15.25 (+15%)
Month 6: $23.00 (+130%)
```

---

## 🔍 **MONITORING & ALERTS**

### **Initialization Messages**
```
💰 MICRO ACCOUNT RISK MANAGEMENT INITIALIZED
   ═══════════════════════════════════════════
   💵 Account Balance: $10.00
   📊 Max Risk Per Trade: 1.0% ($0.10)
   📊 Max Daily Risk: 5.0% ($0.50)
   📊 Max Lot Size: 0.01
   📊 Max Trades Per Day: 5
   ⚠️  PROTECTION: Account blow-up prevention ACTIVE
```

### **Real-Time Tracking**
- **Trade Execution**: Shows risk amount and remaining limits
- **Daily Progress**: Tracks trades used and risk accumulated
- **Limit Warnings**: Alerts when approaching daily limits
- **Protection Status**: Confirms micro account protection is active

---

## 🚀 **GETTING STARTED**

### **Step 1: Enable Micro Account Mode**
1. Attach EA to chart
2. Go to EA Properties → Inputs
3. Set `InpMicroAccountMode = true`
4. Set `InpAccountBalance = 10.0` (your actual balance)

### **Step 2: Configure Risk Settings**
1. Set `InpMaxRiskPerTrade = 1.0` (1% risk)
2. Set `InpMaxDailyRisk = 5.0` (5% daily limit)
3. Set `InpMaxTradesPerDay = 5` (daily trade limit)
4. Enable `InpUseFixedMicroLot = true`
5. Set `InpFixedMicroLot = 0.01`

### **Step 3: Verify Protection**
1. Check initialization logs for micro account confirmation
2. Look for "MICRO ACCOUNT MODE: ENABLED" message
3. Verify lot size limits and risk calculations
4. Monitor first few trades for proper risk tracking

---

## 📈 **SCALING UP**

### **Account Growth Milestones**
- **$20 Account**: Increase to 0.02 lot size or 1.5% risk
- **$50 Account**: Consider 2% risk per trade
- **$100 Account**: Standard risk management (2-3% per trade)
- **$500+ Account**: Full EA features with adaptive lot sizing

### **Graduation from Micro Account Mode**
When your account reaches $100+:
1. Set `InpMicroAccountMode = false`
2. Use standard risk management settings
3. Enable adaptive lot sizing features
4. Increase daily trade limits

---

**Status**: 🛡️ **MICRO ACCOUNT PROTECTION ACTIVE**  
**Confidence Level**: 🟢 **HIGH** - Maximum safety for small accounts with strict risk controls
