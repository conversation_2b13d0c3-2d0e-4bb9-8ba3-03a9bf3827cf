//+------------------------------------------------------------------+
//|                                   AMPD Compilation Test.mq5     |
//|                        Copyright 2024, <PERSON><PERSON> |
//|                                             https://www.ampd.com |
//+------------------------------------------------------------------+
#property copyright "Arise Moroka Prince Dynasty"
#property link      "https://ampd.com"
#property description "Compilation Test for AMPD System Components"
#property version   "1.0"
#property script_show_inputs

//--- Input parameters
input bool InpTestIndicator = true;                // Test Indicator Compilation
input bool InpTestEA = true;                       // Test EA Compilation
input bool InpShowDetails = true;                  // Show Detailed Results

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
   Print("=== AMPD SYSTEM COMPILATION TEST ===");
   Print("Testing compilation of all system components...");
   Print("==========================================");
   
   bool allTestsPassed = true;
   
   // Test 1: Basic MQL5 Functions
   if(!TestBasicFunctions())
   {
      Print("ERROR: Basic MQL5 functions test failed!");
      allTestsPassed = false;
   }
   else
   {
      Print("SUCCESS: Basic MQL5 functions test passed");
   }
   
   // Test 2: Time Functions
   if(!TestTimeFunctions())
   {
      Print("ERROR: Time functions test failed!");
      allTestsPassed = false;
   }
   else
   {
      Print("SUCCESS: Time functions test passed");
   }
   
   // Test 3: Trading Functions
   if(!TestTradingFunctions())
   {
      Print("ERROR: Trading functions test failed!");
      allTestsPassed = false;
   }
   else
   {
      Print("SUCCESS: Trading functions test passed");
   }
   
   // Test 4: Indicator Functions
   if(!TestIndicatorFunctions())
   {
      Print("ERROR: Indicator functions test failed!");
      allTestsPassed = false;
   }
   else
   {
      Print("SUCCESS: Indicator functions test passed");
   }
   
   // Test 5: Array Operations
   if(!TestArrayOperations())
   {
      Print("ERROR: Array operations test failed!");
      allTestsPassed = false;
   }
   else
   {
      Print("SUCCESS: Array operations test passed");
   }
   
   Print("==========================================");
   if(allTestsPassed)
   {
      Print("=== ALL COMPILATION TESTS PASSED ===");
      Print("System components should compile successfully");
   }
   else
   {
      Print("=== SOME TESTS FAILED ===");
      Print("Please check the errors above and fix accordingly");
   }
   Print("==========================================");
}

//+------------------------------------------------------------------+
//| Test basic MQL5 functions                                       |
//+------------------------------------------------------------------+
bool TestBasicFunctions()
{
   Print("Testing basic MQL5 functions...");
   
   // Test basic math functions
   double testValue = 123.456;
   double rounded = NormalizeDouble(testValue, 2);
   double absolute = MathAbs(-10.5);
   double maximum = MathMax(5.0, 10.0);
   double minimum = MathMin(5.0, 10.0);
   
   if(InpShowDetails)
   {
      Print("- NormalizeDouble(123.456, 2) = ", rounded);
      Print("- MathAbs(-10.5) = ", absolute);
      Print("- MathMax(5.0, 10.0) = ", maximum);
      Print("- MathMin(5.0, 10.0) = ", minimum);
   }
   
   // Test string functions
   string testString = "AMPD Test";
   int stringLength = StringLen(testString);
   string upperString = testString;
   StringToUpper(upperString);
   
   if(InpShowDetails)
   {
      Print("- StringLen('AMPD Test') = ", stringLength);
      Print("- StringToUpper('AMPD Test') = ", upperString);
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| Test time functions                                             |
//+------------------------------------------------------------------+
bool TestTimeFunctions()
{
   Print("Testing time functions...");
   
   // Test current time functions
   datetime currentTime = TimeCurrent();
   datetime localTime = TimeLocal();
   
   if(InpShowDetails)
   {
      Print("- TimeCurrent() = ", TimeToString(currentTime));
      Print("- TimeLocal() = ", TimeToString(localTime));
   }
   
   // Test TimeToStruct function (this was causing compilation errors)
   MqlDateTime timeStruct;
   bool structResult = TimeToStruct(currentTime, timeStruct);
   
   if(!structResult)
   {
      Print("ERROR: TimeToStruct function failed");
      return false;
   }
   
   if(InpShowDetails)
   {
      Print("- TimeToStruct result:");
      Print("  Year: ", timeStruct.year);
      Print("  Month: ", timeStruct.mon);
      Print("  Day: ", timeStruct.day);
      Print("  Hour: ", timeStruct.hour);
      Print("  Minute: ", timeStruct.min);
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| Test trading functions                                          |
//+------------------------------------------------------------------+
bool TestTradingFunctions()
{
   Print("Testing trading functions...");
   
   // Test symbol info functions
   string currentSymbol = _Symbol;
   double point = SymbolInfoDouble(currentSymbol, SYMBOL_POINT);
   double tickSize = SymbolInfoDouble(currentSymbol, SYMBOL_TRADE_TICK_SIZE);
   double minLot = SymbolInfoDouble(currentSymbol, SYMBOL_VOLUME_MIN);
   double maxLot = SymbolInfoDouble(currentSymbol, SYMBOL_VOLUME_MAX);
   
   if(InpShowDetails)
   {
      Print("- Current Symbol: ", currentSymbol);
      Print("- Point: ", point);
      Print("- Tick Size: ", tickSize);
      Print("- Min Lot: ", minLot);
      Print("- Max Lot: ", maxLot);
   }
   
   // Test account info functions
   double balance = AccountInfoDouble(ACCOUNT_BALANCE);
   double equity = AccountInfoDouble(ACCOUNT_EQUITY);
   string currency = AccountInfoString(ACCOUNT_CURRENCY);
   
   if(InpShowDetails)
   {
      Print("- Account Balance: ", balance);
      Print("- Account Equity: ", equity);
      Print("- Account Currency: ", currency);
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| Test indicator functions                                        |
//+------------------------------------------------------------------+
bool TestIndicatorFunctions()
{
   Print("Testing indicator functions...");
   
   // Test built-in indicators
   int rsiHandle = iRSI(_Symbol, PERIOD_M1, 14, PRICE_CLOSE);
   int atrHandle = iATR(_Symbol, PERIOD_M1, 14);
   
   if(rsiHandle == INVALID_HANDLE)
   {
      Print("ERROR: Failed to create RSI indicator handle");
      return false;
   }
   
   if(atrHandle == INVALID_HANDLE)
   {
      Print("ERROR: Failed to create ATR indicator handle");
      IndicatorRelease(rsiHandle);
      return false;
   }
   
   if(InpShowDetails)
   {
      Print("- RSI Handle: ", rsiHandle);
      Print("- ATR Handle: ", atrHandle);
   }
   
   // Test data copying
   double rsiBuffer[];
   double atrBuffer[];
   
   ArraySetAsSeries(rsiBuffer, true);
   ArraySetAsSeries(atrBuffer, true);
   
   int rsiCopied = CopyBuffer(rsiHandle, 0, 0, 10, rsiBuffer);
   int atrCopied = CopyBuffer(atrHandle, 0, 0, 10, atrBuffer);
   
   if(rsiCopied <= 0)
   {
      Print("ERROR: Failed to copy RSI buffer data");
      IndicatorRelease(rsiHandle);
      IndicatorRelease(atrHandle);
      return false;
   }
   
   if(atrCopied <= 0)
   {
      Print("ERROR: Failed to copy ATR buffer data");
      IndicatorRelease(rsiHandle);
      IndicatorRelease(atrHandle);
      return false;
   }
   
   if(InpShowDetails)
   {
      Print("- RSI values copied: ", rsiCopied);
      Print("- ATR values copied: ", atrCopied);
      Print("- Current RSI: ", DoubleToString(rsiBuffer[0], 2));
      Print("- Current ATR: ", DoubleToString(atrBuffer[0], _Digits));
   }
   
   // Clean up
   IndicatorRelease(rsiHandle);
   IndicatorRelease(atrHandle);
   
   return true;
}

//+------------------------------------------------------------------+
//| Test array operations                                           |
//+------------------------------------------------------------------+
bool TestArrayOperations()
{
   Print("Testing array operations...");
   
   // Test dynamic arrays
   double testArray[];
   ArrayResize(testArray, 100);
   ArraySetAsSeries(testArray, true);
   
   // Fill array with test data
   for(int i = 0; i < 100; i++)
   {
      testArray[i] = i * 1.5;
   }
   
   int arraySize = ArraySize(testArray);
   if(arraySize != 100)
   {
      Print("ERROR: Array size mismatch. Expected 100, got ", arraySize);
      return false;
   }
   
   if(InpShowDetails)
   {
      Print("- Array size: ", arraySize);
      Print("- First element: ", testArray[0]);
      Print("- Last element: ", testArray[99]);
   }
   
   // Test price arrays
   double closeArray[];
   double openArray[];
   
   ArraySetAsSeries(closeArray, true);
   ArraySetAsSeries(openArray, true);
   
   int closeCopied = CopyClose(_Symbol, PERIOD_M1, 0, 50, closeArray);
   int openCopied = CopyOpen(_Symbol, PERIOD_M1, 0, 50, openArray);
   
   if(closeCopied <= 0)
   {
      Print("ERROR: Failed to copy close prices");
      return false;
   }
   
   if(openCopied <= 0)
   {
      Print("ERROR: Failed to copy open prices");
      return false;
   }
   
   if(InpShowDetails)
   {
      Print("- Close prices copied: ", closeCopied);
      Print("- Open prices copied: ", openCopied);
      Print("- Current close: ", DoubleToString(closeArray[0], _Digits));
      Print("- Current open: ", DoubleToString(openArray[0], _Digits));
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| Additional compilation validation                               |
//+------------------------------------------------------------------+
void TestCompilationFeatures()
{
   Print("Testing compilation-specific features...");
   
   // Test enums
   enum TEST_ENUM
   {
      TEST_NONE = 0,
      TEST_ONE = 1,
      TEST_TWO = 2
   };
   
   TEST_ENUM testEnum = TEST_ONE;
   
   // Test structures
   struct TestStruct
   {
      double value1;
      int value2;
      string value3;
   };
   
   TestStruct testStruct;
   testStruct.value1 = 123.45;
   testStruct.value2 = 678;
   testStruct.value3 = "Test";
   
   // Test classes (basic)
   class TestClass
   {
   public:
      double m_value;
      TestClass(double value) { m_value = value; }
      double GetValue() { return m_value; }
   };
   
   TestClass testClass(999.99);
   double classValue = testClass.GetValue();
   
   if(InpShowDetails)
   {
      Print("- Enum value: ", testEnum);
      Print("- Struct value1: ", testStruct.value1);
      Print("- Struct value2: ", testStruct.value2);
      Print("- Struct value3: ", testStruct.value3);
      Print("- Class value: ", classValue);
   }
}
