//+------------------------------------------------------------------+
//|                                AMPD System Validation Test.mq5 |
//|                        Copyright 2025, <PERSON><PERSON> |
//|                                             https://www.ampd.com |
//+------------------------------------------------------------------+
#property copyright "Arise Moroka Prince Dynasty"
#property link      "https://ampd.com"
#property description "System Validation Test for AMPD High-Frequency Trading System"
#property version   "1.0"
#property script_show_inputs

//--- Input parameters
input string InpIndicatorName = "AMPD_RealTime_Precision_Indicator"; // Indicator Name
input bool   InpTestSignalDetection = true;    // Test Signal Detection
input bool   InpTestBufferAccess = true;       // Test Buffer Access
input bool   InpTestVisualElements = true;     // Test Visual Elements
input int    InpTestBars = 100;                // Number of bars to test

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
   Print("================================================================");
   Print("🧪 AMPD SYSTEM VALIDATION TEST - STARTING");
   Print("================================================================");
   
   bool allTestsPassed = true;
   
   //--- Test 1: Indicator Connection
   Print("📋 TEST 1: Indicator Connection");
   if(TestIndicatorConnection())
   {
      Print("✅ TEST 1 PASSED: Indicator connection successful");
   }
   else
   {
      Print("❌ TEST 1 FAILED: Cannot connect to indicator");
      allTestsPassed = false;
   }
   
   //--- Test 2: Buffer Access
   if(InpTestBufferAccess)
   {
      Print("📋 TEST 2: Buffer Access");
      if(TestBufferAccess())
      {
         Print("✅ TEST 2 PASSED: All buffers accessible");
      }
      else
      {
         Print("❌ TEST 2 FAILED: Buffer access issues");
         allTestsPassed = false;
      }
   }
   
   //--- Test 3: Signal Detection
   if(InpTestSignalDetection)
   {
      Print("📋 TEST 3: Signal Detection Logic");
      if(TestSignalDetection())
      {
         Print("✅ TEST 3 PASSED: Signal detection working");
      }
      else
      {
         Print("❌ TEST 3 FAILED: Signal detection issues");
         allTestsPassed = false;
      }
   }
   
   //--- Test 4: Visual Elements
   if(InpTestVisualElements)
   {
      Print("📋 TEST 4: Visual Elements");
      if(TestVisualElements())
      {
         Print("✅ TEST 4 PASSED: Visual elements working");
      }
      else
      {
         Print("❌ TEST 4 FAILED: Visual element issues");
         allTestsPassed = false;
      }
   }
   
   //--- Final Result
   Print("================================================================");
   if(allTestsPassed)
   {
      Print("🎉 ALL TESTS PASSED - SYSTEM READY FOR TRADING");
      Print("✅ Indicator: Connected and functional");
      Print("✅ Buffers: All accessible");
      Print("✅ Signals: Detection working");
      Print("✅ Visuals: Elements displaying correctly");
   }
   else
   {
      Print("⚠️ SOME TESTS FAILED - SYSTEM NEEDS ATTENTION");
      Print("Please review the failed tests above");
   }
   Print("================================================================");
}

//+------------------------------------------------------------------+
//| Test indicator connection                                        |
//+------------------------------------------------------------------+
bool TestIndicatorConnection()
{
   int indicatorHandle = iCustom(_Symbol, PERIOD_CURRENT, InpIndicatorName);
   if(indicatorHandle == INVALID_HANDLE)
   {
      Print("   ❌ Cannot create indicator handle");
      return false;
   }
   
   Print("   ✅ Indicator handle created successfully: ", indicatorHandle);
   IndicatorRelease(indicatorHandle);
   return true;
}

//+------------------------------------------------------------------+
//| Test buffer access                                               |
//+------------------------------------------------------------------+
bool TestBufferAccess()
{
   int indicatorHandle = iCustom(_Symbol, PERIOD_CURRENT, InpIndicatorName);
   if(indicatorHandle == INVALID_HANDLE) return false;
   
   double goldBuffer[], limeBuffer[], entryBuffer[], exitBuffer[];
   
   // Test GOLD arrows buffer (Buffer 0)
   if(CopyBuffer(indicatorHandle, 0, 0, 10, goldBuffer) != 10)
   {
      Print("   ❌ Cannot access GOLD arrows buffer (Buffer 0)");
      IndicatorRelease(indicatorHandle);
      return false;
   }
   Print("   ✅ GOLD arrows buffer accessible");
   
   // Test LIME arrows buffer (Buffer 1)
   if(CopyBuffer(indicatorHandle, 1, 0, 10, limeBuffer) != 10)
   {
      Print("   ❌ Cannot access LIME arrows buffer (Buffer 1)");
      IndicatorRelease(indicatorHandle);
      return false;
   }
   Print("   ✅ LIME arrows buffer accessible");
   
   // Test Entry dots buffer (Buffer 2)
   if(CopyBuffer(indicatorHandle, 2, 0, 10, entryBuffer) != 10)
   {
      Print("   ❌ Cannot access Entry dots buffer (Buffer 2)");
      IndicatorRelease(indicatorHandle);
      return false;
   }
   Print("   ✅ Entry dots buffer accessible");
   
   // Test Exit dots buffer (Buffer 3)
   if(CopyBuffer(indicatorHandle, 3, 0, 10, exitBuffer) != 10)
   {
      Print("   ❌ Cannot access Exit dots buffer (Buffer 3)");
      IndicatorRelease(indicatorHandle);
      return false;
   }
   Print("   ✅ Exit dots buffer accessible");
   
   IndicatorRelease(indicatorHandle);
   return true;
}

//+------------------------------------------------------------------+
//| Test signal detection                                            |
//+------------------------------------------------------------------+
bool TestSignalDetection()
{
   int indicatorHandle = iCustom(_Symbol, PERIOD_CURRENT, InpIndicatorName);
   if(indicatorHandle == INVALID_HANDLE) return false;
   
   double goldBuffer[], limeBuffer[], exitBuffer[];
   int signalCount = 0;
   
   // Check recent bars for signals
   for(int i = 0; i < InpTestBars; i++)
   {
      if(CopyBuffer(indicatorHandle, 0, i, 1, goldBuffer) == 1 &&
         CopyBuffer(indicatorHandle, 1, i, 1, limeBuffer) == 1 &&
         CopyBuffer(indicatorHandle, 3, i, 1, exitBuffer) == 1)
      {
         bool goldSignal = (goldBuffer[0] != EMPTY_VALUE && goldBuffer[0] != 0.0);
         bool limeSignal = (limeBuffer[0] != EMPTY_VALUE && limeBuffer[0] != 0.0);
         bool exitSignal = (exitBuffer[0] != EMPTY_VALUE && exitBuffer[0] != 0.0);
         
         if(goldSignal || limeSignal || exitSignal)
         {
            signalCount++;
            if(signalCount <= 5) // Show first 5 signals found
            {
               string signalType = goldSignal ? "GOLD" : (limeSignal ? "LIME" : "EXIT");
               Print("   📊 Signal found at bar ", i, ": ", signalType);
            }
         }
      }
   }
   
   Print("   📈 Total signals found in ", InpTestBars, " bars: ", signalCount);
   
   IndicatorRelease(indicatorHandle);
   return signalCount > 0; // At least some signals should be present
}

//+------------------------------------------------------------------+
//| Test visual elements                                             |
//+------------------------------------------------------------------+
bool TestVisualElements()
{
   // Check if indicator is attached to chart
   int indicatorsTotal = ChartIndicatorsTotal(0, 0);
   Print("   📊 Total indicators on chart: ", indicatorsTotal);
   
   // Look for our indicator
   for(int i = 0; i < indicatorsTotal; i++)
   {
      string indicatorName = ChartIndicatorName(0, 0, i);
      if(StringFind(indicatorName, "AMPD") >= 0)
      {
         Print("   ✅ AMPD indicator found on chart: ", indicatorName);
         return true;
      }
   }
   
   Print("   ⚠️ AMPD indicator not found on chart - may need manual attachment");
   return true; // Don't fail the test for this
}
