//+------------------------------------------------------------------+
//|                                        AMPD Jump Crash EA.mq5   |
//|                        Copyright 2024, <PERSON>se <PERSON> |
//|                                             https://www.ampd.com |
//+------------------------------------------------------------------+
#property copyright "Arise Moroka Prince Dynasty"
#property link      "https://ampd.com"
#property description "Expert Advisor for Jump 75 & Crash 1000 Trading"
#property version   "1.0"
#property strict

#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\OrderInfo.mqh>

//--- Input parameters
input group "=== TRADING SETTINGS ==="
input double InpLotSize = 0.01;                    // Lot Size
input int    InpMagicNumber = 123456;              // Magic Number
input string InpComment = "AMPD Jump Crash";       // Order Comment

input group "=== JUMP 75 SETTINGS ==="
input bool   InpTradeJump75 = true;                // Enable Jump 75 Trading
input string InpJump75Symbol = "Jump 75 Index";    // Jump 75 Symbol
input double InpJump75TakeProfit = 50.0;           // Jump 75 Take Profit (points)
input double InpJump75StopLoss = 25.0;             // Jump 75 Stop Loss (points)
input int    InpJump75MaxTrades = 3;               // Jump 75 Max Concurrent Trades

input group "=== CRASH 1000 SETTINGS ==="
input bool   InpTradeCrash1000 = true;             // Enable Crash 1000 Trading
input string InpCrash1000Symbol = "Crash 1000 Index"; // Crash 1000 Symbol
input double InpCrash1000TakeProfit = 75.0;        // Crash 1000 Take Profit (points)
input double InpCrash1000StopLoss = 35.0;          // Crash 1000 Stop Loss (points)
input int    InpCrash1000MaxTrades = 2;            // Crash 1000 Max Concurrent Trades

input group "=== RISK MANAGEMENT ==="
input double InpMaxRiskPercent = 2.0;              // Maximum Risk Per Trade (%)
input bool   InpUseTrailingStop = true;            // Use Trailing Stop
input double InpTrailingStopDistance = 20.0;       // Trailing Stop Distance (points)
input int    InpMaxDailyTrades = 10;               // Maximum Daily Trades

input group "=== TIME SETTINGS ==="
input bool   InpUseTradingHours = false;           // Use Trading Hours
input string InpStartTime = "08:00";               // Trading Start Time
input string InpEndTime = "22:00";                 // Trading End Time
input int    InpSignalCooldown = 60;               // Signal Cooldown (seconds) - Reduced for more frequent signals
input bool   InpTradeEveryBar = true;              // Check Signals Every Bar
input int    InpMinBarsBetweenSignals = 1;         // Minimum Bars Between Signals

//--- Global variables
CTrade trade;
CPositionInfo position;
COrderInfo order;

int IndicatorHandle;
datetime LastJumpSignalTime = 0;
datetime LastCrashSignalTime = 0;
datetime LastTradeTime = 0;
int DailyTradeCount = 0;
datetime LastDayReset = 0;

//--- Indicator buffers for signal reading
double JumpSignals[];
double CrashSignals[];
double ExitSignals[];

//--- Signal types (must match indicator)
enum SIGNAL_TYPE
{
   SIGNAL_NONE = 0,
   SIGNAL_JUMP = 1,
   SIGNAL_CRASH = -1,
   SIGNAL_EXIT = 2
};

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   // Set trade parameters
   trade.SetExpertMagicNumber(InpMagicNumber);
   trade.SetMarginMode();
   trade.SetTypeFillingBySymbol(_Symbol);
   
   // Load the custom indicator
   IndicatorHandle = iCustom(_Symbol, PERIOD_M1, "AMPD_Jump_Crash_Indicator");
   
   if(IndicatorHandle == INVALID_HANDLE)
   {
      Print("Error loading AMPD Jump Crash Indicator");
      return INIT_FAILED;
   }
   
   // Initialize arrays
   ArraySetAsSeries(JumpSignals, true);
   ArraySetAsSeries(CrashSignals, true);
   ArraySetAsSeries(ExitSignals, true);
   
   // Reset daily counters
   ResetDailyCounters();
   
   Print("AMPD Jump Crash EA initialized successfully");
   Print("Jump 75 Trading: ", InpTradeJump75 ? "Enabled" : "Disabled");
   Print("Crash 1000 Trading: ", InpTradeCrash1000 ? "Enabled" : "Disabled");
   
   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   if(IndicatorHandle != INVALID_HANDLE)
      IndicatorRelease(IndicatorHandle);
   
   Print("AMPD Jump Crash EA deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // Check if new bar or trade every tick based on settings
   static datetime lastBarTime = 0;
   static datetime lastProcessTime = 0;
   datetime currentBarTime = iTime(_Symbol, PERIOD_M1, 0);

   bool shouldProcess = false;

   if(InpTradeEveryBar)
   {
      // Process on new bar only
      if(currentBarTime != lastBarTime)
      {
         lastBarTime = currentBarTime;
         shouldProcess = true;
      }
   }
   else
   {
      // Process every tick but respect cooldown
      if(TimeCurrent() - lastProcessTime >= InpSignalCooldown)
      {
         shouldProcess = true;
         lastProcessTime = TimeCurrent();
      }
   }

   if(!shouldProcess)
      return;
   
   // Reset daily counters if new day
   MqlDateTime currentTime, lastResetTime;
   TimeToStruct(TimeCurrent(), currentTime);
   TimeToStruct(LastDayReset, lastResetTime);

   if(currentTime.day != lastResetTime.day)
      ResetDailyCounters();
   
   // Check trading conditions
   if(!IsTradingAllowed())
      return;
   
   // Get indicator signals
   if(!GetIndicatorSignals())
      return;
   
   // Process signals
   ProcessSignals();
   
   // Manage existing positions
   ManagePositions();
}

//+------------------------------------------------------------------+
//| Check if trading is allowed                                     |
//+------------------------------------------------------------------+
bool IsTradingAllowed()
{
   // Check daily trade limit
   if(DailyTradeCount >= InpMaxDailyTrades)
      return false;
   
   // Check trading hours
   if(InpUseTradingHours)
   {
      MqlDateTime currentTime;
      TimeToStruct(TimeCurrent(), currentTime);
      
      int currentMinutes = currentTime.hour * 60 + currentTime.min;
      
      string startParts[];
      string endParts[];
      StringSplit(InpStartTime, ':', startParts);
      StringSplit(InpEndTime, ':', endParts);
      
      int startMinutes = (int)StringToInteger(startParts[0]) * 60 + (int)StringToInteger(startParts[1]);
      int endMinutes = (int)StringToInteger(endParts[0]) * 60 + (int)StringToInteger(endParts[1]);
      
      if(currentMinutes < startMinutes || currentMinutes > endMinutes)
         return false;
   }
   
   // Check signal cooldown
   if(TimeCurrent() - LastTradeTime < InpSignalCooldown)
      return false;
   
   return true;
}

//+------------------------------------------------------------------+
//| Get indicator signals                                           |
//+------------------------------------------------------------------+
bool GetIndicatorSignals()
{
   // Copy indicator buffers
   if(CopyBuffer(IndicatorHandle, 0, 0, 3, JumpSignals) <= 0)
      return false;
   
   if(CopyBuffer(IndicatorHandle, 1, 0, 3, CrashSignals) <= 0)
      return false;
   
   if(CopyBuffer(IndicatorHandle, 2, 0, 3, ExitSignals) <= 0)
      return false;
   
   return true;
}

//+------------------------------------------------------------------+
//| Process trading signals                                         |
//+------------------------------------------------------------------+
void ProcessSignals()
{
   // Check for Jump 75 signals with enhanced detection
   if(InpTradeJump75 && JumpSignals[0] != EMPTY_VALUE && JumpSignals[0] > 0)
   {
      datetime currentBarTime = iTime(_Symbol, PERIOD_M1, 0);
      bool cooldownPassed = (TimeCurrent() - LastJumpSignalTime >= InpSignalCooldown);
      bool barsPassed = (currentBarTime - iTime(_Symbol, PERIOD_M1, InpMinBarsBetweenSignals) >= LastJumpSignalTime);

      if(cooldownPassed && barsPassed)
      {
         if(CountPositions(POSITION_TYPE_BUY, InpJump75Symbol) < InpJump75MaxTrades)
         {
            Print("JUMP 75 Signal Detected - Executing BUY trade");
            ExecuteJumpTrade();
            LastJumpSignalTime = currentBarTime;
         }
         else
         {
            Print("JUMP 75 Signal Detected but max trades reached: ", InpJump75MaxTrades);
         }
      }
   }

   // Check for Crash 1000 signals with enhanced detection
   if(InpTradeCrash1000 && CrashSignals[0] != EMPTY_VALUE && CrashSignals[0] > 0)
   {
      datetime currentBarTime = iTime(_Symbol, PERIOD_M1, 0);
      bool cooldownPassed = (TimeCurrent() - LastCrashSignalTime >= InpSignalCooldown);
      bool barsPassed = (currentBarTime - iTime(_Symbol, PERIOD_M1, InpMinBarsBetweenSignals) >= LastCrashSignalTime);

      if(cooldownPassed && barsPassed)
      {
         if(CountPositions(POSITION_TYPE_SELL, InpCrash1000Symbol) < InpCrash1000MaxTrades)
         {
            Print("CRASH 1000 Signal Detected - Executing SELL trade");
            ExecuteCrashTrade();
            LastCrashSignalTime = currentBarTime;
         }
         else
         {
            Print("CRASH 1000 Signal Detected but max trades reached: ", InpCrash1000MaxTrades);
         }
      }
   }
   
   // Check for exit signals with enhanced detection
   if(ExitSignals[0] != EMPTY_VALUE && ExitSignals[0] > 0)
   {
      Print("EXIT Signal Detected - Closing all positions");
      CloseAllPositions();
   }
}

//+------------------------------------------------------------------+
//| Execute Jump 75 trade                                          |
//+------------------------------------------------------------------+
void ExecuteJumpTrade()
{
   double price = SymbolInfoDouble(InpJump75Symbol, SYMBOL_ASK);
   double sl = price - InpJump75StopLoss * SymbolInfoDouble(InpJump75Symbol, SYMBOL_POINT);
   double tp = price + InpJump75TakeProfit * SymbolInfoDouble(InpJump75Symbol, SYMBOL_POINT);
   
   // Calculate lot size based on risk
   double lotSize = CalculateLotSize(InpJump75Symbol, InpJump75StopLoss);
   
   if(trade.Buy(lotSize, InpJump75Symbol, price, sl, tp, InpComment + " Jump75"))
   {
      Print("Jump 75 BUY order executed: ", trade.ResultOrder());
      DailyTradeCount++;
      LastTradeTime = TimeCurrent();
   }
   else
   {
      Print("Jump 75 BUY order failed: ", trade.ResultRetcodeDescription());
   }
}

//+------------------------------------------------------------------+
//| Execute Crash 1000 trade                                       |
//+------------------------------------------------------------------+
void ExecuteCrashTrade()
{
   double price = SymbolInfoDouble(InpCrash1000Symbol, SYMBOL_BID);
   double sl = price + InpCrash1000StopLoss * SymbolInfoDouble(InpCrash1000Symbol, SYMBOL_POINT);
   double tp = price - InpCrash1000TakeProfit * SymbolInfoDouble(InpCrash1000Symbol, SYMBOL_POINT);
   
   // Calculate lot size based on risk
   double lotSize = CalculateLotSize(InpCrash1000Symbol, InpCrash1000StopLoss);
   
   if(trade.Sell(lotSize, InpCrash1000Symbol, price, sl, tp, InpComment + " Crash1000"))
   {
      Print("Crash 1000 SELL order executed: ", trade.ResultOrder());
      DailyTradeCount++;
      LastTradeTime = TimeCurrent();
   }
   else
   {
      Print("Crash 1000 SELL order failed: ", trade.ResultRetcodeDescription());
   }
}

//+------------------------------------------------------------------+
//| Calculate lot size based on risk management                     |
//+------------------------------------------------------------------+
double CalculateLotSize(string symbol, double stopLossPoints)
{
   double accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
   double riskAmount = accountBalance * InpMaxRiskPercent / 100.0;

   double pointValue = SymbolInfoDouble(symbol, SYMBOL_TRADE_TICK_VALUE);
   double pointSize = SymbolInfoDouble(symbol, SYMBOL_POINT);

   if(pointValue == 0 || pointSize == 0)
      return InpLotSize;

   double stopLossValue = stopLossPoints * pointSize;
   double lotSize = riskAmount / (stopLossValue * pointValue / pointSize);

   // Normalize lot size
   double minLot = SymbolInfoDouble(symbol, SYMBOL_VOLUME_MIN);
   double maxLot = SymbolInfoDouble(symbol, SYMBOL_VOLUME_MAX);
   double lotStep = SymbolInfoDouble(symbol, SYMBOL_VOLUME_STEP);

   lotSize = MathMax(minLot, MathMin(maxLot, lotSize));
   lotSize = NormalizeDouble(lotSize / lotStep, 0) * lotStep;

   return lotSize;
}

//+------------------------------------------------------------------+
//| Count positions by type and symbol                              |
//+------------------------------------------------------------------+
int CountPositions(ENUM_POSITION_TYPE posType, string symbol = "")
{
   int count = 0;

   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(position.SelectByIndex(i))
      {
         if(position.Magic() == InpMagicNumber)
         {
            if(symbol == "" || position.Symbol() == symbol)
            {
               if(position.PositionType() == posType)
                  count++;
            }
         }
      }
   }

   return count;
}

//+------------------------------------------------------------------+
//| Close all positions                                             |
//+------------------------------------------------------------------+
void CloseAllPositions()
{
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(position.SelectByIndex(i))
      {
         if(position.Magic() == InpMagicNumber)
         {
            trade.PositionClose(position.Ticket());
            Print("Position closed: ", position.Ticket());
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Manage existing positions (trailing stop, etc.)                |
//+------------------------------------------------------------------+
void ManagePositions()
{
   if(!InpUseTrailingStop)
      return;

   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(position.SelectByIndex(i))
      {
         if(position.Magic() == InpMagicNumber)
         {
            UpdateTrailingStop();
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Update trailing stop for current position                       |
//+------------------------------------------------------------------+
void UpdateTrailingStop()
{
   double currentPrice;
   double newStopLoss;
   double point = SymbolInfoDouble(position.Symbol(), SYMBOL_POINT);
   double trailingDistance = InpTrailingStopDistance * point;

   if(position.PositionType() == POSITION_TYPE_BUY)
   {
      currentPrice = SymbolInfoDouble(position.Symbol(), SYMBOL_BID);
      newStopLoss = currentPrice - trailingDistance;

      if(newStopLoss > position.StopLoss() + point)
      {
         trade.PositionModify(position.Ticket(), newStopLoss, position.TakeProfit());
      }
   }
   else if(position.PositionType() == POSITION_TYPE_SELL)
   {
      currentPrice = SymbolInfoDouble(position.Symbol(), SYMBOL_ASK);
      newStopLoss = currentPrice + trailingDistance;

      if(newStopLoss < position.StopLoss() - point || position.StopLoss() == 0)
      {
         trade.PositionModify(position.Ticket(), newStopLoss, position.TakeProfit());
      }
   }
}

//+------------------------------------------------------------------+
//| Reset daily counters                                            |
//+------------------------------------------------------------------+
void ResetDailyCounters()
{
   DailyTradeCount = 0;
   LastDayReset = TimeCurrent();
   Print("Daily counters reset");
}

//+------------------------------------------------------------------+
//| OnTrade event handler                                           |
//+------------------------------------------------------------------+
void OnTrade()
{
   // Handle trade events if needed
   Print("Trade event occurred");
}

//+------------------------------------------------------------------+
//| OnTimer event handler                                           |
//+------------------------------------------------------------------+
void OnTimer()
{
   // Periodic checks if needed
   static datetime lastCheck = 0;

   if(TimeCurrent() - lastCheck >= 60) // Check every minute
   {
      lastCheck = TimeCurrent();

      // Check for any maintenance tasks
      MqlDateTime currentTime, lastResetTime;
      TimeToStruct(TimeCurrent(), currentTime);
      TimeToStruct(LastDayReset, lastResetTime);

      if(currentTime.day != lastResetTime.day)
         ResetDailyCounters();
   }
}
