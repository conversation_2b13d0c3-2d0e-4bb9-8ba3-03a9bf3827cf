===============================================================================
                    AMPD JUMP 75 BIDIRECTIONAL TRADING SYSTEM
                      ENHANCED WITH BUY AND SELL CAPABILITIES
===============================================================================

SYSTEM OVERVIEW:
================
The Jump 75 system has been enhanced to include bidirectional trading capabilities,
allowing it to capture both bullish and bearish momentum bursts in the Jump 75 Index.

ENHANCED FEATURES:
==================
✅ BUY Signal Detection: Green arrows (↑) below candles for bullish momentum bursts
✅ SELL Signal Detection: Red arrows (↓) above candles for bearish momentum bursts
✅ Independent BUY/SELL configuration with separate parameters
✅ Bidirectional Expert Advisor with separate risk management for each direction
✅ Real-time information panel showing both BUY and SELL momentum
✅ Comprehensive alert system for both signal types

FILES INCLUDED:
===============
1. AMPD_Jump75_Indicator.mq5 (Enhanced with bidirectional signals)
2. AMPD_Jump75_EA.mq5 (Enhanced with bidirectional trading)

===============================================================================
                              ENHANCED INDICATOR SETUP
===============================================================================

INDICATOR PARAMETERS:
---------------------

Jump 75 BUY Settings:
- BUY Volatility Threshold: 2.0 (ATR multiplier for bullish momentum detection)
- BUY Momentum Period: 3 (bullish momentum confirmation period)
- BUY Minimum Candle Size: 1.5 (ATR multiplier for bullish candle validation)
- BUY Confirmation Bars: 1 (immediate bullish signal confirmation)

Jump 75 SELL Settings:
- SELL Volatility Threshold: 2.0 (ATR multiplier for bearish momentum detection)
- SELL Momentum Period: 3 (bearish momentum confirmation period)
- SELL Minimum Candle Size: 1.5 (ATR multiplier for bearish candle validation)
- SELL Confirmation Bars: 1 (immediate bearish signal confirmation)

General Settings:
- ATR Period: 14 (volatility calculation period)
- Enable BUY Signals: true (toggle BUY signal detection)
- Enable SELL Signals: true (toggle SELL signal detection)

Alert Settings:
- Show Alerts: true
- Sound Alerts: true
- Email Alerts: false (enable if needed)
- Push Alerts: false (enable if needed)

Display Settings:
- Show Info Panel: true (enhanced with BUY/SELL momentum display)
- Show Trend Line: true (reference line)

VISUAL SIGNALS:
---------------
✅ Green arrows (↑) below candles = BUY signals (bullish momentum bursts)
✅ Red arrows (↓) above candles = SELL signals (bearish momentum bursts)
✅ Gold circles (○) on candles = EXIT signals (momentum reversals)
✅ Blue trend line = Price reference

ENHANCED INFO PANEL:
--------------------
- Line 1: Jump 75 spread information
- Line 2: BUY momentum level (real-time calculation)
- Line 3: SELL momentum level (real-time calculation)
- Line 4: Current signal status (BUY ACTIVE/SELL ACTIVE/EXIT ACTIVE/Waiting)
- Line 5: Enabled signals status and current time

===============================================================================
                              ENHANCED EXPERT ADVISOR SETUP
===============================================================================

EA PARAMETERS:
--------------

Trading Settings:
- Lot Size: 0.01 (adjust based on account size)
- Magic Number: 750001 (unique identifier)
- Symbol: "Jump 75 Index" (adjust to your broker)

Jump 75 BUY Strategy:
- BUY Take Profit: 50 points
- BUY Stop Loss: 25 points
- Maximum Concurrent BUY Trades: 5
- Enable BUY Trading: true

Jump 75 SELL Strategy:
- SELL Take Profit: 50 points
- SELL Stop Loss: 25 points
- Maximum Concurrent SELL Trades: 5
- Enable SELL Trading: true

General Trading:
- Trade on Every Signal: true

Risk Management:
- Max Risk Per Trade: 1.5%
- Use Trailing Stop: true
- Trailing Stop Distance: 15 points
- Max Daily Trades: 20 (combined BUY and SELL)

Time Settings:
- Use Trading Hours: false (24/7 trading)
- Signal Cooldown: 30 seconds

BIDIRECTIONAL TRADING LOGIC:
============================

BUY SIGNAL DETECTION:
---------------------
✅ Must be bullish candle (close > open)
✅ Candle size must exceed 1.5x ATR threshold
✅ Volatility expansion must exceed 2.0x ATR threshold
✅ Momentum confirmation over 3-period lookback
✅ Comparison with recent average candle sizes

SELL SIGNAL DETECTION:
----------------------
✅ Must be bearish candle (close < open)
✅ Candle size must exceed 1.5x ATR threshold
✅ Volatility expansion must exceed 2.0x ATR threshold
✅ Bearish momentum confirmation over 3-period lookback
✅ Comparison with recent average candle sizes

EXIT SIGNAL DETECTION:
----------------------
✅ Detects both bullish and bearish momentum reversals
✅ Triggers closure of all positions when momentum shifts
✅ Works for both BUY and SELL positions

EXPECTED PERFORMANCE:
=====================

SIGNAL FREQUENCY:
-----------------
- BUY Signals: 2-3 per hour during bullish momentum periods
- SELL Signals: 2-3 per hour during bearish momentum periods
- Combined: 4-6 signals per hour during active market conditions
- EXIT Signals: As needed based on momentum reversals

PERFORMANCE TARGETS:
--------------------
BUY Trades:
- Target Win Rate: 60-70%
- Risk/Reward Ratio: 1:2 (25 SL : 50 TP)
- Expected: 2-3 BUY signals per hour

SELL Trades:
- Target Win Rate: 60-70%
- Risk/Reward Ratio: 1:2 (25 SL : 50 TP)
- Expected: 2-3 SELL signals per hour

Combined System:
- Enhanced market coverage with bidirectional trading
- Increased trading opportunities
- Better risk distribution across market directions

INSTALLATION STEPS:
===================

1. REPLACE EXISTING FILES:
--------------------------
- Replace AMPD_Jump75_Indicator.mq5 with enhanced version
- Replace AMPD_Jump75_EA.mq5 with enhanced version

2. COMPILE FILES:
-----------------
- Compile both files in MetaEditor (F7)
- Verify 0 errors, 0 warnings

3. CHART SETUP:
---------------
- Remove old Jump 75 indicator if present
- Add enhanced AMPD_Jump75_Indicator
- Configure parameters as specified above
- Remove old Jump 75 EA if present
- Add enhanced AMPD_Jump75_EA
- Configure parameters as specified above

4. VERIFICATION:
----------------
- Verify both green (BUY) and red (SELL) arrows appear
- Check info panel shows BUY/SELL momentum levels
- Confirm EA shows "BUY Trading: Enabled" and "SELL Trading: Enabled"
- Test alerts for both signal types

OPTIMIZATION OPTIONS:
=====================

FOR MORE FREQUENT SIGNALS:
---------------------------
BUY Signals:
- Reduce BUY Volatility Threshold to 1.8
- Reduce BUY Candle Size to 1.3
- Reduce Signal Cooldown to 15 seconds

SELL Signals:
- Reduce SELL Volatility Threshold to 1.8
- Reduce SELL Candle Size to 1.3
- Reduce Signal Cooldown to 15 seconds

FOR HIGHER QUALITY SIGNALS:
----------------------------
BUY Signals:
- Increase BUY Volatility Threshold to 2.5
- Increase BUY Candle Size to 2.0
- Increase Signal Cooldown to 60 seconds

SELL Signals:
- Increase SELL Volatility Threshold to 2.5
- Increase SELL Candle Size to 2.0
- Increase Signal Cooldown to 60 seconds

TRADING MODES:
==============

1. FULL BIDIRECTIONAL MODE:
----------------------------
- Enable BUY Trading: true
- Enable SELL Trading: true
- Maximum market coverage and opportunities

2. BUY ONLY MODE:
-----------------
- Enable BUY Trading: true
- Enable SELL Trading: false
- Focus on bullish momentum only

3. SELL ONLY MODE:
------------------
- Enable BUY Trading: false
- Enable SELL Trading: true
- Focus on bearish momentum only

MONITORING:
===========

WHAT TO WATCH:
--------------
✅ Green arrows (↑) for BUY signals
✅ Red arrows (↓) for SELL signals
✅ Gold circles (○) for EXIT signals
✅ Info panel showing real-time momentum levels
✅ EA executing both BUY and SELL trades
✅ Proper position management for both directions

ALERT MESSAGES:
---------------
- "🚀 JUMP 75 BUY SIGNAL" for bullish momentum bursts
- "📉 JUMP 75 SELL SIGNAL" for bearish momentum bursts
- "🔄 JUMP 75 EXIT SIGNAL" for momentum reversals

ADVANTAGES OF BIDIRECTIONAL SYSTEM:
====================================

✅ INCREASED OPPORTUNITIES: Captures both bullish and bearish momentum
✅ BETTER MARKET COVERAGE: Works in trending markets in both directions
✅ RISK DISTRIBUTION: Spreads risk across different market movements
✅ ENHANCED PROFITABILITY: More trading opportunities per hour
✅ FLEXIBLE OPERATION: Can be configured for any trading preference
✅ INDEPENDENT OPERATION: Still completely separate from Crash 1000 system

===============================================================================
                              READY FOR ENHANCED TRADING
===============================================================================

The Jump 75 system now provides comprehensive bidirectional trading capabilities
while maintaining its independence from the Crash 1000 system. This enhancement
significantly increases trading opportunities and market coverage.

===============================================================================
