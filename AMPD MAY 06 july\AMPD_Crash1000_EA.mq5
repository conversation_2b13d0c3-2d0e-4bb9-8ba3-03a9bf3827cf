//+------------------------------------------------------------------+
//|                                        AMPD Crash1000 EA.mq5    |
//|                        Copyright 2024, Arise Morok<PERSON> Prince Dynasty |
//|                                             https://www.ampd.com |
//+------------------------------------------------------------------+
#property copyright "Arise Moroka Prince Dynasty"
#property link      "https://ampd.com"
#property description "Expert Advisor for Crash 1000 Index Trading"
#property version   "1.0"

#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\OrderInfo.mqh>

//--- Input parameters
input group "=== TRADING SETTINGS ==="
input double InpLotSize = 0.01;                    // Lot Size
input int    InpMagicNumber = 100001;              // Magic Number
input string InpComment = "AMPD Crash1000";        // Order Comment
input string InpSymbol = "Crash 1000 Index";       // Crash 1000 Symbol

input group "=== CRASH 1000 STRATEGY ==="
input double InpTakeProfit = 75.0;                 // Take Profit (points)
input double InpStopLoss = 35.0;                   // Stop Loss (points)
input int    InpMaxTrades = 3;                     // Maximum Concurrent Trades
input bool   InpTradeOnEverySignal = true;         // Trade on Every Signal

input group "=== RISK MANAGEMENT ==="
input double InpMaxRiskPercent = 2.0;              // Maximum Risk Per Trade (%)
input bool   InpUseTrailingStop = true;            // Use Trailing Stop
input double InpTrailingStopDistance = 20.0;       // Trailing Stop Distance (points)
input int    InpMaxDailyTrades = 15;               // Maximum Daily Trades

input group "=== TIME SETTINGS ==="
input bool   InpUseTradingHours = false;           // Use Trading Hours
input string InpStartTime = "00:00";               // Trading Start Time
input string InpEndTime = "23:59";                 // Trading End Time
input int    InpSignalCooldown = 60;               // Signal Cooldown (seconds)

//--- Global variables
CTrade trade;
CPositionInfo position;
COrderInfo order;

int IndicatorHandle;
datetime LastSignalTime = 0;
datetime LastTradeTime = 0;
int DailyTradeCount = 0;
datetime LastDayReset = 0;

//--- Indicator buffers for signal reading
double CrashSignals[];
double ExitSignals[];

//--- Signal types (must match indicator)
enum ENUM_SIGNAL
{
   SIGNAL_NONE = 0,
   SIGNAL_CRASH_SELL = 1,
   SIGNAL_EXIT = 2
};

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   // Set trade parameters
   trade.SetExpertMagicNumber(InpMagicNumber);
   trade.SetMarginMode();
   trade.SetTypeFillingBySymbol(InpSymbol);
   
   // Load the custom indicator
   IndicatorHandle = iCustom(InpSymbol, PERIOD_M1, "AMPD_Crash1000_Indicator");
   
   if(IndicatorHandle == INVALID_HANDLE)
   {
      Print("Error loading AMPD Crash1000 Indicator");
      return INIT_FAILED;
   }
   
   // Initialize arrays
   ArraySetAsSeries(CrashSignals, true);
   ArraySetAsSeries(ExitSignals, true);
   
   // Reset daily counters
   ResetDailyCounters();
   
   Print("AMPD Crash 1000 EA initialized successfully");
   Print("Symbol: ", InpSymbol);
   Print("Take Profit: ", InpTakeProfit, " | Stop Loss: ", InpStopLoss);
   Print("Max Trades: ", InpMaxTrades, " | Max Daily: ", InpMaxDailyTrades);
   
   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   if(IndicatorHandle != INVALID_HANDLE)
      IndicatorRelease(IndicatorHandle);
   
   Print("AMPD Crash 1000 EA deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // Check if new bar
   static datetime lastBarTime = 0;
   datetime currentBarTime = iTime(InpSymbol, PERIOD_M1, 0);
   
   if(currentBarTime == lastBarTime && !InpTradeOnEverySignal)
      return;
   
   lastBarTime = currentBarTime;
   
   // Reset daily counters if new day
   MqlDateTime currentTime, lastResetTime;
   TimeToStruct(TimeCurrent(), currentTime);
   TimeToStruct(LastDayReset, lastResetTime);
   
   if(currentTime.day != lastResetTime.day)
      ResetDailyCounters();
   
   // Check trading conditions
   if(!IsTradingAllowed())
      return;
   
   // Get indicator signals
   if(!GetIndicatorSignals())
      return;
   
   // Process signals
   ProcessSignals();
   
   // Manage existing positions
   ManagePositions();
}

//+------------------------------------------------------------------+
//| Check if trading is allowed                                     |
//+------------------------------------------------------------------+
bool IsTradingAllowed()
{
   // Check daily trade limit
   if(DailyTradeCount >= InpMaxDailyTrades)
      return false;
   
   // Check trading hours
   if(InpUseTradingHours)
   {
      MqlDateTime currentTime;
      TimeToStruct(TimeCurrent(), currentTime);
      
      int currentMinutes = currentTime.hour * 60 + currentTime.min;
      
      string startParts[];
      string endParts[];
      StringSplit(InpStartTime, ':', startParts);
      StringSplit(InpEndTime, ':', endParts);
      
      int startMinutes = (int)StringToInteger(startParts[0]) * 60 + (int)StringToInteger(startParts[1]);
      int endMinutes = (int)StringToInteger(endParts[0]) * 60 + (int)StringToInteger(endParts[1]);
      
      if(currentMinutes < startMinutes || currentMinutes > endMinutes)
         return false;
   }
   
   // Check signal cooldown
   if(TimeCurrent() - LastTradeTime < InpSignalCooldown)
      return false;
   
   return true;
}

//+------------------------------------------------------------------+
//| Get indicator signals                                           |
//+------------------------------------------------------------------+
bool GetIndicatorSignals()
{
   // Copy indicator buffers
   if(CopyBuffer(IndicatorHandle, 0, 0, 3, CrashSignals) <= 0)
      return false;
   
   if(CopyBuffer(IndicatorHandle, 1, 0, 3, ExitSignals) <= 0)
      return false;
   
   return true;
}

//+------------------------------------------------------------------+
//| Process trading signals                                         |
//+------------------------------------------------------------------+
void ProcessSignals()
{
   // Check for Crash 1000 signals
   if(CrashSignals[0] != 0.0 && CrashSignals[0] != EMPTY_VALUE)
   {
      if(TimeCurrent() - LastSignalTime >= InpSignalCooldown)
      {
         if(CountPositions(POSITION_TYPE_SELL) < InpMaxTrades)
         {
            Print("CRASH 1000 Signal Detected - Executing SELL trade");
            ExecuteCrashTrade();
            LastSignalTime = TimeCurrent();
         }
         else
         {
            Print("CRASH 1000 Signal Detected but max trades reached: ", InpMaxTrades);
         }
      }
   }
   
   // Check for exit signals
   if(ExitSignals[0] != 0.0 && ExitSignals[0] != EMPTY_VALUE)
   {
      Print("EXIT Signal Detected - Closing all Crash 1000 positions");
      CloseAllPositions();
   }
}

//+------------------------------------------------------------------+
//| Execute Crash 1000 trade                                       |
//+------------------------------------------------------------------+
void ExecuteCrashTrade()
{
   double price = SymbolInfoDouble(InpSymbol, SYMBOL_BID);
   double sl = price + InpStopLoss * SymbolInfoDouble(InpSymbol, SYMBOL_POINT);
   double tp = price - InpTakeProfit * SymbolInfoDouble(InpSymbol, SYMBOL_POINT);
   
   // Calculate lot size based on risk
   double lotSize = CalculateLotSize(InpStopLoss);
   
   if(trade.Sell(lotSize, InpSymbol, price, sl, tp, InpComment))
   {
      Print("Crash 1000 SELL order executed: ", trade.ResultOrder());
      Print("Price: ", price, " | SL: ", sl, " | TP: ", tp, " | Lot: ", lotSize);
      DailyTradeCount++;
      LastTradeTime = TimeCurrent();
   }
   else
   {
      Print("Crash 1000 SELL order failed: ", trade.ResultRetcodeDescription());
   }
}

//+------------------------------------------------------------------+
//| Calculate lot size based on risk management                     |
//+------------------------------------------------------------------+
double CalculateLotSize(double stopLossPoints)
{
   double accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
   double riskAmount = accountBalance * InpMaxRiskPercent / 100.0;
   
   double pointValue = SymbolInfoDouble(InpSymbol, SYMBOL_TRADE_TICK_VALUE);
   double pointSize = SymbolInfoDouble(InpSymbol, SYMBOL_POINT);
   
   if(pointValue == 0 || pointSize == 0)
      return InpLotSize;
   
   double stopLossValue = stopLossPoints * pointSize;
   double lotSize = riskAmount / (stopLossValue * pointValue / pointSize);
   
   // Normalize lot size
   double minLot = SymbolInfoDouble(InpSymbol, SYMBOL_VOLUME_MIN);
   double maxLot = SymbolInfoDouble(InpSymbol, SYMBOL_VOLUME_MAX);
   double lotStep = SymbolInfoDouble(InpSymbol, SYMBOL_VOLUME_STEP);
   
   lotSize = MathMax(minLot, MathMin(maxLot, lotSize));
   lotSize = NormalizeDouble(lotSize / lotStep, 0) * lotStep;
   
   return lotSize;
}

//+------------------------------------------------------------------+
//| Count positions by type                                         |
//+------------------------------------------------------------------+
int CountPositions(ENUM_POSITION_TYPE posType)
{
   int count = 0;

   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(position.SelectByIndex(i))
      {
         if(position.Magic() == InpMagicNumber && position.Symbol() == InpSymbol)
         {
            if(position.PositionType() == posType)
               count++;
         }
      }
   }

   return count;
}

//+------------------------------------------------------------------+
//| Close all positions                                             |
//+------------------------------------------------------------------+
void CloseAllPositions()
{
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(position.SelectByIndex(i))
      {
         if(position.Magic() == InpMagicNumber && position.Symbol() == InpSymbol)
         {
            trade.PositionClose(position.Ticket());
            Print("Crash 1000 position closed: ", position.Ticket());
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Manage existing positions (trailing stop, etc.)                |
//+------------------------------------------------------------------+
void ManagePositions()
{
   if(!InpUseTrailingStop)
      return;

   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(position.SelectByIndex(i))
      {
         if(position.Magic() == InpMagicNumber && position.Symbol() == InpSymbol)
         {
            UpdateTrailingStop();
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Update trailing stop for current position                       |
//+------------------------------------------------------------------+
void UpdateTrailingStop()
{
   double currentPrice;
   double newStopLoss;
   double point = SymbolInfoDouble(position.Symbol(), SYMBOL_POINT);
   double trailingDistance = InpTrailingStopDistance * point;

   if(position.PositionType() == POSITION_TYPE_SELL)
   {
      currentPrice = SymbolInfoDouble(position.Symbol(), SYMBOL_ASK);
      newStopLoss = currentPrice + trailingDistance;

      if(newStopLoss < position.StopLoss() - point || position.StopLoss() == 0)
      {
         trade.PositionModify(position.Ticket(), newStopLoss, position.TakeProfit());
         Print("Trailing stop updated for Crash 1000 position: ", position.Ticket());
      }
   }
}

//+------------------------------------------------------------------+
//| Reset daily counters                                            |
//+------------------------------------------------------------------+
void ResetDailyCounters()
{
   DailyTradeCount = 0;
   LastDayReset = TimeCurrent();
   Print("Daily counters reset for Crash 1000 EA");
}

//+------------------------------------------------------------------+
//| OnTrade event handler                                           |
//+------------------------------------------------------------------+
void OnTrade()
{
   Print("Crash 1000 trade event occurred");
}

//+------------------------------------------------------------------+
//| OnTimer event handler                                           |
//+------------------------------------------------------------------+
void OnTimer()
{
   static datetime lastCheck = 0;

   if(TimeCurrent() - lastCheck >= 60) // Check every minute
   {
      lastCheck = TimeCurrent();

      // Check for any maintenance tasks
      MqlDateTime currentTime, lastResetTime;
      TimeToStruct(TimeCurrent(), currentTime);
      TimeToStruct(LastDayReset, lastResetTime);

      if(currentTime.day != lastResetTime.day)
         ResetDailyCounters();
   }
}
