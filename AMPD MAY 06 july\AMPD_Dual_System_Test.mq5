//+------------------------------------------------------------------+
//|                                    AMPD Dual System Test.mq5    |
//|                        Copyright 2024, <PERSON><PERSON> |
//|                                             https://www.ampd.com |
//+------------------------------------------------------------------+
#property copyright "Arise Moroka Prince Dynasty"
#property link      "https://ampd.com"
#property description "Test Script for AMPD Dual Trading System"
#property version   "1.0"
#property script_show_inputs

//--- Input parameters
input string InpCrashSymbol = "Crash 1000 Index";  // Crash 1000 Symbol
input string InpJumpSymbol = "Jump 75 Index";      // Jump 75 Symbol
input bool   InpTestIndicators = true;             // Test Indicators
input bool   InpTestEAs = true;                    // Test EA Integration
input bool   InpShowDetails = true;                // Show Detailed Results

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
   Print("=== AMPD DUAL TRADING SYSTEM TEST ===");
   Print("Testing both Crash 1000 and Jump 75 systems...");
   Print("Crash Symbol: ", InpCrashSymbol);
   Print("Jump Symbol: ", InpJumpSymbol);
   Print("==========================================");
   
   bool allTestsPassed = true;
   
   // Test 1: Basic System Validation
   Print("1. Testing basic system components...");
   if(TestBasicComponents())
   {
      Print("   ✓ PASSED: Basic components working");
   }
   else
   {
      Print("   ✗ FAILED: Basic components error");
      allTestsPassed = false;
   }
   
   // Test 2: Crash 1000 System
   Print("2. Testing Crash 1000 system...");
   if(TestCrashSystem())
   {
      Print("   ✓ PASSED: Crash 1000 system functional");
   }
   else
   {
      Print("   ✗ FAILED: Crash 1000 system error");
      allTestsPassed = false;
   }
   
   // Test 3: Jump 75 System
   Print("3. Testing Jump 75 system...");
   if(TestJumpSystem())
   {
      Print("   ✓ PASSED: Jump 75 system functional");
   }
   else
   {
      Print("   ✗ FAILED: Jump 75 system error");
      allTestsPassed = false;
   }
   
   // Test 4: Symbol Compatibility
   Print("4. Testing symbol compatibility...");
   if(TestSymbolCompatibility())
   {
      Print("   ✓ PASSED: Symbols compatible");
   }
   else
   {
      Print("   ✗ FAILED: Symbol compatibility issues");
      allTestsPassed = false;
   }
   
   // Test 5: Trading Environment
   Print("5. Testing trading environment...");
   if(TestTradingEnvironment())
   {
      Print("   ✓ PASSED: Trading environment ready");
   }
   else
   {
      Print("   ✗ FAILED: Trading environment issues");
      allTestsPassed = false;
   }
   
   Print("==========================================");
   if(allTestsPassed)
   {
      Print("🎉 ALL TESTS PASSED!");
      Print("✅ Crash 1000 System: Ready for deployment");
      Print("✅ Jump 75 System: Ready for deployment");
      Print("✅ Both systems can run simultaneously");
      Print("✅ Arrow visibility optimized");
      Print("✅ Signal frequency optimized");
   }
   else
   {
      Print("❌ SOME TESTS FAILED");
      Print("Please check the errors above before deployment");
   }
   Print("==========================================");
}

//+------------------------------------------------------------------+
//| Test basic system components                                    |
//+------------------------------------------------------------------+
bool TestBasicComponents()
{
   try
   {
      // Test basic MQL5 functions
      double test = MathAbs(-10.5);
      datetime time = TimeCurrent();
      
      // Test TimeToStruct fix
      MqlDateTime timeStruct;
      bool timeResult = TimeToStruct(time, timeStruct);
      
      if(InpShowDetails)
      {
         Print("   Basic math test: ", test);
         Print("   Current time: ", TimeToString(time));
         Print("   TimeToStruct result: ", timeResult);
         Print("   Current day: ", timeStruct.day);
      }
      
      return (test == 10.5 && time > 0 && timeResult);
   }
   catch(string error)
   {
      Print("Basic components error: ", error);
      return false;
   }
}

//+------------------------------------------------------------------+
//| Test Crash 1000 system                                         |
//+------------------------------------------------------------------+
bool TestCrashSystem()
{
   try
   {
      // Test Crash 1000 indicator loading
      int crashIndicator = iCustom(InpCrashSymbol, PERIOD_M1, "AMPD_Crash1000_Indicator");
      
      if(crashIndicator == INVALID_HANDLE)
      {
         Print("   ERROR: Cannot load AMPD_Crash1000_Indicator");
         Print("   Please ensure the indicator is compiled");
         return false;
      }
      
      // Test data retrieval
      double crashSignals[], exitSignals[];
      ArraySetAsSeries(crashSignals, true);
      ArraySetAsSeries(exitSignals, true);
      
      int crashCopied = CopyBuffer(crashIndicator, 0, 0, 10, crashSignals);
      int exitCopied = CopyBuffer(crashIndicator, 1, 0, 10, exitSignals);
      
      IndicatorRelease(crashIndicator);
      
      if(crashCopied <= 0 || exitCopied <= 0)
      {
         Print("   ERROR: Cannot retrieve Crash 1000 indicator data");
         return false;
      }
      
      // Test RSI and ATR indicators
      int rsiHandle = iRSI(InpCrashSymbol, PERIOD_M1, 14, PRICE_CLOSE);
      int atrHandle = iATR(InpCrashSymbol, PERIOD_M1, 14);
      
      bool indicatorsOK = (rsiHandle != INVALID_HANDLE && atrHandle != INVALID_HANDLE);
      
      if(rsiHandle != INVALID_HANDLE) IndicatorRelease(rsiHandle);
      if(atrHandle != INVALID_HANDLE) IndicatorRelease(atrHandle);
      
      if(InpShowDetails)
      {
         Print("   Crash signals copied: ", crashCopied);
         Print("   Exit signals copied: ", exitCopied);
         Print("   RSI/ATR indicators: ", indicatorsOK ? "OK" : "FAILED");
      }
      
      return indicatorsOK;
   }
   catch(string error)
   {
      Print("Crash 1000 system error: ", error);
      return false;
   }
}

//+------------------------------------------------------------------+
//| Test Jump 75 system                                            |
//+------------------------------------------------------------------+
bool TestJumpSystem()
{
   try
   {
      // Test Jump 75 indicator loading
      int jumpIndicator = iCustom(InpJumpSymbol, PERIOD_M1, "AMPD_Jump75_Indicator");
      
      if(jumpIndicator == INVALID_HANDLE)
      {
         Print("   ERROR: Cannot load AMPD_Jump75_Indicator");
         Print("   Please ensure the indicator is compiled");
         return false;
      }
      
      // Test data retrieval
      double jumpSignals[], exitSignals[];
      ArraySetAsSeries(jumpSignals, true);
      ArraySetAsSeries(exitSignals, true);
      
      int jumpCopied = CopyBuffer(jumpIndicator, 0, 0, 10, jumpSignals);
      int exitCopied = CopyBuffer(jumpIndicator, 1, 0, 10, exitSignals);
      
      IndicatorRelease(jumpIndicator);
      
      if(jumpCopied <= 0 || exitCopied <= 0)
      {
         Print("   ERROR: Cannot retrieve Jump 75 indicator data");
         return false;
      }
      
      // Test ATR indicator
      int atrHandle = iATR(InpJumpSymbol, PERIOD_M1, 14);
      bool atrOK = (atrHandle != INVALID_HANDLE);
      
      if(atrHandle != INVALID_HANDLE) IndicatorRelease(atrHandle);
      
      if(InpShowDetails)
      {
         Print("   Jump signals copied: ", jumpCopied);
         Print("   Exit signals copied: ", exitCopied);
         Print("   ATR indicator: ", atrOK ? "OK" : "FAILED");
      }
      
      return atrOK;
   }
   catch(string error)
   {
      Print("Jump 75 system error: ", error);
      return false;
   }
}

//+------------------------------------------------------------------+
//| Test symbol compatibility                                       |
//+------------------------------------------------------------------+
bool TestSymbolCompatibility()
{
   try
   {
      // Test Crash 1000 symbol
      double crashPoint = SymbolInfoDouble(InpCrashSymbol, SYMBOL_POINT);
      double crashMinLot = SymbolInfoDouble(InpCrashSymbol, SYMBOL_VOLUME_MIN);
      double crashTickValue = SymbolInfoDouble(InpCrashSymbol, SYMBOL_TRADE_TICK_VALUE);
      
      // Test Jump 75 symbol
      double jumpPoint = SymbolInfoDouble(InpJumpSymbol, SYMBOL_POINT);
      double jumpMinLot = SymbolInfoDouble(InpJumpSymbol, SYMBOL_VOLUME_MIN);
      double jumpTickValue = SymbolInfoDouble(InpJumpSymbol, SYMBOL_TRADE_TICK_VALUE);
      
      bool crashSymbolOK = (crashPoint > 0 && crashMinLot > 0 && crashTickValue > 0);
      bool jumpSymbolOK = (jumpPoint > 0 && jumpMinLot > 0 && jumpTickValue > 0);
      
      if(InpShowDetails)
      {
         Print("   Crash 1000 Symbol Info:");
         Print("     Point: ", crashPoint);
         Print("     Min Lot: ", crashMinLot);
         Print("     Tick Value: ", crashTickValue);
         Print("   Jump 75 Symbol Info:");
         Print("     Point: ", jumpPoint);
         Print("     Min Lot: ", jumpMinLot);
         Print("     Tick Value: ", jumpTickValue);
      }
      
      if(!crashSymbolOK)
         Print("   WARNING: Crash 1000 symbol may not be available or correctly named");
      
      if(!jumpSymbolOK)
         Print("   WARNING: Jump 75 symbol may not be available or correctly named");
      
      return (crashSymbolOK && jumpSymbolOK);
   }
   catch(string error)
   {
      Print("Symbol compatibility error: ", error);
      return false;
   }
}

//+------------------------------------------------------------------+
//| Test trading environment                                        |
//+------------------------------------------------------------------+
bool TestTradingEnvironment()
{
   try
   {
      // Check trading permissions
      bool autoTradingAllowed = TerminalInfoInteger(TERMINAL_TRADE_ALLOWED);
      bool expertAllowed = MQLInfoInteger(MQL_TRADE_ALLOWED);
      
      // Check account info
      double balance = AccountInfoDouble(ACCOUNT_BALANCE);
      double equity = AccountInfoDouble(ACCOUNT_EQUITY);
      string currency = AccountInfoString(ACCOUNT_CURRENCY);
      
      // Check market status for both symbols
      bool crashMarketOpen = SymbolInfoInteger(InpCrashSymbol, SYMBOL_TRADE_MODE) != SYMBOL_TRADE_MODE_DISABLED;
      bool jumpMarketOpen = SymbolInfoInteger(InpJumpSymbol, SYMBOL_TRADE_MODE) != SYMBOL_TRADE_MODE_DISABLED;
      
      if(InpShowDetails)
      {
         Print("   Auto Trading Allowed: ", autoTradingAllowed ? "YES" : "NO");
         Print("   Expert Trading Allowed: ", expertAllowed ? "YES" : "NO");
         Print("   Account Balance: ", balance, " ", currency);
         Print("   Account Equity: ", equity, " ", currency);
         Print("   Crash 1000 Market: ", crashMarketOpen ? "OPEN" : "CLOSED");
         Print("   Jump 75 Market: ", jumpMarketOpen ? "OPEN" : "CLOSED");
      }
      
      if(!autoTradingAllowed)
         Print("   WARNING: Automated trading is disabled in terminal");
      
      if(!expertAllowed)
         Print("   WARNING: Expert Advisor trading is disabled");
      
      return (balance > 0 && equity > 0);
   }
   catch(string error)
   {
      Print("Trading environment error: ", error);
      return false;
   }
}

//+------------------------------------------------------------------+
//| Display system summary                                          |
//+------------------------------------------------------------------+
void DisplaySystemSummary()
{
   Print("=== AMPD DUAL SYSTEM SUMMARY ===");
   Print("📊 CRASH 1000 SYSTEM:");
   Print("   - Indicator: AMPD_Crash1000_Indicator.mq5");
   Print("   - EA: AMPD_Crash1000_EA.mq5");
   Print("   - Strategy: Bearish spike detection with RSI confirmation");
   Print("   - Signals: Red arrows above candles → SELL orders");
   Print("   - Expected: 1-2 signals per hour during volatile periods");
   Print("");
   Print("🚀 JUMP 75 SYSTEM:");
   Print("   - Indicator: AMPD_Jump75_Indicator.mq5");
   Print("   - EA: AMPD_Jump75_EA.mq5");
   Print("   - Strategy: Momentum burst detection with volatility confirmation");
   Print("   - Signals: Green arrows below candles → BUY orders");
   Print("   - Expected: 3-5 signals per hour during active periods");
   Print("");
   Print("⚙️ SYSTEM FEATURES:");
   Print("   - Independent operation (can run simultaneously)");
   Print("   - Real-time signal processing");
   Print("   - Comprehensive risk management");
   Print("   - Multiple alert types (sound, popup, email, push)");
   Print("   - Live information panels");
   Print("   - Professional arrow visibility");
   Print("================================");
}
