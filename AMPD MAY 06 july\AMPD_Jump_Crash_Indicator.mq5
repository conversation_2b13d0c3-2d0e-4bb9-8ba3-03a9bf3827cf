//+------------------------------------------------------------------+
//|                                    AMPD Jump Crash Indicator.mq5 |
//|                      Copyright 2024, <PERSON><PERSON> |
//|                                             https://www.ampd.com |
//+------------------------------------------------------------------+
#property copyright "Arise Moroka Prince Dynasty"
#property link      "https://ampd.com"
#property description "Comprehensive Jump 75 & Crash 1000 Trading Indicator"
#property version   "1.0"
#property strict

#property indicator_chart_window
#property indicator_buffers 6
#property indicator_plots   4

// Plot definitions with enhanced visibility
#property indicator_label1  "Jump Signal BUY"
#property indicator_type1   DRAW_ARROW
#property indicator_color1  clrLimeGreen
#property indicator_width1  5
#property indicator_style1  STYLE_SOLID

#property indicator_label2  "Crash Signal SELL"
#property indicator_type2   DRAW_ARROW
#property indicator_color2  clrCrimson
#property indicator_width2  5
#property indicator_style2  STYLE_SOLID

#property indicator_label3  "Exit Signal"
#property indicator_type3   DRAW_ARROW
#property indicator_color3  clrGold
#property indicator_width3  4
#property indicator_style3  STYLE_SOLID

#property indicator_label4  "Volatility Reference"
#property indicator_type4   DRAW_LINE
#property indicator_color4  clrDodgerBlue
#property indicator_width4  2
#property indicator_style4  STYLE_DOT

//--- Input parameters
input group "=== JUMP 75 SETTINGS (Optimized for Frequent Signals) ==="
input double InpJumpVolatilityMultiplier = 2.0;    // Jump Volatility Multiplier (Reduced for more signals)
input int    InpJumpMomentumPeriod = 3;            // Jump Momentum Period (Faster detection)
input double InpJumpCandleSizeMin = 1.2;           // Jump Minimum Candle Size Multiplier (Lower threshold)
input int    InpJumpConfirmationBars = 1;          // Jump Confirmation Bars (Immediate signals)

input group "=== CRASH 1000 SETTINGS (Optimized for Frequent Signals) ==="
input double InpCrashVolatilityMultiplier = 2.5;   // Crash Volatility Multiplier (Reduced for more signals)
input int    InpCrashRSIPeriod = 10;               // Crash RSI Period (Faster RSI)
input double InpCrashRSILevel = 60.0;              // Crash RSI Overbought Level (Earlier detection)
input double InpCrashCandleSizeMin = 1.5;          // Crash Minimum Candle Size Multiplier (Lower threshold)

input group "=== GENERAL SETTINGS ==="
input int    InpATRPeriod = 14;                    // ATR Period for Volatility
input int    InpRefreshSeconds = 60;               // Refresh Period (seconds)
input bool   InpShowAlerts = true;                 // Show Alerts
input bool   InpShowInfo = true;                   // Show Info Panel

//--- Indicator buffers
double JumpSignalBuffer[];
double CrashSignalBuffer[];
double ExitSignalBuffer[];
double VolatilityBuffer[];
double RSIBuffer[];
double ATRBuffer[];

//--- Indicator handles
int RSIHandle;
int ATRHandle;

//--- Global variables
datetime LastSignalTime = 0;
datetime LastRefreshTime = 0;
string InfoPanel = "AMPD_InfoPanel";
long CurrentChartID;

//--- Signal types
enum SIGNAL_TYPE
{
   SIGNAL_NONE = 0,
   SIGNAL_JUMP = 1,
   SIGNAL_CRASH = -1,
   SIGNAL_EXIT = 2
};

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
   CurrentChartID = ChartID();

   // Initialize indicator buffers
   SetIndexBuffer(0, JumpSignalBuffer, INDICATOR_DATA);
   SetIndexBuffer(1, CrashSignalBuffer, INDICATOR_DATA);
   SetIndexBuffer(2, ExitSignalBuffer, INDICATOR_DATA);
   SetIndexBuffer(3, VolatilityBuffer, INDICATOR_DATA);
   SetIndexBuffer(4, RSIBuffer, INDICATOR_CALCULATIONS);
   SetIndexBuffer(5, ATRBuffer, INDICATOR_CALCULATIONS);
   
   // Set array properties
   ArraySetAsSeries(JumpSignalBuffer, true);
   ArraySetAsSeries(CrashSignalBuffer, true);
   ArraySetAsSeries(ExitSignalBuffer, true);
   ArraySetAsSeries(VolatilityBuffer, true);
   ArraySetAsSeries(RSIBuffer, true);
   ArraySetAsSeries(ATRBuffer, true);
   
   // Set plot properties with enhanced arrow symbols
   PlotIndexSetInteger(0, PLOT_ARROW, 241);  // Large up arrow for Jump (BUY)
   PlotIndexSetInteger(1, PLOT_ARROW, 242);  // Large down arrow for Crash (SELL)
   PlotIndexSetInteger(2, PLOT_ARROW, 108);  // Diamond for Exit

   // Set empty values
   PlotIndexSetDouble(0, PLOT_EMPTY_VALUE, EMPTY_VALUE);
   PlotIndexSetDouble(1, PLOT_EMPTY_VALUE, EMPTY_VALUE);
   PlotIndexSetDouble(2, PLOT_EMPTY_VALUE, EMPTY_VALUE);
   PlotIndexSetDouble(3, PLOT_EMPTY_VALUE, EMPTY_VALUE);

   // Set arrow positioning offsets for better visibility
   PlotIndexSetInteger(0, PLOT_ARROW_SHIFT, -10);  // Jump arrows below candle
   PlotIndexSetInteger(1, PLOT_ARROW_SHIFT, 10);   // Crash arrows above candle
   PlotIndexSetInteger(2, PLOT_ARROW_SHIFT, 0);    // Exit arrows on price
   
   // Create indicator handles
   RSIHandle = iRSI(_Symbol, PERIOD_M1, InpCrashRSIPeriod, PRICE_CLOSE);
   ATRHandle = iATR(_Symbol, PERIOD_M1, InpATRPeriod);
   
   if(RSIHandle == INVALID_HANDLE || ATRHandle == INVALID_HANDLE)
   {
      Print("Error creating indicator handles");
      return INIT_FAILED;
   }
   
   // Set indicator name
   IndicatorSetString(INDICATOR_SHORTNAME, "AMPD Jump Crash Indicator");
   IndicatorSetInteger(INDICATOR_DIGITS, _Digits);
   
   // Create info panel
   if(InpShowInfo)
      CreateInfoPanel();

   Print("AMPD Jump Crash Indicator initialized successfully");
   Print("Arrow Signals: Jump (Green ↑), Crash (Red ↓), Exit (Gold ♦)");
   Print("Make sure chart has dark background for optimal arrow visibility!");
   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Custom indicator deinitialization function                      |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // Release indicator handles
   if(RSIHandle != INVALID_HANDLE)
      IndicatorRelease(RSIHandle);
   if(ATRHandle != INVALID_HANDLE)
      IndicatorRelease(ATRHandle);
   
   // Delete info panel
   ObjectDelete(CurrentChartID, InfoPanel);
   ObjectDelete(CurrentChartID, InfoPanel + "_bg");
   
   ChartRedraw();
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
   if(rates_total < InpATRPeriod + InpJumpMomentumPeriod)
      return 0;
   
   // Set arrays as series
   ArraySetAsSeries(time, true);
   ArraySetAsSeries(open, true);
   ArraySetAsSeries(high, true);
   ArraySetAsSeries(low, true);
   ArraySetAsSeries(close, true);
   
   // Check for real-time refresh
   if(TimeCurrent() - LastRefreshTime >= InpRefreshSeconds)
   {
      LastRefreshTime = TimeCurrent();
      // Force recalculation of recent bars
      int start = MathMax(0, rates_total - 100);
      for(int i = start; i < rates_total; i++)
      {
         CalculateSignals(i, rates_total, time, open, high, low, close);
      }
   }
   else
   {
      // Normal calculation
      int limit = prev_calculated == 0 ? rates_total - InpATRPeriod - 1 : rates_total - prev_calculated;
      for(int i = 0; i < limit && !IsStopped(); i++)
      {
         CalculateSignals(i, rates_total, time, open, high, low, close);
      }
   }
   
   // Update info panel
   if(InpShowInfo)
      UpdateInfoPanel();
   
   return rates_total;
}

//+------------------------------------------------------------------+
//| Calculate signals for given bar                                 |
//+------------------------------------------------------------------+
void CalculateSignals(int index, int rates_total, const datetime &time[],
                     const double &open[], const double &high[],
                     const double &low[], const double &close[])
{
   // Initialize buffers with EMPTY_VALUE for proper arrow display
   JumpSignalBuffer[index] = EMPTY_VALUE;
   CrashSignalBuffer[index] = EMPTY_VALUE;
   ExitSignalBuffer[index] = EMPTY_VALUE;
   VolatilityBuffer[index] = EMPTY_VALUE;
   
   // Get indicator values
   if(!GetIndicatorValues(index))
      return;
   
   // Calculate current candle properties
   double candleSize = MathAbs(close[index] - open[index]);
   double candleRange = high[index] - low[index];
   double atr = ATRBuffer[index];
   double rsi = RSIBuffer[index];
   
   VolatilityBuffer[index] = atr;
   
   // Check for Jump 75 signal (Bullish momentum burst)
   if(IsJumpSignal(index, candleSize, atr, close, open, high, low))
   {
      // Position arrow below the low with sufficient spacing
      double arrowPosition = low[index] - (atr > 0 ? atr * 1.5 : (high[index] - low[index]) * 2.0);
      JumpSignalBuffer[index] = arrowPosition;

      if(InpShowAlerts && time[index] != LastSignalTime)
      {
         Alert("AMPD: JUMP 75 BUY Signal at ", _Symbol, " - Price: ", DoubleToString(close[index], _Digits), " - ", TimeToString(time[index]));
         LastSignalTime = time[index];
      }
   }

   // Check for Crash 1000 signal (Bearish spike with overbought RSI)
   if(IsCrashSignal(index, candleSize, atr, rsi, close, open, high, low))
   {
      // Position arrow above the high with sufficient spacing
      double arrowPosition = high[index] + (atr > 0 ? atr * 1.5 : (high[index] - low[index]) * 2.0);
      CrashSignalBuffer[index] = arrowPosition;

      if(InpShowAlerts && time[index] != LastSignalTime)
      {
         Alert("AMPD: CRASH 1000 SELL Signal at ", _Symbol, " - Price: ", DoubleToString(close[index], _Digits), " - ", TimeToString(time[index]));
         LastSignalTime = time[index];
      }
   }

   // Check for exit signals
   if(IsExitSignal(index, close, open))
   {
      // Position exit signal at the close price
      ExitSignalBuffer[index] = close[index];

      if(InpShowAlerts && time[index] != LastSignalTime)
      {
         Alert("AMPD: EXIT Signal at ", _Symbol, " - Price: ", DoubleToString(close[index], _Digits), " - ", TimeToString(time[index]));
         LastSignalTime = time[index];
      }
   }
}

//+------------------------------------------------------------------+
//| Get indicator values for current bar                            |
//+------------------------------------------------------------------+
bool GetIndicatorValues(int index)
{
   // Get RSI values
   if(CopyBuffer(RSIHandle, 0, index, 1, RSIBuffer) <= 0)
      return false;

   // Get ATR values
   if(CopyBuffer(ATRHandle, 0, index, 1, ATRBuffer) <= 0)
      return false;

   return true;
}

//+------------------------------------------------------------------+
//| Check for Jump 75 signal (Momentum Burst Entry)                |
//+------------------------------------------------------------------+
bool IsJumpSignal(int index, double candleSize, double atr,
                  const double &close[], const double &open[],
                  const double &high[], const double &low[])
{
   if(atr <= 0) return false;

   // Check if current candle is bullish
   if(close[index] <= open[index]) return false;

   // Check candle size against ATR
   if(candleSize < atr * InpJumpCandleSizeMin) return false;

   // Check volatility expansion
   if(candleSize < atr * InpJumpVolatilityMultiplier) return false;

   // Check momentum confirmation
   bool momentumConfirmed = true;
   for(int i = 1; i <= InpJumpConfirmationBars && (index + i) < ArraySize(close); i++)
   {
      if(close[index] <= close[index + i])
      {
         momentumConfirmed = false;
         break;
      }
   }

   // Additional momentum check - compare with recent average
   double avgCandle = 0;
   int count = 0;
   for(int i = 1; i <= InpJumpMomentumPeriod && (index + i) < ArraySize(close); i++)
   {
      avgCandle += MathAbs(close[index + i] - open[index + i]);
      count++;
   }

   if(count > 0)
   {
      avgCandle /= count;
      if(candleSize < avgCandle * InpJumpVolatilityMultiplier)
         momentumConfirmed = false;
   }

   return momentumConfirmed;
}

//+------------------------------------------------------------------+
//| Check for Crash 1000 signal (Spike Hunter Sell)               |
//+------------------------------------------------------------------+
bool IsCrashSignal(int index, double candleSize, double atr, double rsi,
                   const double &close[], const double &open[],
                   const double &high[], const double &low[])
{
   if(atr <= 0) return false;

   // Check if current candle is bearish
   if(close[index] >= open[index]) return false;

   // Check RSI overbought condition
   if(rsi < InpCrashRSILevel) return false;

   // Check candle size against ATR
   if(candleSize < atr * InpCrashCandleSizeMin) return false;

   // Check volatility expansion (crash detection)
   if(candleSize < atr * InpCrashVolatilityMultiplier) return false;

   // Additional crash pattern validation
   double bodySize = MathAbs(close[index] - open[index]);
   double shadowSize = high[index] - MathMax(close[index], open[index]);

   // Crash candles typically have large bodies with small upper shadows
   if(shadowSize > bodySize * 0.3) return false;

   // Check if this is significantly larger than recent candles
   double avgCandle = 0;
   int count = 0;
   for(int i = 1; i <= 10 && (index + i) < ArraySize(close); i++)
   {
      avgCandle += MathAbs(close[index + i] - open[index + i]);
      count++;
   }

   if(count > 0)
   {
      avgCandle /= count;
      return (candleSize > avgCandle * 2.0); // Must be at least 2x average
   }

   return true;
}

//+------------------------------------------------------------------+
//| Check for exit signals                                          |
//+------------------------------------------------------------------+
bool IsExitSignal(int index, const double &close[], const double &open[])
{
   // Simple exit logic - opposite signal or momentum reversal
   if(index + 2 >= ArraySize(close)) return false;

   // Check for momentum reversal
   bool bullishReversal = (close[index] > open[index]) &&
                         (close[index + 1] < open[index + 1]) &&
                         (close[index + 2] < open[index + 2]);

   bool bearishReversal = (close[index] < open[index]) &&
                         (close[index + 1] > open[index + 1]) &&
                         (close[index + 2] > open[index + 2]);

   return (bullishReversal || bearishReversal);
}

//+------------------------------------------------------------------+
//| Create information panel                                         |
//+------------------------------------------------------------------+
void CreateInfoPanel()
{
   // Create background rectangle
   ObjectCreate(CurrentChartID, InfoPanel + "_bg", OBJ_RECTANGLE_LABEL, 0, 0, 0);
   ObjectSetInteger(CurrentChartID, InfoPanel + "_bg", OBJPROP_XDISTANCE, 10);
   ObjectSetInteger(CurrentChartID, InfoPanel + "_bg", OBJPROP_YDISTANCE, 30);
   ObjectSetInteger(CurrentChartID, InfoPanel + "_bg", OBJPROP_XSIZE, 250);
   ObjectSetInteger(CurrentChartID, InfoPanel + "_bg", OBJPROP_YSIZE, 120);
   ObjectSetInteger(CurrentChartID, InfoPanel + "_bg", OBJPROP_BGCOLOR, clrDarkBlue);
   ObjectSetInteger(CurrentChartID, InfoPanel + "_bg", OBJPROP_BORDER_TYPE, BORDER_FLAT);
   ObjectSetInteger(CurrentChartID, InfoPanel + "_bg", OBJPROP_BORDER_COLOR, clrWhite);
   ObjectSetInteger(CurrentChartID, InfoPanel + "_bg", OBJPROP_CORNER, CORNER_LEFT_UPPER);

   // Create text label
   ObjectCreate(CurrentChartID, InfoPanel, OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(CurrentChartID, InfoPanel, OBJPROP_XDISTANCE, 15);
   ObjectSetInteger(CurrentChartID, InfoPanel, OBJPROP_YDISTANCE, 35);
   ObjectSetInteger(CurrentChartID, InfoPanel, OBJPROP_COLOR, clrWhite);
   ObjectSetInteger(CurrentChartID, InfoPanel, OBJPROP_FONTSIZE, 9);
   ObjectSetString(CurrentChartID, InfoPanel, OBJPROP_FONT, "Consolas");
   ObjectSetInteger(CurrentChartID, InfoPanel, OBJPROP_CORNER, CORNER_LEFT_UPPER);
}

//+------------------------------------------------------------------+
//| Update information panel                                         |
//+------------------------------------------------------------------+
void UpdateInfoPanel()
{
   if(!InpShowInfo) return;

   string info = "AMPD Jump Crash Indicator\n";
   info += "Symbol: " + _Symbol + "\n";
   info += "Timeframe: M1\n";
   info += "Last Update: " + TimeToString(TimeCurrent(), TIME_SECONDS) + "\n";
   info += "ATR: " + DoubleToString(ATRBuffer[0], _Digits) + "\n";
   info += "RSI: " + DoubleToString(RSIBuffer[0], 2) + "\n";
   info += "Status: Active";

   ObjectSetString(CurrentChartID, InfoPanel, OBJPROP_TEXT, info);
}

//+------------------------------------------------------------------+
//| Get current signal type for EA integration                      |
//+------------------------------------------------------------------+
SIGNAL_TYPE GetCurrentSignal()
{
   if(JumpSignalBuffer[0] != 0.0) return SIGNAL_JUMP;
   if(CrashSignalBuffer[0] != 0.0) return SIGNAL_CRASH;
   if(ExitSignalBuffer[0] != 0.0) return SIGNAL_EXIT;
   return SIGNAL_NONE;
}
