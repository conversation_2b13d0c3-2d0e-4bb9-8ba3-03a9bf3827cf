//+------------------------------------------------------------------+
//|                           AMPD HighFrequency AutoTrader EA.mq5  |
//|                        Copyright 2025, Arise <PERSON> |
//|                                             https://www.ampd.com |
//+------------------------------------------------------------------+
#property copyright "Arise Moroka Prince Dynasty"
#property link      "https://ampd.com"
#property description "UNIVERSAL High-Frequency Auto Trading EA - Compatible with ALL MT5 Instruments (Forex, Indices, Crypto, Commodities)"
#property version   "2.0"

#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\AccountInfo.mqh>

//--- Trading objects
CTrade trade;
CPositionInfo position;
CAccountInfo account;

//--- Input parameters for high-frequency trading
input group "=== UNIVERSAL TRADING SETTINGS ==="
input bool   InpAutoTradingEnabled = true;        // Enable Automatic Trading
input double InpLotSize = 0.0;                    // Lot Size (0.0 = Auto-detect minimum)
input int    InpMaxSpread = 50;                   // Maximum Spread (points) - Universal default
input int    InpSlippage = 10;                    // Maximum Slippage (points) - Universal default
input bool   InpSignalBasedExitOnly = true;       // Exit ONLY on Signal (No SL/TP)
input int    InpMaxBarsInTrade = 100;             // Emergency Exit after N bars

input group "=== MICRO ACCOUNT RISK MANAGEMENT ($10 Account) ==="
input bool   InpMicroAccountMode = true;          // Enable Micro Account Protection
input double InpAccountBalance = 10.0;            // Account Balance ($10 default)
input double InpMaxRiskPerTrade = 1.0;            // Max Risk Per Trade (% of balance)
input double InpMaxDailyRisk = 5.0;               // Max Daily Risk (% of balance)
input int    InpMaxTradesPerDay = 5;              // Max Trades Per Day (micro account limit)
input bool   InpUseFixedMicroLot = true;          // Use Fixed Micro Lot Size
input double InpFixedMicroLot = 0.01;             // Fixed Micro Lot (0.01 = $0.10 per pip typically)

input group "=== EXECUTION SETTINGS ==="
input int    InpMagicNumber = 123456;             // Magic Number
input int    InpMaxRetries = 5;                   // Maximum Execution Retries
input int    InpExecutionTimeout = 100;           // Execution Timeout (ms)
input bool   InpCloseOppositePositions = true;    // Close Opposite Positions
input int    InpMaxPositions = 1;                 // Maximum Concurrent Positions

input group "=== RISK MANAGEMENT ==="
input double InpMaxRiskPercent = 2.0;             // Maximum Risk per Trade (%)
input double InpMaxDailyLoss = 100.0;             // Maximum Daily Loss ($)
input double InpMaxDailyProfit = 500.0;           // Maximum Daily Profit ($)
input bool   InpUseTrailingStop = false;          // Use Trailing Stop

input group "=== AUTO-TRADING INTELLIGENCE ==="
input bool   InpAdaptiveLotSizing = true;         // Enable Adaptive Lot Sizing
input bool   InpAutoRiskAdjustment = true;        // Auto-Adjust Risk Based on Performance
input bool   InpSmartSessionManagement = true;    // Smart Trading Session Management
input double InpPerformanceThreshold = 0.6;      // Minimum Win Rate Threshold (0.0-1.0)
input int    InpPerformanceWindow = 20;           // Performance Evaluation Window (trades)
input int    InpTrailingDistance = 15;            // Trailing Stop Distance (points)

input group "=== SIGNAL INTEGRATION ==="
input string InpIndicatorName = "AMPD_RealTime_Precision_Indicator"; // Indicator Name
input int    InpSignalValidationBars = 0;         // Signal Validation Bars (0=immediate)
input bool   InpRequireSignalConfirmation = false; // Require Signal Confirmation
input double InpMinSignalStrength = 0.5;          // Minimum Signal Strength (0.1-1.0)

input group "=== ALERT SYSTEM ==="
input bool   InpEnableAlerts = true;              // Enable Trading Alerts
input bool   InpSoundAlerts = true;               // Sound Alerts
input bool   InpEmailAlerts = false;              // Email Alerts
input bool   InpPushAlerts = false;               // Push Notifications

//--- Global variables
datetime LastTradeTime = 0;
datetime LastSignalTime = 0;
double DailyProfit = 0;
double DailyLoss = 0;
int TotalTrades = 0;
int SuccessfulTrades = 0;
int FailedTrades = 0;
bool TradingEnabled = true;

//--- Universal trading variables
double CurrentLotSize = 0.0;                     // Will be auto-detected
double BaselineWinRate = 0.0;
double RecentWinRate = 0.0;
int RecentTrades[];
double PerformanceScore = 1.0;
datetime LastPerformanceUpdate = 0;
bool SmartTradingActive = true;

//--- Universal instrument properties (auto-detected)
double UniversalMinLot = 0.01;
double UniversalMaxLot = 100.0;
double UniversalLotStep = 0.01;
double UniversalPointValue = 1.0;
double UniversalTickSize = 0.00001;
string InstrumentType = "UNKNOWN";

//--- Signal enumeration
enum ENUM_SIGNAL_TYPE
{
   SIGNAL_NONE,
   SIGNAL_BUY,
   SIGNAL_SELL,
   SIGNAL_EXIT
};

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   //--- Initialize trading objects
   trade.SetExpertMagicNumber(InpMagicNumber);
   trade.SetDeviationInPoints(InpSlippage);
   trade.SetTypeFilling(ORDER_FILLING_FOK);
   
   //--- UNIVERSAL INSTRUMENT DETECTION AND SETUP
   if(!InitializeUniversalSettings())
   {
      Alert("❌ FAILED: Cannot initialize universal settings for ", _Symbol);
      return INIT_FAILED;
   }

   //--- Check if auto trading is allowed (with detailed guidance)
   if(!TerminalInfoInteger(TERMINAL_TRADE_ALLOWED))
   {
      Print("⚠️ AUTO TRADING DISABLED IN TERMINAL");
      Print("📋 TO ENABLE AUTO TRADING:");
      Print("   1. Go to Tools → Options → Expert Advisors");
      Print("   2. Check 'Allow automated trading'");
      Print("   3. Check 'Allow DLL imports' (if needed)");
      Print("   4. Click OK and restart MT5");
      Print("   5. Click the 'AutoTrading' button in toolbar (should be GREEN)");
      Alert("⚠️ Please enable Auto Trading in MT5 settings first!");
      // Don't fail initialization - allow EA to load for manual setup
      Print("🔄 EA loaded in MONITORING MODE - Enable auto trading to activate");
   }

   if(!MQLInfoInteger(MQL_TRADE_ALLOWED))
   {
      Print("⚠️ AUTO TRADING DISABLED FOR THIS EA");
      Print("📋 SOLUTION: Check EA properties → Allow automated trading");
      Alert("⚠️ Please enable 'Allow automated trading' in EA properties!");
      // Don't fail initialization - allow EA to load
      Print("🔄 EA loaded in MONITORING MODE - Check EA properties");
   }

   //--- Universal lot size validation and setup
   if(InpLotSize < 0)
   {
      Print("❌ ERROR: Lot size cannot be negative!");
      Alert("ERROR: Lot size cannot be negative!");
      return INIT_FAILED;
   }

   // Auto-detect minimum lot size if user set 0.0
   CurrentLotSize = (InpLotSize > 0) ? InpLotSize : UniversalMinLot;
   CurrentLotSize = NormalizeLotSize(CurrentLotSize);

   //--- Check auto-trading status and provide guidance
   bool autoTradingReady = CheckAutoTradingStatus();
   
   if(InpSignalBasedExitOnly)
   {
      Print("SIGNAL-BASED EXIT MODE: Trades will exit ONLY on indicator signals");
      Print("Emergency exit after ", InpMaxBarsInTrade, " bars if no signal");
   }
   
   //--- Initialize daily tracking
   ResetDailyCounters();
   
   //--- Test indicator connection
   int indicatorHandle = iCustom(_Symbol, PERIOD_CURRENT, InpIndicatorName);
   if(indicatorHandle == INVALID_HANDLE)
   {
      Alert("ERROR: Cannot connect to indicator: ", InpIndicatorName);
      Alert("Please ensure the indicator is compiled and available");
      return INIT_FAILED;
   }
   IndicatorRelease(indicatorHandle);

   //--- Print universal initialization info
   Print("================================================================");
   Print("� AMPD UNIVERSAL HIGH-FREQUENCY AUTOTRADER EA v2.0");
   Print("================================================================");
   Print("✅ INSTRUMENT: ", _Symbol, " (", InstrumentType, ")");
   Print("✅ EA STATUS: FULLY LOADED & UNIVERSAL");
   Print("✅ AUTO TRADING: ", autoTradingReady ? "🟢 FULLY ENABLED" : "🟡 MONITORING MODE");
   Print("✅ LOT SIZE: ", CurrentLotSize, " (Auto-detected: Min=", UniversalMinLot, ", Max=", UniversalMaxLot, ")");
   Print("✅ EXIT MODE: ", InpSignalBasedExitOnly ? "SIGNAL-BASED ONLY" : "TRADITIONAL SL/TP");
   Print("✅ MAX RISK: ", InpMaxRiskPercent, "%");
   Print("✅ POINT VALUE: ", UniversalPointValue);
   Print("✅ TICK SIZE: ", UniversalTickSize);
   Print("✅ INDICATOR CONNECTION: VERIFIED");
   Print("✅ COMPATIBILITY: ALL MT5 INSTRUMENTS (Forex, Indices, Crypto, Commodities)");
   Print("================================================================");
   if(autoTradingReady)
   {
      Print("🚀 SYSTEM READY FOR AUTO TRADING - Look for 😊 smiley face");
   }
   else
   {
      Print("🔄 SYSTEM LOADED IN MONITORING MODE - Enable auto trading for full functionality");
   }
   Print("================================================================");

   //--- Set EA as successfully initialized (this enables the smiley face)
   TradingEnabled = true;

   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Initialize Universal Settings for ANY MT5 Instrument            |
//+------------------------------------------------------------------+
bool InitializeUniversalSettings()
{
   //--- Get universal instrument properties
   UniversalMinLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
   UniversalMaxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
   UniversalLotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
   UniversalPointValue = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
   UniversalTickSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);

   //--- Validate critical values
   if(UniversalMinLot <= 0 || UniversalMaxLot <= 0 || UniversalLotStep <= 0)
   {
      Print("❌ ERROR: Invalid lot size parameters for ", _Symbol);
      Print("   Min Lot: ", UniversalMinLot);
      Print("   Max Lot: ", UniversalMaxLot);
      Print("   Lot Step: ", UniversalLotStep);
      return false;
   }

   if(UniversalPointValue <= 0 || UniversalTickSize <= 0)
   {
      Print("❌ ERROR: Invalid point/tick parameters for ", _Symbol);
      Print("   Point Value: ", UniversalPointValue);
      Print("   Tick Size: ", UniversalTickSize);
      return false;
   }

   //--- Auto-detect instrument type for better handling
   string symbolName = _Symbol;
   if(StringFind(symbolName, "USD") >= 0 || StringFind(symbolName, "EUR") >= 0 ||
      StringFind(symbolName, "GBP") >= 0 || StringFind(symbolName, "JPY") >= 0 ||
      StringFind(symbolName, "CHF") >= 0 || StringFind(symbolName, "CAD") >= 0 ||
      StringFind(symbolName, "AUD") >= 0 || StringFind(symbolName, "NZD") >= 0)
   {
      InstrumentType = "FOREX";
   }
   else if(StringFind(symbolName, "BTC") >= 0 || StringFind(symbolName, "ETH") >= 0 ||
           StringFind(symbolName, "LTC") >= 0 || StringFind(symbolName, "XRP") >= 0)
   {
      InstrumentType = "CRYPTO";
   }
   else if(StringFind(symbolName, "XAU") >= 0 || StringFind(symbolName, "XAG") >= 0 ||
           StringFind(symbolName, "OIL") >= 0 || StringFind(symbolName, "GOLD") >= 0)
   {
      InstrumentType = "COMMODITY";
   }
   else if(StringFind(symbolName, "US30") >= 0 || StringFind(symbolName, "NAS100") >= 0 ||
           StringFind(symbolName, "SPX500") >= 0 || StringFind(symbolName, "DAX") >= 0)
   {
      InstrumentType = "INDEX";
   }
   else if(StringFind(symbolName, "Jump") >= 0 || StringFind(symbolName, "Crash") >= 0 ||
           StringFind(symbolName, "Step") >= 0 || StringFind(symbolName, "Boom") >= 0)
   {
      InstrumentType = "SYNTHETIC";
   }
   else
   {
      InstrumentType = "OTHER";
   }

   Print("🌍 UNIVERSAL SETUP: ", _Symbol, " detected as ", InstrumentType);
   Print("   📊 Min Lot: ", UniversalMinLot, " | Max Lot: ", UniversalMaxLot, " | Step: ", UniversalLotStep);
   Print("   📏 Point: ", UniversalPointValue, " | Tick Size: ", UniversalTickSize);

   return true;
}

//+------------------------------------------------------------------+
//| Normalize lot size according to instrument specifications       |
//+------------------------------------------------------------------+
double NormalizeLotSize(double lotSize)
{
   //--- Ensure lot size is within broker limits
   lotSize = MathMax(UniversalMinLot, MathMin(UniversalMaxLot, lotSize));

   //--- Round to nearest lot step
   lotSize = MathRound(lotSize / UniversalLotStep) * UniversalLotStep;

   //--- Final validation
   if(lotSize < UniversalMinLot) lotSize = UniversalMinLot;
   if(lotSize > UniversalMaxLot) lotSize = UniversalMaxLot;

   return lotSize;
}

//+------------------------------------------------------------------+
//| Check Auto Trading Status and Provide Guidance                  |
//+------------------------------------------------------------------+
bool CheckAutoTradingStatus()
{
   bool terminalAllowed = TerminalInfoInteger(TERMINAL_TRADE_ALLOWED);
   bool eaAllowed = MQLInfoInteger(MQL_TRADE_ALLOWED);
   bool autoTradingEnabled = TerminalInfoInteger(TERMINAL_TRADE_ALLOWED);

   Print("🔍 AUTO TRADING STATUS CHECK:");
   Print("   📊 Terminal Auto Trading: ", terminalAllowed ? "✅ ENABLED" : "❌ DISABLED");
   Print("   📊 EA Auto Trading: ", eaAllowed ? "✅ ENABLED" : "❌ DISABLED");
   Print("   📊 AutoTrading Button: ", autoTradingEnabled ? "✅ GREEN" : "❌ RED");

   if(!terminalAllowed)
   {
      Print("🔧 TO ENABLE TERMINAL AUTO TRADING:");
      Print("   1. Tools → Options → Expert Advisors");
      Print("   2. ✅ Check 'Allow automated trading'");
      Print("   3. ✅ Check 'Allow DLL imports'");
      Print("   4. Click OK and restart MT5");
   }

   if(!eaAllowed)
   {
      Print("🔧 TO ENABLE EA AUTO TRADING:");
      Print("   1. Right-click on EA in Navigator");
      Print("   2. Properties → Common tab");
      Print("   3. ✅ Check 'Allow automated trading'");
      Print("   4. Click OK");
   }

   if(!autoTradingEnabled)
   {
      Print("🔧 TO ENABLE AUTOTRADING BUTTON:");
      Print("   1. Click the 'AutoTrading' button in toolbar");
      Print("   2. Button should turn GREEN");
      Print("   3. You should see a smiley face 😊 in top-right corner");
   }

   bool allReady = terminalAllowed && eaAllowed && autoTradingEnabled;

   if(allReady)
   {
      Print("🚀 AUTO TRADING: FULLY ENABLED AND READY");
   }
   else
   {
      Print("⚠️ AUTO TRADING: PARTIALLY DISABLED - EA will run in MONITORING MODE");
      Print("💡 Follow the instructions above to enable full auto trading");
   }

   return allReady;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   //--- Print final statistics
   double successRate = TotalTrades > 0 ? (SuccessfulTrades * 100.0 / TotalTrades) : 0;
   Print("AMPD AutoTrader EA Statistics:");
   Print("Total Trades: ", TotalTrades);
   Print("Successful: ", SuccessfulTrades, " (", DoubleToString(successRate, 1), "%)");
   Print("Failed: ", FailedTrades);
   Print("Daily P&L: $", DoubleToString(DailyProfit - DailyLoss, 2));
}

//+------------------------------------------------------------------+
//| Expert tick function - High-frequency execution                 |
//+------------------------------------------------------------------+
void OnTick()
{
   //--- Check if trading is enabled
   if(!InpAutoTradingEnabled || !TradingEnabled) return;
   
   //--- Check daily limits
   if(!CheckDailyLimits()) return;
   
   //--- Check spread
   if(!CheckSpread()) return;
   
   //--- Get signal from indicator
   ENUM_SIGNAL_TYPE signal = GetIndicatorSignal();
   
   //--- Process signal with high-frequency execution
   if(signal != SIGNAL_NONE)
   {
      ProcessHighFrequencySignal(signal);
   }
   
   //--- Manage existing positions
   ManagePositions();
   
   //--- Update trailing stops if enabled
   if(InpUseTrailingStop)
   {
      UpdateTrailingStops();
   }
}

//+------------------------------------------------------------------+
//| Get SIMPLIFIED and RELIABLE signal from the companion indicator |
//+------------------------------------------------------------------+
ENUM_SIGNAL_TYPE GetIndicatorSignal()
{
   //--- Check market conditions first
   if(!IsMarketConditionsSuitable()) return SIGNAL_NONE;

   //--- SIMPLIFIED signal detection - check individual buffers independently
   double goldBuyBuffer[], limeSellBuffer[], exitDotBuffer[];

   //--- Copy required indicator buffers with error checking
   int indicatorHandle = iCustom(_Symbol, PERIOD_CURRENT, InpIndicatorName);
   if(indicatorHandle == INVALID_HANDLE)
   {
      Print("❌ ERROR: Cannot access indicator: ", InpIndicatorName);
      return SIGNAL_NONE;
   }

   // Copy only the buffers we actually need
   if(CopyBuffer(indicatorHandle, 0, 0, 2, goldBuyBuffer) != 2)
   {
      Print("❌ ERROR: Cannot copy GOLD arrows buffer");
      return SIGNAL_NONE;
   }
   if(CopyBuffer(indicatorHandle, 1, 0, 2, limeSellBuffer) != 2)
   {
      Print("❌ ERROR: Cannot copy LIME arrows buffer");
      return SIGNAL_NONE;
   }
   if(CopyBuffer(indicatorHandle, 3, 0, 2, exitDotBuffer) != 2)
   {
      Print("❌ ERROR: Cannot copy Exit dots buffer");
      return SIGNAL_NONE;
   }

   //--- SIMPLIFIED: Check for signals independently
   bool goldArrowPresent = (goldBuyBuffer[0] != EMPTY_VALUE && goldBuyBuffer[0] != 0.0);
   bool limeArrowPresent = (limeSellBuffer[0] != EMPTY_VALUE && limeSellBuffer[0] != 0.0);
   bool exitDotPresent = (exitDotBuffer[0] != EMPTY_VALUE && exitDotBuffer[0] != 0.0);

   //--- Debug signal detection every 30 seconds
   static datetime lastDebugTime = 0;
   if(TimeCurrent() - lastDebugTime >= 30)
   {
      Print("🔍 SIGNAL CHECK - GOLD:", goldArrowPresent, "(", goldBuyBuffer[0], ") LIME:", limeArrowPresent, "(", limeSellBuffer[0], ") EXIT:", exitDotPresent, "(", exitDotBuffer[0], ")");
      lastDebugTime = TimeCurrent();
   }

   //--- PRIORITY 1: Check for EXIT signal first (if we have open positions)
   if(exitDotPresent && PositionsTotal() > 0)
   {
      Print("� EXIT SIGNAL CONFIRMED: Exit dot detected with open positions");
      Print("   → Exit Dot Value: ", exitDotBuffer[0]);
      return SIGNAL_EXIT;
   }

   //--- PRIORITY 2: Check for BUY signal (GOLD arrow only)
   if(goldArrowPresent && !exitDotPresent)
   {
      Print("� BUY SIGNAL CONFIRMED: GOLD arrow detected");
      Print("   → GOLD Buffer Value: ", goldBuyBuffer[0]);
      return SIGNAL_BUY;
   }

   //--- PRIORITY 3: Check for SELL signal (LIME arrow only)
   if(limeArrowPresent && !exitDotPresent)
   {
      Print("� SELL SIGNAL CONFIRMED: LIME arrow detected");
      Print("   → LIME Buffer Value: ", limeSellBuffer[0]);
      return SIGNAL_SELL;
   }

   return SIGNAL_NONE;
}

//+------------------------------------------------------------------+
//| Check if market conditions are suitable for trading             |
//+------------------------------------------------------------------+
bool IsMarketConditionsSuitable()
{
   //--- Check spread conditions
   MqlTick tick;
   if(!SymbolInfoTick(_Symbol, tick)) return false;

   double spread = (tick.ask - tick.bid) / UniversalPointValue;
   if(spread > InpMaxSpread)
   {
      Print("❌ SPREAD TOO HIGH: ", DoubleToString(spread, 1), " points (Max: ", InpMaxSpread, ") for ", InstrumentType);
      return false;
   }

   //--- Check if market is open
   if(!SymbolInfoInteger(_Symbol, SYMBOL_TRADE_MODE))
   {
      Print("❌ MARKET CLOSED for ", _Symbol);
      return false;
   }

   //--- Check current lot size against universal limits
   if(CurrentLotSize < UniversalMinLot)
   {
      Print("❌ CURRENT LOT SIZE TOO SMALL: ", CurrentLotSize, " (Min: ", UniversalMinLot, ")");
      return false;
   }

   if(CurrentLotSize > UniversalMaxLot)
   {
      Print("❌ CURRENT LOT SIZE TOO LARGE: ", CurrentLotSize, " (Max: ", UniversalMaxLot, ")");
      return false;
   }

   //--- All conditions passed
   return true;
}

//+------------------------------------------------------------------+
//| Calculate adaptive lot size based on performance and risk       |
//+------------------------------------------------------------------+
double CalculateAdaptiveLotSize()
{
   if(!InpAdaptiveLotSizing) return InpLotSize;

   double adaptiveLot = InpLotSize;

   //--- Adjust based on recent performance
   if(InpAutoRiskAdjustment && TotalTrades >= 5)
   {
      double currentWinRate = (double)SuccessfulTrades / TotalTrades;

      if(currentWinRate >= 0.8) // Excellent performance
         adaptiveLot = InpLotSize * 1.5;
      else if(currentWinRate >= 0.6) // Good performance
         adaptiveLot = InpLotSize * 1.2;
      else if(currentWinRate < 0.4) // Poor performance
         adaptiveLot = InpLotSize * 0.5;
      else if(currentWinRate < 0.2) // Very poor performance
         adaptiveLot = InpLotSize * 0.3;
   }

   //--- Apply account balance scaling
   double accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
   double riskAmount = accountBalance * (InpMaxRiskPercent / 100.0);

   //--- Calculate position size based on risk
   MqlTick tick;
   if(SymbolInfoTick(_Symbol, tick))
   {
      double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
      double tickSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
      double stopLossPoints = 50; // Estimated SL in points

      if(tickValue > 0 && tickSize > 0)
      {
         double riskBasedLot = riskAmount / (stopLossPoints * tickValue / tickSize);
         adaptiveLot = MathMin(adaptiveLot, riskBasedLot);
      }
   }

   //--- Use universal lot size normalization
   adaptiveLot = NormalizeLotSize(adaptiveLot);

   CurrentLotSize = adaptiveLot;
   return adaptiveLot;
}

//+------------------------------------------------------------------+
//| Update performance tracking and adjust trading behavior         |
//+------------------------------------------------------------------+
void UpdatePerformanceTracking(bool tradeSuccess)
{
   //--- Update basic counters
   TotalTrades++;
   if(tradeSuccess) SuccessfulTrades++;
   else FailedTrades++;

   //--- Update recent performance window
   if(ArraySize(RecentTrades) < InpPerformanceWindow)
      ArrayResize(RecentTrades, ArraySize(RecentTrades) + 1);

   // Shift array and add new result
   for(int i = ArraySize(RecentTrades) - 1; i > 0; i--)
      RecentTrades[i] = RecentTrades[i-1];

   RecentTrades[0] = tradeSuccess ? 1 : 0;

   //--- Calculate recent win rate
   int recentWins = 0;
   int recentTotal = ArraySize(RecentTrades);
   for(int i = 0; i < recentTotal; i++)
      if(RecentTrades[i] == 1) recentWins++;

   RecentWinRate = (double)recentWins / recentTotal;

   //--- Update performance score
   if(TotalTrades >= 10)
   {
      double overallWinRate = (double)SuccessfulTrades / TotalTrades;
      PerformanceScore = (overallWinRate * 0.7) + (RecentWinRate * 0.3);

      //--- Adjust trading behavior based on performance
      if(InpSmartSessionManagement)
      {
         if(PerformanceScore < InpPerformanceThreshold)
         {
            SmartTradingActive = false;
            Print("🔴 SMART TRADING PAUSED - Performance below threshold: ",
                  DoubleToString(PerformanceScore, 3));
         }
         else if(!SmartTradingActive && PerformanceScore > (InpPerformanceThreshold + 0.1))
         {
            SmartTradingActive = true;
            Print("🟢 SMART TRADING RESUMED - Performance improved: ",
                  DoubleToString(PerformanceScore, 3));
         }
      }
   }

   LastPerformanceUpdate = TimeCurrent();

   //--- Print performance update
   if(TotalTrades % 5 == 0) // Every 5 trades
   {
      Print("📊 PERFORMANCE UPDATE:");
      Print("   Total Trades: ", TotalTrades);
      Print("   Overall Win Rate: ", DoubleToString((double)SuccessfulTrades/TotalTrades*100, 1), "%");
      Print("   Recent Win Rate: ", DoubleToString(RecentWinRate*100, 1), "%");
      Print("   Performance Score: ", DoubleToString(PerformanceScore, 3));
      Print("   Current Lot Size: ", DoubleToString(CurrentLotSize, 2));
   }
}

//+------------------------------------------------------------------+
//| Process high-frequency signal with immediate execution          |
//+------------------------------------------------------------------+
void ProcessHighFrequencySignal(ENUM_SIGNAL_TYPE signal)
{
   datetime currentTime = TimeCurrent();
   
   //--- Prevent duplicate signals
   if(currentTime == LastSignalTime) return;
   LastSignalTime = currentTime;
   
   //--- Check position limits
   if(PositionsTotal() >= InpMaxPositions && signal != SIGNAL_EXIT) return;
   
   //--- Execute signal with high-frequency logic
   switch(signal)
   {
      case SIGNAL_BUY:
         ExecuteBuyOrder();
         break;
         
      case SIGNAL_SELL:
         ExecuteSellOrder();
         break;
         
      case SIGNAL_EXIT:
         CloseAllPositions();
         break;
   }
}

//+------------------------------------------------------------------+
//| Execute BUY order with retry mechanism                          |
//+------------------------------------------------------------------+
void ExecuteBuyOrder()
{
   //--- Close opposite positions if enabled
   if(InpCloseOppositePositions)
   {
      ClosePositionsByType(POSITION_TYPE_SELL);
   }
   
   //--- Calculate adaptive lot size based on performance and risk
   double lotSize = CalculateAdaptiveLotSize();
   if(lotSize <= 0) return;

   //--- Check if smart trading is active
   if(InpSmartSessionManagement && !SmartTradingActive)
   {
      Print("⏸️ TRADE SKIPPED - Smart trading paused due to poor performance");
      return;
   }
   
   //--- Get current prices
   MqlTick tick;
   if(!SymbolInfoTick(_Symbol, tick)) return;
   
   double price = tick.ask;
   double sl = 0;  // No stop loss - signal-based exit only
   double tp = 0;  // No take profit - signal-based exit only
   
   //--- Execute with retry mechanism
   ulong startTime = GetMicrosecondCount();
   
   for(int retry = 0; retry < InpMaxRetries; retry++)
   {
      if(trade.Buy(lotSize, _Symbol, price, sl, tp, "AMPD-SIGNAL-BUY"))
      {
         SuccessfulTrades++;
         TotalTrades++;
         LastTradeTime = TimeCurrent();
         
         //--- Calculate execution time
         ulong endTime = GetMicrosecondCount();
         double executionTime = (double)(endTime - startTime) / 1000.0;
         
         //--- Send alerts
         string alertMsg = StringFormat("🟡 BUY EXECUTED: %s @ %s | SIGNAL-BASED EXIT | Time: %.1fms",
                          _Symbol, DoubleToString(price, _Digits), executionTime);
         
         SendTradingAlert(alertMsg);
         Print(alertMsg);
         
         if(executionTime > InpExecutionTimeout)
         {
            Print("WARNING: Execution time exceeded target: ", executionTime, "ms");
         }
         
         return;
      }
      else
      {
         Print("BUY execution failed, retry ", retry + 1, "/", InpMaxRetries, " - Error: ", trade.ResultRetcode());
         Sleep(10); // Brief pause before retry
      }
   }
   
   //--- All retries failed
   FailedTrades++;
   TotalTrades++;
   Print("ERROR: BUY order failed after ", InpMaxRetries, " retries");
}

//+------------------------------------------------------------------+
//| Execute SELL order with retry mechanism                         |
//+------------------------------------------------------------------+
void ExecuteSellOrder()
{
   //--- Close opposite positions if enabled
   if(InpCloseOppositePositions)
   {
      ClosePositionsByType(POSITION_TYPE_BUY);
   }

   //--- Calculate adaptive lot size based on performance and risk
   double lotSize = CalculateAdaptiveLotSize();
   if(lotSize <= 0) return;

   //--- Check if smart trading is active
   if(InpSmartSessionManagement && !SmartTradingActive)
   {
      Print("⏸️ TRADE SKIPPED - Smart trading paused due to poor performance");
      return;
   }

   //--- Get current prices
   MqlTick tick;
   if(!SymbolInfoTick(_Symbol, tick)) return;

   double price = tick.bid;
   double sl = 0;  // No stop loss - signal-based exit only
   double tp = 0;  // No take profit - signal-based exit only

   //--- Execute with retry mechanism
   ulong startTime = GetMicrosecondCount();

   for(int retry = 0; retry < InpMaxRetries; retry++)
   {
      if(trade.Sell(lotSize, _Symbol, price, sl, tp, "AMPD-SIGNAL-SELL"))
      {
         SuccessfulTrades++;
         TotalTrades++;
         LastTradeTime = TimeCurrent();

         //--- Calculate execution time
         ulong endTime = GetMicrosecondCount();
         double executionTime = (double)(endTime - startTime) / 1000.0;

         //--- Send alerts
         string alertMsg = StringFormat("🟢 SELL EXECUTED: %s @ %s | SIGNAL-BASED EXIT | Time: %.1fms",
                          _Symbol, DoubleToString(price, _Digits), executionTime);

         SendTradingAlert(alertMsg);
         Print(alertMsg);

         if(executionTime > InpExecutionTimeout)
         {
            Print("WARNING: Execution time exceeded target: ", executionTime, "ms");
         }

         return;
      }
      else
      {
         Print("SELL execution failed, retry ", retry + 1, "/", InpMaxRetries, " - Error: ", trade.ResultRetcode());
         Sleep(10); // Brief pause before retry
      }
   }

   //--- All retries failed
   FailedTrades++;
   TotalTrades++;
   Print("ERROR: SELL order failed after ", InpMaxRetries, " retries");
}

//+------------------------------------------------------------------+
//| Close all positions RELIABLY (EXIT signal)                      |
//+------------------------------------------------------------------+
void CloseAllPositions()
{
   int closedCount = 0;
   int failedCount = 0;

   Print("🔴 CLOSING ALL POSITIONS - Total positions: ", PositionsTotal());

   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(position.SelectByIndex(i))
      {
         if(position.Symbol() == _Symbol && position.Magic() == InpMagicNumber)
         {
            ulong ticket = position.Ticket();
            double profit = position.Profit();
            ENUM_POSITION_TYPE posType = position.PositionType();

            // Try to close with retry mechanism
            bool closed = false;
            for(int retry = 0; retry < 3; retry++)
            {
               if(trade.PositionClose(ticket))
               {
                  closed = true;
                  closedCount++;
                  Print("✅ POSITION CLOSED: Ticket ", ticket, " | Type: ", EnumToString(posType), " | Profit: $", profit);
                  UpdatePerformanceTracking(profit > 0);
                  break;
               }
               else
               {
                  Print("⚠️ Close attempt ", retry + 1, " failed for ticket ", ticket, " - Error: ", trade.ResultRetcode());
                  Sleep(100); // Brief pause before retry
               }
            }

            if(!closed)
            {
               failedCount++;
               Print("❌ FAILED to close position: Ticket ", ticket, " after 3 attempts");
            }
         }
      }
   }

   Print("🔴 CLOSE SUMMARY: ", closedCount, " positions closed, ", failedCount, " failed");

   // Send alert about position closure
   if(closedCount > 0)
   {
      string alertMsg = StringFormat("EXIT SIGNAL: %d positions closed", closedCount);
      SendTradingAlert(alertMsg);
   }
}

//+------------------------------------------------------------------+
//| Close positions by type                                         |
//+------------------------------------------------------------------+
void ClosePositionsByType(ENUM_POSITION_TYPE posType)
{
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(position.SelectByIndex(i))
      {
         if(position.Symbol() == _Symbol && position.Magic() == InpMagicNumber && position.PositionType() == posType)
         {
            if(trade.PositionClose(position.Ticket()))
            {
               Print("Position closed due to opposite signal: Ticket ", position.Ticket());
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Calculate optimal lot size based on risk management             |
//+------------------------------------------------------------------+
double CalculateOptimalLotSize()
{
   double lotSize = InpLotSize;

   //--- Risk-based lot size calculation
   if(InpMaxRiskPercent > 0)
   {
      double accountBalance = account.Balance();
      double riskAmount = accountBalance * InpMaxRiskPercent / 100.0;
      double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
      double stopLossPoints = 50 * UniversalPointValue; // Fixed 50 points for risk calculation

      if(tickValue > 0 && stopLossPoints > 0)
      {
         double calculatedLotSize = riskAmount / (stopLossPoints * tickValue / UniversalPointValue);
         lotSize = MathMin(lotSize, calculatedLotSize);
      }
   }

   //--- Use universal lot size normalization
   lotSize = NormalizeLotSize(lotSize);

   return lotSize;
}

//+------------------------------------------------------------------+
//| Check daily limits                                               |
//+------------------------------------------------------------------+
bool CheckDailyLimits()
{
   //--- Reset counters at start of new day
   static int lastDay = -1;
   MqlDateTime dt;
   TimeToStruct(TimeCurrent(), dt);

   if(dt.day != lastDay)
   {
      ResetDailyCounters();
      lastDay = dt.day;
      TradingEnabled = true;
   }

   //--- Check daily loss limit
   if(InpMaxDailyLoss > 0 && DailyLoss >= InpMaxDailyLoss)
   {
      if(TradingEnabled)
      {
         TradingEnabled = false;
         string msg = "Daily loss limit reached: $" + DoubleToString(DailyLoss, 2);
         SendTradingAlert("🚫 " + msg);
         Print("WARNING: ", msg);
      }
      return false;
   }

   //--- Check daily profit limit
   if(InpMaxDailyProfit > 0 && DailyProfit >= InpMaxDailyProfit)
   {
      if(TradingEnabled)
      {
         TradingEnabled = false;
         string msg = "Daily profit target reached: $" + DoubleToString(DailyProfit, 2);
         SendTradingAlert("🎯 " + msg);
         Print("SUCCESS: ", msg);
      }
      return false;
   }

   return true;
}

//+------------------------------------------------------------------+
//| Reset daily counters                                             |
//+------------------------------------------------------------------+
void ResetDailyCounters()
{
   DailyProfit = 0;
   DailyLoss = 0;

   //--- Calculate today's P&L from closed positions
   if(HistorySelect(iTime(_Symbol, PERIOD_D1, 0), TimeCurrent()))
   {
      for(int i = 0; i < HistoryDealsTotal(); i++)
      {
         ulong ticket = HistoryDealGetTicket(i);
         if(HistoryDealGetString(ticket, DEAL_SYMBOL) == _Symbol &&
            HistoryDealGetInteger(ticket, DEAL_MAGIC) == InpMagicNumber)
         {
            double profit = HistoryDealGetDouble(ticket, DEAL_PROFIT);
            if(profit > 0) DailyProfit += profit;
            else DailyLoss += MathAbs(profit);
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Check spread conditions                                          |
//+------------------------------------------------------------------+
bool CheckSpread()
{
   MqlTick tick;
   if(!SymbolInfoTick(_Symbol, tick)) return false;

   double spread = (tick.ask - tick.bid) / UniversalPointValue;

   if(spread > InpMaxSpread)
   {
      static datetime lastSpreadWarning = 0;
      if(TimeCurrent() - lastSpreadWarning > 60) // Warn once per minute
      {
         Print("WARNING: Spread too high: ", DoubleToString(spread, 1), " points (Max: ", InpMaxSpread, ")");
         lastSpreadWarning = TimeCurrent();
      }
      return false;
   }

   return true;
}

//+------------------------------------------------------------------+
//| ENHANCED Position Management with RELIABLE exit logic           |
//+------------------------------------------------------------------+
void ManagePositions()
{
   //--- PRIORITY 1: Check for exit signals FIRST and ALWAYS
   ENUM_SIGNAL_TYPE currentSignal = GetIndicatorSignal();
   if(currentSignal == SIGNAL_EXIT)
   {
      Print("� EXIT SIGNAL DETECTED - Closing ALL positions immediately");
      CloseAllPositions();
      return;
   }

   //--- PRIORITY 2: Manage individual positions with multiple exit conditions
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(position.SelectByIndex(i))
      {
         if(position.Symbol() == _Symbol && position.Magic() == InpMagicNumber)
         {
            //--- Get position details
            datetime positionTime = (datetime)position.Time();
            int barsInTrade = Bars(_Symbol, PERIOD_CURRENT, positionTime, TimeCurrent());
            double currentProfit = position.Profit();
            ENUM_POSITION_TYPE posType = position.PositionType();

            //--- Update daily P&L tracking
            if(currentProfit > 0)
            {
               DailyProfit = MathMax(DailyProfit, currentProfit);
            }
            else
            {
               DailyLoss = MathMax(DailyLoss, MathAbs(currentProfit));
            }

            //--- EXIT CONDITION 1: Emergency exit after maximum bars (CRITICAL SAFETY)
            if(barsInTrade >= InpMaxBarsInTrade)
            {
               Print("⚠️ EMERGENCY EXIT: Position held for ", barsInTrade, " bars - Force closing ticket ", position.Ticket());
               if(trade.PositionClose(position.Ticket()))
               {
                  Print("✅ Emergency exit successful - Profit: $", currentProfit);
                  UpdatePerformanceTracking(currentProfit > 0);
               }
               else
               {
                  Print("❌ Failed to close position: ", trade.ResultRetcode());
               }
               continue;
            }

            //--- EXIT CONDITION 2: Opposite signal detection (close opposite positions)
            if(InpCloseOppositePositions)
            {
               if((posType == POSITION_TYPE_BUY && currentSignal == SIGNAL_SELL) ||
                  (posType == POSITION_TYPE_SELL && currentSignal == SIGNAL_BUY))
               {
                  Print("🔄 OPPOSITE SIGNAL: Closing ", EnumToString(posType), " position due to ", EnumToString(currentSignal), " signal");
                  if(trade.PositionClose(position.Ticket()))
                  {
                     Print("✅ Opposite signal exit successful - Profit: $", currentProfit);
                     UpdatePerformanceTracking(currentProfit > 0);
                  }
                  continue;
               }
            }

            //--- EXIT CONDITION 3: Time-based exit for stuck positions (additional safety)
            int minutesInTrade = (int)((TimeCurrent() - positionTime) / 60);
            if(minutesInTrade >= 60) // 1 hour maximum
            {
               Print("⏰ TIME EXIT: Position held for ", minutesInTrade, " minutes - Closing ticket ", position.Ticket());
               if(trade.PositionClose(position.Ticket()))
               {
                  Print("✅ Time-based exit successful - Profit: $", currentProfit);
                  UpdatePerformanceTracking(currentProfit > 0);
               }
               continue;
            }

            //--- Position status logging (every 10 bars)
            if(barsInTrade % 10 == 0)
            {
               Print("📊 Position Status: Ticket ", position.Ticket(), " | Type: ", EnumToString(posType),
                     " | Bars: ", barsInTrade, " | Profit: $", currentProfit);
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Update trailing stops                                            |
//+------------------------------------------------------------------+
void UpdateTrailingStops()
{
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(position.SelectByIndex(i))
      {
         if(position.Symbol() == _Symbol && position.Magic() == InpMagicNumber)
         {
            double currentPrice = position.PositionType() == POSITION_TYPE_BUY ?
                                 SymbolInfoDouble(_Symbol, SYMBOL_BID) :
                                 SymbolInfoDouble(_Symbol, SYMBOL_ASK);

            double currentSL = position.StopLoss();
            double newSL = 0;

            if(position.PositionType() == POSITION_TYPE_BUY)
            {
               newSL = currentPrice - (InpTrailingDistance * UniversalPointValue);
               if(newSL > currentSL + UniversalPointValue)
               {
                  trade.PositionModify(position.Ticket(), newSL, position.TakeProfit());
               }
            }
            else // SELL position
            {
               newSL = currentPrice + (InpTrailingDistance * UniversalPointValue);
               if(newSL < currentSL - UniversalPointValue)
               {
                  trade.PositionModify(position.Ticket(), newSL, position.TakeProfit());
               }
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Send trading alerts                                              |
//+------------------------------------------------------------------+
void SendTradingAlert(string message)
{
   if(!InpEnableAlerts) return;

   if(InpSoundAlerts) PlaySound("alert2.wav");
   if(InpEmailAlerts) SendMail("AMPD AutoTrader Alert", message);
   if(InpPushAlerts) SendNotification(message);

   Alert(message);
}

//+------------------------------------------------------------------+
//| Get current trading statistics                                   |
//+------------------------------------------------------------------+
string GetTradingStats()
{
   double successRate = TotalTrades > 0 ? (SuccessfulTrades * 100.0 / TotalTrades) : 0;
   double netPL = DailyProfit - DailyLoss;

   return StringFormat("Trades: %d | Success: %.1f%% | Daily P&L: $%.2f | Positions: %d",
                      TotalTrades, successRate, netPL, PositionsTotal());
}

//+------------------------------------------------------------------+
//| OnTimer function for periodic tasks                             |
//+------------------------------------------------------------------+
void OnTimer()
{
   //--- Print periodic statistics
   static datetime lastStatsTime = 0;
   if(TimeCurrent() - lastStatsTime >= 300) // Every 5 minutes
   {
      Print("AMPD AutoTrader Stats: ", GetTradingStats());
      lastStatsTime = TimeCurrent();
   }
}

//+------------------------------------------------------------------+
//| OnTrade function - Track trade results                          |
//+------------------------------------------------------------------+
void OnTrade()
{
   //--- Update statistics when trades are closed
   if(HistorySelect(TimeCurrent() - 60, TimeCurrent())) // Last minute
   {
      for(int i = HistoryDealsTotal() - 1; i >= 0; i--)
      {
         ulong ticket = HistoryDealGetTicket(i);
         if(HistoryDealGetString(ticket, DEAL_SYMBOL) == _Symbol &&
            HistoryDealGetInteger(ticket, DEAL_MAGIC) == InpMagicNumber &&
            HistoryDealGetInteger(ticket, DEAL_ENTRY) == DEAL_ENTRY_OUT)
         {
            double profit = HistoryDealGetDouble(ticket, DEAL_PROFIT);
            string alertMsg = StringFormat("🔄 TRADE CLOSED: Ticket %d | Profit: $%.2f",
                             (int)ticket, profit);

            if(profit > 0)
            {
               alertMsg = "✅ " + alertMsg + " | PROFIT";
            }
            else
            {
               alertMsg = "❌ " + alertMsg + " | LOSS";
            }

            SendTradingAlert(alertMsg);
            break; // Process only the most recent trade
         }
      }
   }
}
