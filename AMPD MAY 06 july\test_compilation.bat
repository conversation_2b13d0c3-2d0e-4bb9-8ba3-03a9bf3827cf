@echo off
echo Testing AMPD System Compilation...
echo.

echo Checking Indicator file...
if exist "AMPD_RealTime_Precision_Indicator.mq5" (
    echo ✓ Indicator file found
) else (
    echo ✗ Indicator file missing
    exit /b 1
)

echo Checking EA file...
if exist "AMPD_HighFrequency_AutoTrader_EA.mq5" (
    echo ✓ EA file found
) else (
    echo ✗ EA file missing
    exit /b 1
)

echo.
echo All files present. Ready for compilation in MetaEditor.
echo.
echo Instructions:
echo 1. Open MetaEditor
echo 2. Open both .mq5 files
echo 3. Compile each file (F7 or Compile button)
echo 4. Check for any remaining errors
echo.
pause
