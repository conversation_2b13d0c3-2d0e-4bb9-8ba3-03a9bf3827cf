//+------------------------------------------------------------------+
//|                                    AMPD Crash1000 Indicator.mq5 |
//|                        Copyright 2024, <PERSON><PERSON> |
//|                                             https://www.ampd.com |
//+------------------------------------------------------------------+
#property copyright "Arise Moroka Prince Dynasty"
#property link      "https://ampd.com"
#property description "Professional Crash 1000 Index Trading Indicator"
#property version   "1.0"

#property indicator_chart_window
#property indicator_buffers 3
#property indicator_plots   3

//--- Plot definitions using proven working structure
#property indicator_label1  "Crash1000_SELL"
#property indicator_type1   DRAW_ARROW
#property indicator_color1  clrRed
#property indicator_width1  3

#property indicator_label2  "EXIT_Signal"
#property indicator_type2   DRAW_ARROW
#property indicator_color2  clrGold
#property indicator_width2  2

#property indicator_label3  "Trend_Line"
#property indicator_type3   DRAW_LINE
#property indicator_color3  clrDodgerBlue
#property indicator_width3  1

//--- Signal Types
enum ENUM_SIGNAL
{
   SIGNAL_NONE,
   SIGNAL_CRASH_SELL,
   SIGNAL_EXIT
};

//--- Input parameters
input group "=== CRASH 1000 SETTINGS ==="
input double InpVolatilityThreshold = 2.5;         // Volatility Threshold (ATR Multiplier)
input int    InpRSIPeriod = 14;                    // RSI Period
input double InpRSILevel = 65.0;                   // RSI Overbought Level
input double InpCandleSize = 2.0;                  // Minimum Candle Size (ATR Multiplier)
input int    InpATRPeriod = 14;                    // ATR Period

input group "=== ALERT SETTINGS ==="
input bool   InpShowAlerts = true;                 // Show Alerts
input bool   InpSoundAlerts = true;                // Sound Alerts
input bool   InpEmailAlerts = false;               // Email Alerts
input bool   InpPushAlerts = false;                // Push Notifications

input group "=== DISPLAY SETTINGS ==="
input bool   InpShowInfoPanel = true;              // Show Info Panel
input bool   InpShowTrendLine = true;              // Show Trend Reference Line

//--- Global variables
double CrashSellBuffer[];
double ExitBuffer[];
double TrendBuffer[];

int atrHandle;
int rsiHandle;

long chartID = ChartID();
#define LabelBox "CRASH_BOX"
#define Label1 "CRASH_L1"
#define Label2 "CRASH_L2"
#define Label3 "CRASH_L3"
#define Label4 "CRASH_L4"

string label1 = "CRASH 1000: ",
       label2 = "RSI LEVEL: ",
       label3 = "SIGNALS: ",
       label4 = "STATUS: ";

int doubleToPip;
double pipToDouble;
MqlTick tick;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
   IndicatorSetString(INDICATOR_SHORTNAME, "AMPD Crash 1000 Pro");
   
   //--- Set points & digits (from reference structure)
   IndicatorSetInteger(INDICATOR_DIGITS, _Digits);   
   if(_Digits == 2 || _Digits == 3)  
      doubleToPip = 100;
   else                          
      doubleToPip = 10000;
   
   if(_Digits == 2 || _Digits == 4) 
      pipToDouble = _Point;
   else                       
      pipToDouble = _Point * 10;

   //--- Create indicator handles
   atrHandle = iATR(_Symbol, PERIOD_CURRENT, InpATRPeriod);
   rsiHandle = iRSI(_Symbol, PERIOD_CURRENT, InpRSIPeriod, PRICE_CLOSE);
   
   if(atrHandle == INVALID_HANDLE || rsiHandle == INVALID_HANDLE)
   {
      Print("Error creating indicator handles for Crash 1000");
      return INIT_FAILED;
   }

   //--- Create info panel
   if(InpShowInfoPanel)
      CreateInfoPanel();

   //--- Indicator buffers mapping (using proven structure)
   SetIndexBuffer(0, CrashSellBuffer, INDICATOR_DATA);
   ArraySetAsSeries(CrashSellBuffer, true);
   PlotIndexSetString(0, PLOT_LABEL, "Crash1000_SELL");   
   PlotIndexSetInteger(0, PLOT_ARROW, 234);  // Down arrow
   PlotIndexSetDouble(0, PLOT_EMPTY_VALUE, 0);
   
   SetIndexBuffer(1, ExitBuffer, INDICATOR_DATA);
   ArraySetAsSeries(ExitBuffer, true);
   PlotIndexSetString(1, PLOT_LABEL, "EXIT_Signal");   
   PlotIndexSetInteger(1, PLOT_ARROW, 159);  // Circle
   PlotIndexSetDouble(1, PLOT_EMPTY_VALUE, 0);
   
   SetIndexBuffer(2, TrendBuffer, INDICATOR_DATA);
   ArraySetAsSeries(TrendBuffer, true);
   PlotIndexSetString(2, PLOT_LABEL, "Trend_Line");   
   PlotIndexSetDouble(2, PLOT_EMPTY_VALUE, 0);

   Print("AMPD Crash 1000 Indicator initialized successfully");
   Print("RSI Period: ", InpRSIPeriod, " | RSI Level: ", InpRSILevel);
   Print("Volatility Threshold: ", InpVolatilityThreshold, "x ATR");
   
   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
   if(rates_total < 100)  
      return 0;

   //--- Set arrays as series (from reference structure)
   ArraySetAsSeries(time, true);
   ArraySetAsSeries(open, true);
   ArraySetAsSeries(high, true);
   ArraySetAsSeries(low, true);
   ArraySetAsSeries(close, true);

   //--- Update info panel
   if(InpShowInfoPanel)
      UpdateInfoPanel();

   int limit;
   //--- Calculate limit (from reference structure)
   limit = rates_total - prev_calculated;
   if(prev_calculated == 0)
   { 
      limit = (int)ChartGetInteger(chartID, CHART_VISIBLE_BARS) + 100;
      PlotIndexSetInteger(0, PLOT_DRAW_BEGIN, rates_total - limit);
      PlotIndexSetInteger(1, PLOT_DRAW_BEGIN, rates_total - limit);
      PlotIndexSetInteger(2, PLOT_DRAW_BEGIN, rates_total - limit);
   }

   //--- Main calculation loop (from reference structure)
   for(int i = limit; i >= 0 && !IsStopped(); i--)
   {
      // Initialize buffers
      CrashSellBuffer[i] = 0;
      ExitBuffer[i] = 0;
      TrendBuffer[i] = 0;
      
      // Get indicator values
      double atr = GetATR(i);
      double rsi = GetRSI(i);
      
      if(atr <= 0) continue;
      
      // Set trend reference line
      if(InpShowTrendLine)
         TrendBuffer[i] = close[i];
      
      // Analyze Crash 1000 signals
      ENUM_SIGNAL signal = AnalyzeCrashSignals(i, time, open, high, low, close, atr, rsi);
      
      // Plot signals
      if(signal == SIGNAL_CRASH_SELL)
      {
         CrashSellBuffer[i] = high[i] + atr;
         HandleAlerts(time[i], SIGNAL_CRASH_SELL, close[i]);
      }
      else if(signal == SIGNAL_EXIT)
      {
         ExitBuffer[i] = close[i];
         HandleAlerts(time[i], SIGNAL_EXIT, close[i]);
      }
   }

   return rates_total;
}

//+------------------------------------------------------------------+
//| Custom indicator deinitialization function                      |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   //--- Delete info panel objects (from reference structure)
   if(ObjectFind(0, LabelBox) != -1) ObjectDelete(0, LabelBox);
   if(ObjectFind(0, Label1) != -1) ObjectDelete(0, Label1);
   if(ObjectFind(0, Label2) != -1) ObjectDelete(0, Label2);
   if(ObjectFind(0, Label3) != -1) ObjectDelete(0, Label3);
   if(ObjectFind(0, Label4) != -1) ObjectDelete(0, Label4);
   
   //--- Release indicator handles
   IndicatorRelease(atrHandle);
   IndicatorRelease(rsiHandle);
   
   Print("AMPD Crash 1000 Indicator deinitialized");
}

//+------------------------------------------------------------------+
//| Get ATR value                                                    |
//+------------------------------------------------------------------+
double GetATR(int index)
{
   static double atr[1];
   if(CopyBuffer(atrHandle, 0, index, 1, atr) != 1)
      return 0.0;
   return atr[0];
}

//+------------------------------------------------------------------+
//| Get RSI value                                                    |
//+------------------------------------------------------------------+
double GetRSI(int index)
{
   static double rsi[1];
   if(CopyBuffer(rsiHandle, 0, index, 1, rsi) != 1)
      return 50.0;
   return rsi[0];
}

//+------------------------------------------------------------------+
//| Analyze Crash 1000 signals                                     |
//+------------------------------------------------------------------+
ENUM_SIGNAL AnalyzeCrashSignals(int index, 
                               const datetime &time[],
                               const double &open[],
                               const double &high[],
                               const double &low[],
                               const double &close[],
                               double atr,
                               double rsi)
{
   // Calculate candle properties
   double candleSize = MathAbs(close[index] - open[index]);
   double candleRange = high[index] - low[index];
   
   if(candleRange <= 0 || atr <= 0) return SIGNAL_NONE;
   
   // Crash 1000 Analysis (Spike Hunter Detection)
   if(IsCrashSignal(index, candleSize, atr, rsi, close, open, high, low))
      return SIGNAL_CRASH_SELL;
   
   // Exit Signal Analysis
   if(IsExitSignal(index, close, open))
      return SIGNAL_EXIT;
   
   return SIGNAL_NONE;
}

//+------------------------------------------------------------------+
//| Check for Crash 1000 signal (Spike Hunter Sell)               |
//+------------------------------------------------------------------+
bool IsCrashSignal(int index, double candleSize, double atr, double rsi,
                   const double &close[], const double &open[],
                   const double &high[], const double &low[])
{
   // Must be bearish candle
   if(close[index] >= open[index]) return false;

   // Check RSI overbought condition
   if(rsi < InpRSILevel) return false;

   // Check candle size against ATR threshold
   if(candleSize < atr * InpCandleSize) return false;

   // Check volatility expansion (crash detection)
   if(candleSize < atr * InpVolatilityThreshold) return false;

   // Crash pattern validation
   double bodySize = MathAbs(close[index] - open[index]);
   double shadowSize = high[index] - MathMax(close[index], open[index]);

   // Crash candles typically have large bodies with small upper shadows
   if(shadowSize > bodySize * 0.3) return false;

   return true;
}

//+------------------------------------------------------------------+
//| Check for exit signals                                          |
//+------------------------------------------------------------------+
bool IsExitSignal(int index, const double &close[], const double &open[])
{
   if(index + 2 >= ArraySize(close)) return false;

   // Check for momentum reversal (bullish reversal after bearish trend)
   bool bullishReversal = (close[index] > open[index]) &&
                         (close[index + 1] < open[index + 1]) &&
                         (close[index + 2] < open[index + 2]);

   return bullishReversal;
}

//+------------------------------------------------------------------+
//| Create information panel                                         |
//+------------------------------------------------------------------+
void CreateInfoPanel()
{
   int xStart = 7;
   int yStart = 30;
   int yIncrement = 16;
   int ySize = 80;

   // Create background box (from reference structure)
   ObjectCreate(chartID, LabelBox, OBJ_RECTANGLE_LABEL, 0, 0, 0);
   ObjectSetInteger(chartID, LabelBox, OBJPROP_XDISTANCE, 5);
   ObjectSetInteger(chartID, LabelBox, OBJPROP_YDISTANCE, 25);
   ObjectSetInteger(chartID, LabelBox, OBJPROP_XSIZE, 200);
   ObjectSetInteger(chartID, LabelBox, OBJPROP_YSIZE, ySize);
   ObjectSetInteger(chartID, LabelBox, OBJPROP_BGCOLOR, clrDarkRed);
   ObjectSetInteger(chartID, LabelBox, OBJPROP_BORDER_TYPE, BORDER_FLAT);
   ObjectSetInteger(chartID, LabelBox, OBJPROP_BORDER_COLOR, clrRed);

   // Create labels
   ObjectCreate(chartID, Label1, OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(chartID, Label1, OBJPROP_XDISTANCE, xStart);
   ObjectSetInteger(chartID, Label1, OBJPROP_YDISTANCE, yStart);
   ObjectSetString(chartID, Label1, OBJPROP_FONT, "Arial Bold");
   ObjectSetInteger(chartID, Label1, OBJPROP_FONTSIZE, 9);
   ObjectSetInteger(chartID, Label1, OBJPROP_COLOR, clrWhite);

   ObjectCreate(chartID, Label2, OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(chartID, Label2, OBJPROP_XDISTANCE, xStart);
   ObjectSetInteger(chartID, Label2, OBJPROP_YDISTANCE, yStart + yIncrement);
   ObjectSetString(chartID, Label2, OBJPROP_FONT, "Arial Bold");
   ObjectSetInteger(chartID, Label2, OBJPROP_FONTSIZE, 9);
   ObjectSetInteger(chartID, Label2, OBJPROP_COLOR, clrYellow);

   ObjectCreate(chartID, Label3, OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(chartID, Label3, OBJPROP_XDISTANCE, xStart);
   ObjectSetInteger(chartID, Label3, OBJPROP_YDISTANCE, yStart + yIncrement * 2);
   ObjectSetString(chartID, Label3, OBJPROP_FONT, "Arial Bold");
   ObjectSetInteger(chartID, Label3, OBJPROP_FONTSIZE, 9);
   ObjectSetInteger(chartID, Label3, OBJPROP_COLOR, clrLime);

   ObjectCreate(chartID, Label4, OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(chartID, Label4, OBJPROP_XDISTANCE, xStart);
   ObjectSetInteger(chartID, Label4, OBJPROP_YDISTANCE, yStart + yIncrement * 3);
   ObjectSetString(chartID, Label4, OBJPROP_FONT, "Arial Bold");
   ObjectSetInteger(chartID, Label4, OBJPROP_FONTSIZE, 9);
   ObjectSetInteger(chartID, Label4, OBJPROP_COLOR, clrCyan);
}

//+------------------------------------------------------------------+
//| Update information panel                                         |
//+------------------------------------------------------------------+
void UpdateInfoPanel()
{
   if(!InpShowInfoPanel) return;

   // Update spread info (from reference structure)
   if(SymbolInfoTick(_Symbol, tick))
      ObjectSetString(chartID, Label1, OBJPROP_TEXT, label1 +
                      DoubleToString((tick.ask - tick.bid) * doubleToPip, 1) + " pips");

   // Update RSI level
   double currentRSI = GetRSI(0);
   ObjectSetString(chartID, Label2, OBJPROP_TEXT, label2 + DoubleToString(currentRSI, 1));

   // Update signal status
   string signalStatus = "Waiting";
   if(CrashSellBuffer[0] != 0.0) signalStatus = "SELL ACTIVE";
   else if(ExitBuffer[0] != 0.0) signalStatus = "EXIT ACTIVE";
   ObjectSetString(chartID, Label3, OBJPROP_TEXT, label3 + signalStatus);

   // Update time
   ObjectSetString(chartID, Label4, OBJPROP_TEXT, label4 + TimeToString(TimeCurrent(), TIME_SECONDS));
}

//+------------------------------------------------------------------+
//| Handle alerts for all signal types                              |
//+------------------------------------------------------------------+
void HandleAlerts(const datetime &time, const ENUM_SIGNAL signal_type, double price)
{
   static datetime timePrev;
   static ENUM_SIGNAL typePrev;

   if(timePrev != time || typePrev != signal_type)
   {
      timePrev = time;
      typePrev = signal_type;

      string alertMessage = "";

      switch(signal_type)
      {
         case SIGNAL_CRASH_SELL:
            alertMessage = StringFormat("💥 CRASH 1000 SELL SIGNAL @ %s - Price: %s - RSI: %.1f",
                          _Symbol, DoubleToString(price, _Digits), GetRSI(0));
            break;

         case SIGNAL_EXIT:
            alertMessage = StringFormat("🔄 CRASH 1000 EXIT SIGNAL @ %s - Price: %s",
                          _Symbol, DoubleToString(price, _Digits));
            break;
      }

      // Show alerts based on settings
      if(InpShowAlerts && alertMessage != "")
      {
         Alert(alertMessage);
         Print(alertMessage);
      }

      if(InpSoundAlerts)
         PlaySound("alert.wav");

      if(InpEmailAlerts)
         SendMail("AMPD Crash 1000 Alert", alertMessage);

      if(InpPushAlerts)
         SendNotification(alertMessage);
   }
}

//+------------------------------------------------------------------+
//| Get current signal for EA integration                           |
//+------------------------------------------------------------------+
ENUM_SIGNAL GetCurrentSignal()
{
   if(CrashSellBuffer[0] != 0.0) return SIGNAL_CRASH_SELL;
   if(ExitBuffer[0] != 0.0) return SIGNAL_EXIT;
   return SIGNAL_NONE;
}

//+------------------------------------------------------------------+
//| Check if Crash signal is active                                 |
//+------------------------------------------------------------------+
bool IsCrashSignalActive()
{
   return (CrashSellBuffer[0] != 0.0);
}

//+------------------------------------------------------------------+
//| Check if Exit signal is active                                  |
//+------------------------------------------------------------------+
bool IsExitSignalActive()
{
   return (ExitBuffer[0] != 0.0);
}
