===============================================================================
                    AMPD JUMP 75 & CRASH 1000 PARAMETER SETTINGS
                         OPTIMIZATION & CONFIGURATION GUIDE
===============================================================================

RECOMMENDED PARAMETER SETS FOR DIFFERENT MARKET CONDITIONS:

===============================================================================
                              CONSERVATIVE SETTINGS
===============================================================================

INDICATOR PARAMETERS:
---------------------
Jump 75 Settings:
- Jump Volatility Multiplier: 3.0 (higher threshold for fewer false signals)
- Jump Momentum Period: 7 (longer confirmation period)
- Jump Minimum Candle Size: 2.0 (larger minimum size)
- Jump Confirmation Bars: 3 (more confirmation required)

Crash 1000 Settings:
- Crash Volatility Multiplier: 3.5 (higher threshold)
- Crash RSI Period: 14 (standard RSI period)
- Crash RSI Level: 70.0 (stricter overbought level)
- Crash Minimum Candle Size: 2.5 (larger minimum size)

EXPERT ADVISOR PARAMETERS:
--------------------------
Trading Settings:
- Lot Size: 0.01
- Maximum Risk Per Trade: 1.5%

Jump 75 Settings:
- Take Profit: 40 points (conservative target)
- Stop Loss: 20 points (tight stop)
- Max Concurrent Trades: 2 (reduced exposure)

Crash 1000 Settings:
- Take Profit: 60 points (conservative target)
- Stop Loss: 30 points (tight stop)
- Max Concurrent Trades: 1 (single position)

Risk Management:
- Maximum Daily Trades: 5 (limited daily exposure)
- Signal Cooldown: 600 seconds (10 minutes)

===============================================================================
                              AGGRESSIVE SETTINGS
===============================================================================

INDICATOR PARAMETERS:
---------------------
Jump 75 Settings:
- Jump Volatility Multiplier: 2.0 (lower threshold for more signals)
- Jump Momentum Period: 3 (shorter confirmation)
- Jump Minimum Candle Size: 1.2 (smaller minimum size)
- Jump Confirmation Bars: 1 (minimal confirmation)

Crash 1000 Settings:
- Crash Volatility Multiplier: 2.5 (lower threshold)
- Crash RSI Period: 10 (faster RSI)
- Crash RSI Level: 60.0 (earlier overbought detection)
- Crash Minimum Candle Size: 1.5 (smaller minimum size)

EXPERT ADVISOR PARAMETERS:
--------------------------
Trading Settings:
- Lot Size: 0.02
- Maximum Risk Per Trade: 3.0%

Jump 75 Settings:
- Take Profit: 70 points (higher target)
- Stop Loss: 35 points (wider stop)
- Max Concurrent Trades: 5 (higher exposure)

Crash 1000 Settings:
- Take Profit: 100 points (higher target)
- Stop Loss: 50 points (wider stop)
- Max Concurrent Trades: 3 (multiple positions)

Risk Management:
- Maximum Daily Trades: 20 (higher daily limit)
- Signal Cooldown: 180 seconds (3 minutes)

===============================================================================
                              BALANCED SETTINGS (RECOMMENDED)
===============================================================================

INDICATOR PARAMETERS:
---------------------
Jump 75 Settings:
- Jump Volatility Multiplier: 2.5 (balanced sensitivity)
- Jump Momentum Period: 5 (moderate confirmation)
- Jump Minimum Candle Size: 1.5 (reasonable minimum)
- Jump Confirmation Bars: 2 (adequate confirmation)

Crash 1000 Settings:
- Crash Volatility Multiplier: 3.0 (balanced threshold)
- Crash RSI Period: 14 (standard period)
- Crash RSI Level: 65.0 (moderate overbought level)
- Crash Minimum Candle Size: 2.0 (reasonable minimum)

EXPERT ADVISOR PARAMETERS:
--------------------------
Trading Settings:
- Lot Size: 0.01
- Maximum Risk Per Trade: 2.0%

Jump 75 Settings:
- Take Profit: 50 points (balanced target)
- Stop Loss: 25 points (reasonable stop)
- Max Concurrent Trades: 3 (moderate exposure)

Crash 1000 Settings:
- Take Profit: 75 points (balanced target)
- Stop Loss: 35 points (reasonable stop)
- Max Concurrent Trades: 2 (controlled exposure)

Risk Management:
- Maximum Daily Trades: 10 (reasonable daily limit)
- Signal Cooldown: 300 seconds (5 minutes)

===============================================================================
                              SYMBOL-SPECIFIC ADJUSTMENTS
===============================================================================

FOR JUMP 75 INDEX:
-------------------
If experiencing too many false signals:
- Increase Jump Volatility Multiplier to 3.0+
- Increase Jump Confirmation Bars to 3
- Increase Signal Cooldown to 600 seconds

If missing too many signals:
- Decrease Jump Volatility Multiplier to 2.0
- Decrease Jump Confirmation Bars to 1
- Decrease Signal Cooldown to 180 seconds

FOR CRASH 1000 INDEX:
---------------------
If experiencing too many false signals:
- Increase Crash RSI Level to 70+
- Increase Crash Volatility Multiplier to 3.5+
- Increase Crash Minimum Candle Size to 2.5+

If missing too many signals:
- Decrease Crash RSI Level to 60
- Decrease Crash Volatility Multiplier to 2.5
- Decrease Crash Minimum Candle Size to 1.5

===============================================================================
                              TIME-BASED ADJUSTMENTS
===============================================================================

HIGH VOLATILITY PERIODS (London/NY Overlap):
---------------------------------------------
- Increase all multipliers by 0.5
- Increase Take Profit targets by 20%
- Increase Stop Loss levels by 15%
- Reduce Max Concurrent Trades by 1

LOW VOLATILITY PERIODS (Asian Session):
---------------------------------------
- Decrease all multipliers by 0.3
- Decrease Take Profit targets by 15%
- Decrease Stop Loss levels by 10%
- Increase Signal Cooldown by 50%

NEWS RELEASE PERIODS:
---------------------
- Temporarily disable trading 30 minutes before/after major news
- Increase all volatility thresholds by 50%
- Reduce position sizes by 50%

===============================================================================
                              OPTIMIZATION PROCESS
===============================================================================

STEP 1: BASELINE TESTING
-------------------------
1. Start with Balanced Settings
2. Run in demo mode for 1 week
3. Record performance metrics:
   - Total trades
   - Win rate
   - Average profit/loss
   - Maximum drawdown

STEP 2: PARAMETER ADJUSTMENT
----------------------------
1. Identify main issues:
   - Too many false signals → Increase thresholds
   - Missing good signals → Decrease thresholds
   - Large losses → Tighten stops or reduce lot size
   - Small profits → Increase targets or lot size

2. Adjust one parameter group at a time
3. Test for another week
4. Compare results

STEP 3: FINE-TUNING
-------------------
1. Make small incremental changes (±0.1 to ±0.5)
2. Focus on the most impactful parameters:
   - Volatility Multipliers
   - Take Profit/Stop Loss levels
   - RSI levels for Crash signals

STEP 4: VALIDATION
------------------
1. Test optimized settings for 2-4 weeks
2. Ensure consistent performance
3. Monitor for over-optimization
4. Validate on different market conditions

===============================================================================
                              PERFORMANCE MONITORING
===============================================================================

KEY METRICS TO TRACK:
----------------------
- Win Rate (target: 60%+)
- Risk/Reward Ratio (target: 1:1.5+)
- Maximum Drawdown (keep under 10%)
- Daily/Weekly profit consistency
- Signal frequency (3-5 Jump signals/hour, 1-2 Crash signals/hour)

WARNING SIGNS:
--------------
- Win rate below 50%
- Consecutive losses > 5
- Drawdown exceeding 15%
- No signals for extended periods
- Excessive false signals

ADJUSTMENT TRIGGERS:
--------------------
- Performance degradation for 3+ days
- Market condition changes
- Broker spread/execution changes
- System resource issues

===============================================================================
                              BROKER-SPECIFIC NOTES
===============================================================================

DERIV/BINARY.COM:
-----------------
- Symbol names: "Volatility 75 Index", "Crash 1000 Index"
- Typical spreads: 0.5-2.0 points
- Execution: Usually good
- Recommended settings: Balanced to Conservative

EXNESS:
-------
- Symbol names: "JMP75", "CR1000"
- Typical spreads: 1.0-3.0 points
- Execution: Variable
- Recommended settings: Conservative (wider stops)

OTHER BROKERS:
--------------
- Verify symbol availability and names
- Test execution quality in demo
- Adjust spreads in calculations
- Monitor slippage and requotes

===============================================================================
                              TROUBLESHOOTING GUIDE
===============================================================================

COMMON ISSUES AND SOLUTIONS:

No Signals Generated:
- Check symbol names match broker exactly
- Verify indicator is properly loaded
- Check if market is open
- Reduce volatility thresholds temporarily

Too Many False Signals:
- Increase volatility multipliers
- Increase confirmation requirements
- Add longer signal cooldown
- Check for news events

Poor Performance:
- Review parameter settings
- Check broker execution quality
- Verify spread costs
- Consider market condition changes

System Errors:
- Recompile indicator and EA
- Check MT5 version compatibility
- Verify file permissions
- Restart MT5 platform

===============================================================================
