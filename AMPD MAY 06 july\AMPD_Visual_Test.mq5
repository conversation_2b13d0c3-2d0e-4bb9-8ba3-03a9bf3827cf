//+------------------------------------------------------------------+
//| AMPD Visual Test Indicator                                       |
//| Simple test to verify arrow and dot display                     |
//+------------------------------------------------------------------+
#property copyright "ARISE-MAY-DYNASTY"
#property link      ""
#property version   "1.00"
#property indicator_chart_window
#property indicator_buffers 4
#property indicator_plots   4

//--- Plot settings
#property indicator_label1  "GOLD_BUY_TEST"
#property indicator_type1   DRAW_ARROW
#property indicator_color1  clrGold
#property indicator_style1  STYLE_SOLID
#property indicator_width1  3

#property indicator_label2  "LIME_SELL_TEST"
#property indicator_type2   DRAW_ARROW
#property indicator_color2  clrLime
#property indicator_style2  STYLE_SOLID
#property indicator_width2  3

#property indicator_label3  "WHITE_ENTRY_TEST"
#property indicator_type3   DRAW_ARROW
#property indicator_color3  clrWhite
#property indicator_style3  STYLE_SOLID
#property indicator_width3  2

#property indicator_label4  "YELLOW_EXIT_TEST"
#property indicator_type4   DRAW_ARROW
#property indicator_color4  clrYellow
#property indicator_style4  STYLE_SOLID
#property indicator_width4  2

//--- Indicator buffers
double GoldBuyBuffer[];
double LimeSellBuffer[];
double WhiteEntryBuffer[];
double YellowExitBuffer[];

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
   //--- Set buffers
   SetIndexBuffer(0, GoldBuyBuffer, INDICATOR_DATA);
   SetIndexBuffer(1, LimeSellBuffer, INDICATOR_DATA);
   SetIndexBuffer(2, WhiteEntryBuffer, INDICATOR_DATA);
   SetIndexBuffer(3, YellowExitBuffer, INDICATOR_DATA);
   
   //--- Set arrow codes
   PlotIndexSetInteger(0, PLOT_ARROW, 233);  // Up arrow
   PlotIndexSetInteger(1, PLOT_ARROW, 234);  // Down arrow
   PlotIndexSetInteger(2, PLOT_ARROW, 159);  // Dot
   PlotIndexSetInteger(3, PLOT_ARROW, 159);  // Dot
   
   //--- Set empty values
   PlotIndexSetDouble(0, PLOT_EMPTY_VALUE, 0);
   PlotIndexSetDouble(1, PLOT_EMPTY_VALUE, 0);
   PlotIndexSetDouble(2, PLOT_EMPTY_VALUE, 0);
   PlotIndexSetDouble(3, PLOT_EMPTY_VALUE, 0);
   
   //--- Set arrays as series
   ArraySetAsSeries(GoldBuyBuffer, true);
   ArraySetAsSeries(LimeSellBuffer, true);
   ArraySetAsSeries(WhiteEntryBuffer, true);
   ArraySetAsSeries(YellowExitBuffer, true);
   
   Print("AMPD Visual Test Indicator loaded - Check for GOLD/LIME arrows and WHITE/YELLOW dots");
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
   ArraySetAsSeries(high, true);
   ArraySetAsSeries(low, true);
   ArraySetAsSeries(close, true);
   
   int start = prev_calculated > 0 ? prev_calculated - 1 : 0;
   
   for(int i = start; i < rates_total - 1; i++)
   {
      int index = rates_total - 1 - i;
      
      //--- Clear all buffers first
      GoldBuyBuffer[index] = 0;
      LimeSellBuffer[index] = 0;
      WhiteEntryBuffer[index] = 0;
      YellowExitBuffer[index] = 0;
      
      //--- Simple test: show arrows every 10 bars
      if(i % 20 == 0)  // GOLD BUY arrow every 20 bars
      {
         GoldBuyBuffer[index] = low[index] - 10 * _Point;
         WhiteEntryBuffer[index] = low[index] - 5 * _Point;
      }
      else if(i % 20 == 10)  // LIME SELL arrow every 20 bars (offset)
      {
         LimeSellBuffer[index] = high[index] + 10 * _Point;
         WhiteEntryBuffer[index] = high[index] + 5 * _Point;
      }
      
      //--- Show exit dots occasionally
      if(i % 15 == 7)
      {
         YellowExitBuffer[index] = close[index];
      }
   }
   
   return(rates_total);
}
//+------------------------------------------------------------------+
