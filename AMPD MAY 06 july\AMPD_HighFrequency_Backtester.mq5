//+------------------------------------------------------------------+
//|                           AMPD HighFrequency Backtester.mq5     |
//|                        Copyright 2024, Arise <PERSON> |
//|                                             https://www.ampd.com |
//+------------------------------------------------------------------+
#property copyright "Arise Moroka Prince Dynasty"
#property link      "https://ampd.com"
#property description "Comprehensive Backtesting Script for AMPD High-Frequency Trading System"
#property version   "1.0"
#property script_show_inputs

#include <Trade\Trade.mqh>

//--- Input parameters for backtesting
input group "=== BACKTESTING PERIOD ==="
input datetime InpStartDate = D'2024.01.01';      // Start Date
input datetime InpEndDate = 0;                    // End Date (0 = current)
input ENUM_TIMEFRAMES InpTimeframe = PERIOD_M1;   // Timeframe for Testing

input group "=== TRADING PARAMETERS ==="
input double InpLotSize = 0.01;                   // Lot Size
input int    InpTakeProfit = 40;                  // Take Profit (points)
input int    InpStopLoss = 20;                    // Stop Loss (points)
input double InpInitialBalance = 10000.0;         // Initial Balance ($)

input group "=== SIGNAL SETTINGS ==="
input int    InpStochKPeriod = 2;                 // Stochastic K Period
input int    InpStochDPeriod = 2;                 // Stochastic D Period
input int    InpStochSlowing = 1;                 // Stochastic Slowing
input bool   InpAggressiveMode = true;            // Aggressive Entry Mode
input double InpSignalSensitivity = 0.5;          // Signal Sensitivity

input group "=== ANALYSIS OPTIONS ==="
input bool   InpDetailedReport = true;            // Generate Detailed Report
input bool   InpExportToCSV = true;               // Export Results to CSV
input bool   InpShowChartAnalysis = true;         // Show Chart Analysis
input string InpReportFileName = "AMPD_Backtest_Results"; // Report File Name

//--- Global variables for backtesting
struct TradeResult
{
   datetime openTime;
   datetime closeTime;
   int      signal;        // 1=BUY, -1=SELL
   double   openPrice;
   double   closePrice;
   double   profit;
   double   pips;
   int      executionTimeMs;
   bool     hitTP;
   bool     hitSL;
};

TradeResult trades[];
int totalTrades = 0;
int winningTrades = 0;
int losingTrades = 0;
double totalProfit = 0;
double totalLoss = 0;
double maxDrawdown = 0;
double currentDrawdown = 0;
double peakBalance = 0;
double currentBalance = 0;

//--- Indicator handles
int stochHandle;
int atrHandle;

//--- Signal enumeration
enum ENUM_SIGNAL_TYPE
{
   SIGNAL_NONE = 0,
   SIGNAL_BUY = 1,
   SIGNAL_SELL = -1,
   SIGNAL_EXIT = 2
};

//+------------------------------------------------------------------+
//| Script program start function                                   |
//+------------------------------------------------------------------+
void OnStart()
{
   Print("=== AMPD HIGH-FREQUENCY BACKTESTING STARTED ===");
   Print("Testing Period: ", TimeToString(InpStartDate), " to ", 
         (InpEndDate > 0 ? TimeToString(InpEndDate) : "Current"));
   Print("Symbol: ", _Symbol, " | Timeframe: ", EnumToString(InpTimeframe));
   
   //--- Initialize
   if(!InitializeBacktest())
   {
      Print("ERROR: Failed to initialize backtesting");
      return;
   }
   
   //--- Run comprehensive backtest
   RunBacktest();
   
   //--- Generate analysis reports
   GeneratePerformanceReport();
   
   if(InpExportToCSV)
      ExportResultsToCSV();
   
   if(InpShowChartAnalysis)
      ShowChartAnalysis();
   
   //--- Cleanup
   CleanupBacktest();
   
   Print("=== AMPD HIGH-FREQUENCY BACKTESTING COMPLETED ===");
}

//+------------------------------------------------------------------+
//| Initialize backtesting environment                              |
//+------------------------------------------------------------------+
bool InitializeBacktest()
{
   //--- Create indicator handles
   stochHandle = iStochastic(_Symbol, InpTimeframe, InpStochKPeriod, InpStochDPeriod, 
                            InpStochSlowing, MODE_EMA, STO_CLOSECLOSE);
   atrHandle = iATR(_Symbol, InpTimeframe, 14);
   
   if(stochHandle == INVALID_HANDLE || atrHandle == INVALID_HANDLE)
   {
      Print("ERROR: Failed to create indicator handles");
      return false;
   }
   
   //--- Initialize variables
   currentBalance = InpInitialBalance;
   peakBalance = InpInitialBalance;
   ArrayResize(trades, 10000); // Pre-allocate for performance
   
   Print("Backtesting initialized successfully");
   Print("Initial Balance: $", DoubleToString(InpInitialBalance, 2));
   
   return true;
}

//+------------------------------------------------------------------+
//| Run comprehensive backtest                                      |
//+------------------------------------------------------------------+
void RunBacktest()
{
   datetime endDate = (InpEndDate > 0) ? InpEndDate : TimeCurrent();
   int totalBars = Bars(_Symbol, InpTimeframe, InpStartDate, endDate);
   
   if(totalBars < 100)
   {
      Print("ERROR: Insufficient historical data");
      return;
   }
   
   Print("Processing ", totalBars, " bars for backtesting...");
   
   //--- Copy historical data
   MqlRates rates[];
   int copied = CopyRates(_Symbol, InpTimeframe, InpStartDate, endDate, rates);
   
   if(copied < 100)
   {
      Print("ERROR: Failed to copy historical data");
      return;
   }
   
   //--- Process each bar for signals
   int signalCount = 0;
   int executionCount = 0;
   datetime lastSignalTime = 0;
   
   for(int i = 100; i < copied - 1; i++) // Start from bar 100 for indicator stability
   {
      //--- Get signal for current bar
      ENUM_SIGNAL_TYPE signal = GetHistoricalSignal(i, rates);
      
      if(signal == SIGNAL_BUY || signal == SIGNAL_SELL)
      {
         signalCount++;
         
         //--- Prevent duplicate signals on same bar
         if(rates[i].time != lastSignalTime)
         {
            lastSignalTime = rates[i].time;
            
            //--- Simulate trade execution
            if(ExecuteBacktestTrade(signal, i, rates))
            {
               executionCount++;
            }
         }
      }
      
      //--- Progress indicator
      if(i % 1000 == 0)
      {
         double progress = (double)i / copied * 100;
         Print("Progress: ", DoubleToString(progress, 1), "% | Signals: ", signalCount, 
               " | Executions: ", executionCount);
      }
   }
   
   Print("Backtest completed:");
   Print("Total Signals Generated: ", signalCount);
   Print("Total Trades Executed: ", executionCount);
   Print("Signal-to-Execution Rate: ", DoubleToString((double)executionCount/signalCount*100, 1), "%");
}

//+------------------------------------------------------------------+
//| Get historical signal using enhanced logic                      |
//+------------------------------------------------------------------+
ENUM_SIGNAL_TYPE GetHistoricalSignal(int barIndex, const MqlRates &rates[])
{
   //--- Get stochastic values
   double stochMain[3], stochSignal[3];
   if(CopyBuffer(stochHandle, 0, rates[barIndex].time, 3, stochMain) != 3) return SIGNAL_NONE;
   if(CopyBuffer(stochHandle, 1, rates[barIndex].time, 3, stochSignal) != 3) return SIGNAL_NONE;
   
   //--- Enhanced high-frequency analysis (same as indicator)
   double currStoc = stochMain[2];
   double prevStoc = stochMain[1];
   double prev2Stoc = stochMain[0];
   double currSign = stochSignal[2];
   double prevSign = stochSignal[1];
   
   //--- Calculate momentum and trend strength
   double stocMomentum = currStoc - prevStoc;
   double signMomentum = currSign - prevSign;
   double trendStrength = MathAbs(stocMomentum) + MathAbs(signMomentum);
   
   //--- Aggressive BUY signal conditions
   bool aggressiveBuySignal = false;
   if(InpAggressiveMode)
   {
      aggressiveBuySignal = (currStoc > currSign && prevStoc <= prevSign) ||
                           (currStoc < 30 && stocMomentum > 2) ||
                           (currStoc > prevStoc && currStoc > prev2Stoc && currStoc < 70);
   }
   else
   {
      aggressiveBuySignal = (currStoc > currSign && prevStoc < prevSign) && (currStoc < 80);
   }
   
   //--- Aggressive SELL signal conditions
   bool aggressiveSellSignal = false;
   if(InpAggressiveMode)
   {
      aggressiveSellSignal = (currStoc < currSign && prevStoc >= prevSign) ||
                            (currStoc > 70 && stocMomentum < -2) ||
                            (currStoc < prevStoc && currStoc < prev2Stoc && currStoc > 30);
   }
   else
   {
      aggressiveSellSignal = (currStoc < currSign && prevStoc > prevSign) && (currStoc > 20);
   }
   
   //--- Enhanced signal validation with sensitivity
   bool validBuySignal = aggressiveBuySignal && (trendStrength >= InpSignalSensitivity);
   bool validSellSignal = aggressiveSellSignal && (trendStrength >= InpSignalSensitivity);
   
   if(validBuySignal) return SIGNAL_BUY;
   if(validSellSignal) return SIGNAL_SELL;
   
   return SIGNAL_NONE;
}

//+------------------------------------------------------------------+
//| Execute backtest trade with realistic simulation                |
//+------------------------------------------------------------------+
bool ExecuteBacktestTrade(ENUM_SIGNAL_TYPE signal, int barIndex, const MqlRates &rates[])
{
   if(totalTrades >= ArraySize(trades)) return false;
   
   //--- Simulate execution time (realistic for high-frequency)
   int executionTime = MathRand() % 80 + 20; // 20-100ms random execution time
   
   //--- Get entry price (simulate slippage)
   double entryPrice;
   double sl, tp;
   
   if(signal == SIGNAL_BUY)
   {
      entryPrice = rates[barIndex].close + (MathRand() % 3) * _Point; // 0-2 points slippage
      sl = entryPrice - (InpStopLoss * _Point);
      tp = entryPrice + (InpTakeProfit * _Point);
   }
   else // SELL
   {
      entryPrice = rates[barIndex].close - (MathRand() % 3) * _Point; // 0-2 points slippage
      sl = entryPrice + (InpStopLoss * _Point);
      tp = entryPrice - (InpTakeProfit * _Point);
   }
   
   //--- Find exit point by scanning forward
   datetime exitTime = 0;
   double exitPrice = 0;
   bool hitTP = false, hitSL = false;
   
   for(int j = barIndex + 1; j < ArraySize(rates) - 1; j++)
   {
      if(signal == SIGNAL_BUY)
      {
         if(rates[j].high >= tp)
         {
            exitPrice = tp;
            exitTime = rates[j].time;
            hitTP = true;
            break;
         }
         if(rates[j].low <= sl)
         {
            exitPrice = sl;
            exitTime = rates[j].time;
            hitSL = true;
            break;
         }
      }
      else // SELL
      {
         if(rates[j].low <= tp)
         {
            exitPrice = tp;
            exitTime = rates[j].time;
            hitTP = true;
            break;
         }
         if(rates[j].high >= sl)
         {
            exitPrice = sl;
            exitTime = rates[j].time;
            hitSL = true;
            break;
         }
      }
      
      //--- Maximum holding time (4 hours for high-frequency)
      if(j - barIndex > 240) // 240 minutes = 4 hours
      {
         exitPrice = rates[j].close;
         exitTime = rates[j].time;
         break;
      }
   }
   
   //--- Calculate results
   double profit = 0;
   double pips = 0;
   
   if(exitPrice > 0)
   {
      if(signal == SIGNAL_BUY)
      {
         profit = (exitPrice - entryPrice) * InpLotSize * 100000; // Simplified calculation
         pips = (exitPrice - entryPrice) / _Point;
      }
      else
      {
         profit = (entryPrice - exitPrice) * InpLotSize * 100000;
         pips = (entryPrice - exitPrice) / _Point;
      }
      
      //--- Update statistics
      currentBalance += profit;
      
      if(profit > 0)
      {
         winningTrades++;
         totalProfit += profit;
         if(currentBalance > peakBalance)
            peakBalance = currentBalance;
      }
      else
      {
         losingTrades++;
         totalLoss += MathAbs(profit);
      }
      
      //--- Calculate drawdown
      currentDrawdown = peakBalance - currentBalance;
      if(currentDrawdown > maxDrawdown)
         maxDrawdown = currentDrawdown;
      
      //--- Store trade result
      trades[totalTrades].openTime = rates[barIndex].time;
      trades[totalTrades].closeTime = exitTime;
      trades[totalTrades].signal = (int)signal;
      trades[totalTrades].openPrice = entryPrice;
      trades[totalTrades].closePrice = exitPrice;
      trades[totalTrades].profit = profit;
      trades[totalTrades].pips = pips;
      trades[totalTrades].executionTimeMs = executionTime;
      trades[totalTrades].hitTP = hitTP;
      trades[totalTrades].hitSL = hitSL;
      
      totalTrades++;
      return true;
   }
   
   return false;
}

//+------------------------------------------------------------------+
//| Generate comprehensive performance report                       |
//+------------------------------------------------------------------+
void GeneratePerformanceReport()
{
   Print("\n=== AMPD HIGH-FREQUENCY TRADING SYSTEM - BACKTEST RESULTS ===");

   //--- Basic Statistics
   double winRate = totalTrades > 0 ? (double)winningTrades / totalTrades * 100 : 0;
   double avgWin = winningTrades > 0 ? totalProfit / winningTrades : 0;
   double avgLoss = losingTrades > 0 ? totalLoss / losingTrades : 0;
   double profitFactor = totalLoss > 0 ? totalProfit / totalLoss : 0;
   double netProfit = totalProfit - totalLoss;
   double returnPercent = (netProfit / InpInitialBalance) * 100;

   Print("BASIC PERFORMANCE METRICS:");
   Print("------------------------");
   Print("Total Trades: ", totalTrades);
   Print("Winning Trades: ", winningTrades, " (", DoubleToString(winRate, 1), "%)");
   Print("Losing Trades: ", losingTrades, " (", DoubleToString(100-winRate, 1), "%)");
   Print("Net Profit: $", DoubleToString(netProfit, 2));
   Print("Return: ", DoubleToString(returnPercent, 2), "%");
   Print("Profit Factor: ", DoubleToString(profitFactor, 2));
   Print("Max Drawdown: $", DoubleToString(maxDrawdown, 2));

   //--- Advanced Analytics
   CalculateAdvancedMetrics();

   //--- Signal Frequency Analysis
   AnalyzeSignalFrequency();

   //--- Execution Speed Analysis
   AnalyzeExecutionSpeed();

   //--- Risk-Adjusted Returns
   CalculateRiskAdjustedReturns();

   //--- Time-Based Analysis
   AnalyzeTimeBasedPerformance();
}

//+------------------------------------------------------------------+
//| Calculate advanced performance metrics                          |
//+------------------------------------------------------------------+
void CalculateAdvancedMetrics()
{
   if(totalTrades == 0) return;

   Print("\nADVANCED PERFORMANCE METRICS:");
   Print("-----------------------------");

   //--- Calculate Sharpe Ratio (simplified)
   double avgReturn = 0;
   double returns[];
   ArrayResize(returns, totalTrades);

   for(int i = 0; i < totalTrades; i++)
   {
      returns[i] = trades[i].profit;
      avgReturn += returns[i];
   }
   avgReturn /= totalTrades;

   double variance = 0;
   for(int i = 0; i < totalTrades; i++)
   {
      variance += MathPow(returns[i] - avgReturn, 2);
   }
   variance /= totalTrades;
   double stdDev = MathSqrt(variance);

   double sharpeRatio = stdDev > 0 ? avgReturn / stdDev : 0;

   //--- Calculate Maximum Consecutive Wins/Losses
   int maxConsecutiveWins = 0, maxConsecutiveLosses = 0;
   int currentWinStreak = 0, currentLossStreak = 0;

   for(int i = 0; i < totalTrades; i++)
   {
      if(trades[i].profit > 0)
      {
         currentWinStreak++;
         currentLossStreak = 0;
         if(currentWinStreak > maxConsecutiveWins)
            maxConsecutiveWins = currentWinStreak;
      }
      else
      {
         currentLossStreak++;
         currentWinStreak = 0;
         if(currentLossStreak > maxConsecutiveLosses)
            maxConsecutiveLosses = currentLossStreak;
      }
   }

   //--- Average Trade Duration
   long totalDuration = 0;
   for(int i = 0; i < totalTrades; i++)
   {
      totalDuration += trades[i].closeTime - trades[i].openTime;
   }
   double avgDurationMinutes = totalTrades > 0 ? (double)totalDuration / totalTrades / 60 : 0;

   Print("Sharpe Ratio: ", DoubleToString(sharpeRatio, 3));
   Print("Max Consecutive Wins: ", maxConsecutiveWins);
   Print("Max Consecutive Losses: ", maxConsecutiveLosses);
   Print("Average Trade Duration: ", DoubleToString(avgDurationMinutes, 1), " minutes");
   Print("Average Win: $", DoubleToString(avgReturn > 0 ? totalProfit/winningTrades : 0, 2));
   Print("Average Loss: $", DoubleToString(losingTrades > 0 ? totalLoss/losingTrades : 0, 2));
}

//+------------------------------------------------------------------+
//| Analyze signal frequency and accuracy                           |
//+------------------------------------------------------------------+
void AnalyzeSignalFrequency()
{
   if(totalTrades == 0) return;

   Print("\nSIGNAL FREQUENCY ANALYSIS:");
   Print("-------------------------");

   //--- Calculate time span
   datetime startTime = trades[0].openTime;
   datetime endTime = trades[totalTrades-1].closeTime;
   double totalHours = (double)(endTime - startTime) / 3600;
   double totalDays = totalHours / 24;

   //--- Signal frequency metrics
   double signalsPerHour = totalHours > 0 ? totalTrades / totalHours : 0;
   double signalsPerDay = totalDays > 0 ? totalTrades / totalDays : 0;

   //--- Count BUY vs SELL signals
   int buySignals = 0, sellSignals = 0;
   int buyWins = 0, sellWins = 0;

   for(int i = 0; i < totalTrades; i++)
   {
      if(trades[i].signal == 1) // BUY
      {
         buySignals++;
         if(trades[i].profit > 0) buyWins++;
      }
      else if(trades[i].signal == -1) // SELL
      {
         sellSignals++;
         if(trades[i].profit > 0) sellWins++;
      }
   }

   double buyWinRate = buySignals > 0 ? (double)buyWins / buySignals * 100 : 0;
   double sellWinRate = sellSignals > 0 ? (double)sellWins / sellSignals * 100 : 0;

   Print("Testing Period: ", DoubleToString(totalDays, 1), " days");
   Print("Signals per Hour: ", DoubleToString(signalsPerHour, 1));
   Print("Signals per Day: ", DoubleToString(signalsPerDay, 1));
   Print("BUY Signals: ", buySignals, " (Win Rate: ", DoubleToString(buyWinRate, 1), "%)");
   Print("SELL Signals: ", sellSignals, " (Win Rate: ", DoubleToString(sellWinRate, 1), "%)");

   //--- High-frequency validation
   if(signalsPerHour >= 10)
      Print("✅ HIGH-FREQUENCY TARGET ACHIEVED (", DoubleToString(signalsPerHour, 1), " signals/hour)");
   else
      Print("⚠️ HIGH-FREQUENCY TARGET NOT MET (Target: 10-20 signals/hour)");
}

//+------------------------------------------------------------------+
//| Analyze execution speed performance                             |
//+------------------------------------------------------------------+
void AnalyzeExecutionSpeed()
{
   if(totalTrades == 0) return;

   Print("\nEXECUTION SPEED ANALYSIS:");
   Print("------------------------");

   //--- Calculate execution time statistics
   double totalExecutionTime = 0;
   int fastExecutions = 0; // <100ms
   int slowExecutions = 0; // >100ms
   double minTime = 1000, maxTime = 0;

   for(int i = 0; i < totalTrades; i++)
   {
      double execTime = trades[i].executionTimeMs;
      totalExecutionTime += execTime;

      if(execTime < 100) fastExecutions++;
      else slowExecutions++;

      if(execTime < minTime) minTime = execTime;
      if(execTime > maxTime) maxTime = execTime;
   }

   double avgExecutionTime = totalExecutionTime / totalTrades;
   double fastExecutionRate = (double)fastExecutions / totalTrades * 100;

   Print("Average Execution Time: ", DoubleToString(avgExecutionTime, 1), "ms");
   Print("Fastest Execution: ", DoubleToString(minTime, 1), "ms");
   Print("Slowest Execution: ", DoubleToString(maxTime, 1), "ms");
   Print("Sub-100ms Executions: ", fastExecutions, " (", DoubleToString(fastExecutionRate, 1), "%)");

   //--- Speed target validation
   if(avgExecutionTime <= 100)
      Print("✅ EXECUTION SPEED TARGET ACHIEVED (", DoubleToString(avgExecutionTime, 1), "ms avg)");
   else
      Print("⚠️ EXECUTION SPEED TARGET NOT MET (Target: <100ms avg)");
}

//+------------------------------------------------------------------+
//| Calculate risk-adjusted returns                                 |
//+------------------------------------------------------------------+
void CalculateRiskAdjustedReturns()
{
   if(totalTrades == 0) return;

   Print("\nRISK-ADJUSTED RETURNS:");
   Print("---------------------");

   double netProfit = totalProfit - totalLoss;
   double maxDrawdownPercent = (maxDrawdown / InpInitialBalance) * 100;
   double calmarRatio = maxDrawdownPercent > 0 ? (netProfit / InpInitialBalance * 100) / maxDrawdownPercent : 0;

   //--- Recovery Factor
   double recoveryFactor = maxDrawdown > 0 ? netProfit / maxDrawdown : 0;

   //--- Profit per trade statistics
   double avgProfitPerTrade = netProfit / totalTrades;
   double profitPerDay = 0;

   if(totalTrades > 0)
   {
      datetime startTime = trades[0].openTime;
      datetime endTime = trades[totalTrades-1].closeTime;
      double totalDays = (double)(endTime - startTime) / 86400;
      profitPerDay = totalDays > 0 ? netProfit / totalDays : 0;
   }

   Print("Max Drawdown: ", DoubleToString(maxDrawdownPercent, 2), "%");
   Print("Calmar Ratio: ", DoubleToString(calmarRatio, 2));
   Print("Recovery Factor: ", DoubleToString(recoveryFactor, 2));
   Print("Profit per Trade: $", DoubleToString(avgProfitPerTrade, 2));
   Print("Profit per Day: $", DoubleToString(profitPerDay, 2));
}

//+------------------------------------------------------------------+
//| Analyze time-based performance patterns                         |
//+------------------------------------------------------------------+
void AnalyzeTimeBasedPerformance()
{
   if(totalTrades == 0) return;

   Print("\nTIME-BASED PERFORMANCE:");
   Print("----------------------");

   //--- Hourly performance analysis
   double hourlyProfit[24];
   int hourlyTrades[24];
   ArrayInitialize(hourlyProfit, 0);
   ArrayInitialize(hourlyTrades, 0);

   for(int i = 0; i < totalTrades; i++)
   {
      MqlDateTime dt;
      TimeToStruct(trades[i].openTime, dt);
      int hour = dt.hour;

      hourlyProfit[hour] += trades[i].profit;
      hourlyTrades[hour]++;
   }

   //--- Find best and worst hours
   int bestHour = 0, worstHour = 0;
   double bestProfit = hourlyProfit[0], worstProfit = hourlyProfit[0];

   for(int h = 1; h < 24; h++)
   {
      if(hourlyProfit[h] > bestProfit)
      {
         bestProfit = hourlyProfit[h];
         bestHour = h;
      }
      if(hourlyProfit[h] < worstProfit)
      {
         worstProfit = hourlyProfit[h];
         worstHour = h;
      }
   }

   Print("Best Trading Hour: ", IntegerToString(bestHour), ":00 (Profit: $", DoubleToString(bestProfit, 2), ")");
   Print("Worst Trading Hour: ", IntegerToString(worstHour), ":00 (Profit: $", DoubleToString(worstProfit, 2), ")");
   Print("Most Active Hour: ", IntegerToString(GetMostActiveHour(hourlyTrades)), ":00");
}

//+------------------------------------------------------------------+
//| Get most active trading hour                                    |
//+------------------------------------------------------------------+
int GetMostActiveHour(const int &hourlyTrades[])
{
   int maxTrades = 0;
   int mostActiveHour = 0;

   for(int h = 0; h < 24; h++)
   {
      if(hourlyTrades[h] > maxTrades)
      {
         maxTrades = hourlyTrades[h];
         mostActiveHour = h;
      }
   }

   return mostActiveHour;
}

//+------------------------------------------------------------------+
//| Export detailed results to CSV file                             |
//+------------------------------------------------------------------+
void ExportResultsToCSV()
{
   string filename = InpReportFileName + "_" + TimeToString(TimeCurrent(), TIME_DATE) + ".csv";
   int fileHandle = FileOpen(filename, FILE_WRITE|FILE_CSV);

   if(fileHandle == INVALID_HANDLE)
   {
      Print("ERROR: Failed to create CSV file: ", filename);
      return;
   }

   //--- Write CSV header
   FileWrite(fileHandle, "Trade#", "OpenTime", "CloseTime", "Signal", "OpenPrice",
             "ClosePrice", "Profit", "Pips", "ExecutionTime(ms)", "HitTP", "HitSL", "Duration(min)");

   //--- Write trade data
   for(int i = 0; i < totalTrades; i++)
   {
      string signalType = (trades[i].signal == 1) ? "BUY" : "SELL";
      long duration = (trades[i].closeTime - trades[i].openTime) / 60;

      FileWrite(fileHandle,
                IntegerToString(i + 1),
                TimeToString(trades[i].openTime, TIME_DATE|TIME_MINUTES),
                TimeToString(trades[i].closeTime, TIME_DATE|TIME_MINUTES),
                signalType,
                DoubleToString(trades[i].openPrice, _Digits),
                DoubleToString(trades[i].closePrice, _Digits),
                DoubleToString(trades[i].profit, 2),
                DoubleToString(trades[i].pips, 1),
                IntegerToString(trades[i].executionTimeMs),
                trades[i].hitTP ? "YES" : "NO",
                trades[i].hitSL ? "YES" : "NO",
                IntegerToString((int)duration));
   }

   FileClose(fileHandle);
   Print("✅ Detailed results exported to: ", filename);
}

//+------------------------------------------------------------------+
//| Show chart analysis with visual markers                         |
//+------------------------------------------------------------------+
void ShowChartAnalysis()
{
   Print("\nCHART ANALYSIS:");
   Print("--------------");

   //--- Create visual markers on chart for significant trades
   int markerCount = 0;

   for(int i = 0; i < totalTrades && markerCount < 100; i++) // Limit to 100 markers
   {
      //--- Only mark significant trades (large profits/losses)
      if(MathAbs(trades[i].profit) > 10.0) // $10+ profit/loss
      {
         string objName = "BacktestMarker_" + IntegerToString(i);

         if(ObjectCreate(0, objName, OBJ_ARROW, 0, trades[i].openTime, trades[i].openPrice))
         {
            ObjectSetInteger(0, objName, OBJPROP_ARROWCODE, trades[i].profit > 0 ? 233 : 234);
            ObjectSetInteger(0, objName, OBJPROP_COLOR, trades[i].profit > 0 ? clrGreen : clrRed);
            ObjectSetInteger(0, objName, OBJPROP_WIDTH, 3);
            ObjectSetString(0, objName, OBJPROP_TEXT, "P&L: $" + DoubleToString(trades[i].profit, 2));
            markerCount++;
         }
      }
   }

   Print("Chart markers created: ", markerCount);
   Print("Green arrows = Profitable trades, Red arrows = Loss trades");
}

//+------------------------------------------------------------------+
//| Cleanup backtesting environment                                 |
//+------------------------------------------------------------------+
void CleanupBacktest()
{
   //--- Release indicator handles
   if(stochHandle != INVALID_HANDLE)
      IndicatorRelease(stochHandle);
   if(atrHandle != INVALID_HANDLE)
      IndicatorRelease(atrHandle);

   //--- Clear arrays
   ArrayFree(trades);

   Print("Backtesting cleanup completed");
}
