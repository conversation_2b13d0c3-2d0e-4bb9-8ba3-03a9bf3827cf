//+------------------------------------------------------------------+
//|                                      AMPD Jump75 Indicator.mq5  |
//|                        Copyright 2024, Arise <PERSON>rok<PERSON> |
//|                                             https://www.ampd.com |
//+------------------------------------------------------------------+
#property copyright "Arise Moroka Prince Dynasty"
#property link      "https://ampd.com"
#property description "Professional Jump 75 Index Trading Indicator"
#property version   "1.0"

#property indicator_chart_window
#property indicator_buffers 4
#property indicator_plots   4

//--- Plot definitions using proven working structure for bidirectional trading
#property indicator_label1  "Jump75_BUY"
#property indicator_type1   DRAW_ARROW
#property indicator_color1  clrLime
#property indicator_width1  3

#property indicator_label2  "Jump75_SELL"
#property indicator_type2   DRAW_ARROW
#property indicator_color2  clrRed
#property indicator_width2  3

#property indicator_label3  "EXIT_Signal"
#property indicator_type3   DRAW_ARROW
#property indicator_color3  clrGold
#property indicator_width3  2

#property indicator_label4  "Trend_Line"
#property indicator_type4   DRAW_LINE
#property indicator_color4  clrDodgerBlue
#property indicator_width4  1

//--- Signal Types for bidirectional trading
enum ENUM_SIGNAL
{
   SIGNAL_NONE,
   SIGNAL_JUMP_BUY,
   SIGNAL_JUMP_SELL,
   SIGNAL_EXIT
};

//--- Input parameters
input group "=== JUMP 75 BUY SETTINGS ==="
input double InpBuyVolatilityThreshold = 2.0;      // BUY Volatility Threshold (ATR Multiplier)
input int    InpBuyMomentumPeriod = 3;             // BUY Momentum Period
input double InpBuyCandleSize = 1.5;               // BUY Minimum Candle Size (ATR Multiplier)
input int    InpBuyConfirmationBars = 1;           // BUY Confirmation Bars

input group "=== JUMP 75 SELL SETTINGS ==="
input double InpSellVolatilityThreshold = 2.0;     // SELL Volatility Threshold (ATR Multiplier)
input int    InpSellMomentumPeriod = 3;            // SELL Momentum Period
input double InpSellCandleSize = 1.5;              // SELL Minimum Candle Size (ATR Multiplier)
input int    InpSellConfirmationBars = 1;          // SELL Confirmation Bars

input group "=== GENERAL SETTINGS ==="
input int    InpATRPeriod = 14;                    // ATR Period
input bool   InpEnableBuySignals = true;           // Enable BUY Signals
input bool   InpEnableSellSignals = true;          // Enable SELL Signals

input group "=== ALERT SETTINGS ==="
input bool   InpShowAlerts = true;                 // Show Alerts
input bool   InpSoundAlerts = true;                // Sound Alerts
input bool   InpEmailAlerts = false;               // Email Alerts
input bool   InpPushAlerts = false;                // Push Notifications

input group "=== DISPLAY SETTINGS ==="
input bool   InpShowInfoPanel = true;              // Show Info Panel
input bool   InpShowTrendLine = true;              // Show Trend Reference Line

//--- Global variables for bidirectional trading
double JumpBuyBuffer[];
double JumpSellBuffer[];
double ExitBuffer[];
double TrendBuffer[];

int atrHandle;

long chartID = ChartID();
#define LabelBox "JUMP_BOX"
#define Label1 "JUMP_L1"
#define Label2 "JUMP_L2"
#define Label3 "JUMP_L3"
#define Label4 "JUMP_L4"
#define Label5 "JUMP_L5"

string label1 = "JUMP 75: ",
       label2 = "BUY MOMENTUM: ",
       label3 = "SELL MOMENTUM: ",
       label4 = "SIGNALS: ",
       label5 = "STATUS: ";

int doubleToPip;
double pipToDouble;
MqlTick tick;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
   IndicatorSetString(INDICATOR_SHORTNAME, "AMPD Jump 75 Pro");
   
   //--- Set points & digits (from reference structure)
   IndicatorSetInteger(INDICATOR_DIGITS, _Digits);   
   if(_Digits == 2 || _Digits == 3)  
      doubleToPip = 100;
   else                          
      doubleToPip = 10000;
   
   if(_Digits == 2 || _Digits == 4) 
      pipToDouble = _Point;
   else                       
      pipToDouble = _Point * 10;

   //--- Create indicator handles
   atrHandle = iATR(_Symbol, PERIOD_CURRENT, InpATRPeriod);
   
   if(atrHandle == INVALID_HANDLE)
   {
      Print("Error creating ATR handle for Jump 75");
      return INIT_FAILED;
   }

   //--- Create info panel
   if(InpShowInfoPanel)
      CreateInfoPanel();

   //--- Indicator buffers mapping for bidirectional trading (using proven structure)
   SetIndexBuffer(0, JumpBuyBuffer, INDICATOR_DATA);
   ArraySetAsSeries(JumpBuyBuffer, true);
   PlotIndexSetString(0, PLOT_LABEL, "Jump75_BUY");
   PlotIndexSetInteger(0, PLOT_ARROW, 233);  // Up arrow
   PlotIndexSetDouble(0, PLOT_EMPTY_VALUE, 0);

   SetIndexBuffer(1, JumpSellBuffer, INDICATOR_DATA);
   ArraySetAsSeries(JumpSellBuffer, true);
   PlotIndexSetString(1, PLOT_LABEL, "Jump75_SELL");
   PlotIndexSetInteger(1, PLOT_ARROW, 234);  // Down arrow
   PlotIndexSetDouble(1, PLOT_EMPTY_VALUE, 0);

   SetIndexBuffer(2, ExitBuffer, INDICATOR_DATA);
   ArraySetAsSeries(ExitBuffer, true);
   PlotIndexSetString(2, PLOT_LABEL, "EXIT_Signal");
   PlotIndexSetInteger(2, PLOT_ARROW, 159);  // Circle
   PlotIndexSetDouble(2, PLOT_EMPTY_VALUE, 0);

   SetIndexBuffer(3, TrendBuffer, INDICATOR_DATA);
   ArraySetAsSeries(TrendBuffer, true);
   PlotIndexSetString(3, PLOT_LABEL, "Trend_Line");
   PlotIndexSetDouble(3, PLOT_EMPTY_VALUE, 0);

   Print("AMPD Jump 75 Bidirectional Indicator initialized successfully");
   Print("BUY Signals: ", InpEnableBuySignals ? "Enabled" : "Disabled");
   Print("SELL Signals: ", InpEnableSellSignals ? "Enabled" : "Disabled");
   Print("BUY Momentum Period: ", InpBuyMomentumPeriod, " | SELL Momentum Period: ", InpSellMomentumPeriod);
   Print("BUY Volatility Threshold: ", InpBuyVolatilityThreshold, "x ATR | SELL Volatility Threshold: ", InpSellVolatilityThreshold, "x ATR");
   
   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
   if(rates_total < 100)  
      return 0;

   //--- Set arrays as series (from reference structure)
   ArraySetAsSeries(time, true);
   ArraySetAsSeries(open, true);
   ArraySetAsSeries(high, true);
   ArraySetAsSeries(low, true);
   ArraySetAsSeries(close, true);

   //--- Update info panel
   if(InpShowInfoPanel)
      UpdateInfoPanel();

   int limit;
   //--- Calculate limit (from reference structure)
   limit = rates_total - prev_calculated;
   if(prev_calculated == 0)
   {
      limit = (int)ChartGetInteger(chartID, CHART_VISIBLE_BARS) + 100;
      PlotIndexSetInteger(0, PLOT_DRAW_BEGIN, rates_total - limit);
      PlotIndexSetInteger(1, PLOT_DRAW_BEGIN, rates_total - limit);
      PlotIndexSetInteger(2, PLOT_DRAW_BEGIN, rates_total - limit);
      PlotIndexSetInteger(3, PLOT_DRAW_BEGIN, rates_total - limit);
   }

   //--- Main calculation loop for bidirectional trading (from reference structure)
   for(int i = limit; i >= 0 && !IsStopped(); i--)
   {
      // Initialize buffers
      JumpBuyBuffer[i] = 0;
      JumpSellBuffer[i] = 0;
      ExitBuffer[i] = 0;
      TrendBuffer[i] = 0;

      // Get indicator values
      double atr = GetATR(i);

      if(atr <= 0) continue;

      // Set trend reference line
      if(InpShowTrendLine)
         TrendBuffer[i] = close[i];

      // Analyze Jump 75 bidirectional signals
      ENUM_SIGNAL signal = AnalyzeJumpSignals(i, time, open, high, low, close, atr);

      // Plot signals based on analysis
      if(signal == SIGNAL_JUMP_BUY && InpEnableBuySignals)
      {
         JumpBuyBuffer[i] = low[i] - atr;
         HandleAlerts(time[i], SIGNAL_JUMP_BUY, close[i]);
      }
      else if(signal == SIGNAL_JUMP_SELL && InpEnableSellSignals)
      {
         JumpSellBuffer[i] = high[i] + atr;
         HandleAlerts(time[i], SIGNAL_JUMP_SELL, close[i]);
      }
      else if(signal == SIGNAL_EXIT)
      {
         ExitBuffer[i] = close[i];
         HandleAlerts(time[i], SIGNAL_EXIT, close[i]);
      }
   }

   return rates_total;
}

//+------------------------------------------------------------------+
//| Custom indicator deinitialization function                      |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   //--- Delete info panel objects (from reference structure)
   if(ObjectFind(0, LabelBox) != -1) ObjectDelete(0, LabelBox);
   if(ObjectFind(0, Label1) != -1) ObjectDelete(0, Label1);
   if(ObjectFind(0, Label2) != -1) ObjectDelete(0, Label2);
   if(ObjectFind(0, Label3) != -1) ObjectDelete(0, Label3);
   if(ObjectFind(0, Label4) != -1) ObjectDelete(0, Label4);
   if(ObjectFind(0, Label5) != -1) ObjectDelete(0, Label5);

   //--- Release indicator handles
   IndicatorRelease(atrHandle);

   Print("AMPD Jump 75 Bidirectional Indicator deinitialized");
}

//+------------------------------------------------------------------+
//| Get ATR value                                                    |
//+------------------------------------------------------------------+
double GetATR(int index)
{
   static double atr[1];
   if(CopyBuffer(atrHandle, 0, index, 1, atr) != 1)
      return 0.0;
   return atr[0];
}

//+------------------------------------------------------------------+
//| Analyze Jump 75 bidirectional signals                          |
//+------------------------------------------------------------------+
ENUM_SIGNAL AnalyzeJumpSignals(int index,
                              const datetime &time[],
                              const double &open[],
                              const double &high[],
                              const double &low[],
                              const double &close[],
                              double atr)
{
   // Calculate candle properties
   double candleSize = MathAbs(close[index] - open[index]);
   double candleRange = high[index] - low[index];

   if(candleRange <= 0 || atr <= 0) return SIGNAL_NONE;

   // Jump 75 BUY Analysis (Bullish Momentum Burst Detection)
   if(InpEnableBuySignals && IsJumpBuySignal(index, candleSize, atr, close, open, high, low))
      return SIGNAL_JUMP_BUY;

   // Jump 75 SELL Analysis (Bearish Momentum Burst Detection)
   if(InpEnableSellSignals && IsJumpSellSignal(index, candleSize, atr, close, open, high, low))
      return SIGNAL_JUMP_SELL;

   // Exit Signal Analysis
   if(IsExitSignal(index, close, open))
      return SIGNAL_EXIT;

   return SIGNAL_NONE;
}

//+------------------------------------------------------------------+
//| Check for Jump 75 BUY signal (Bullish Momentum Burst Entry)    |
//+------------------------------------------------------------------+
bool IsJumpBuySignal(int index, double candleSize, double atr,
                     const double &close[], const double &open[],
                     const double &high[], const double &low[])
{
   // Must be bullish candle
   if(close[index] <= open[index]) return false;

   // Check candle size against ATR threshold
   if(candleSize < atr * InpBuyCandleSize) return false;

   // Check volatility expansion
   if(candleSize < atr * InpBuyVolatilityThreshold) return false;

   // Momentum confirmation
   bool momentumConfirmed = true;
   for(int i = 1; i <= InpBuyConfirmationBars && (index + i) < ArraySize(close); i++)
   {
      if(close[index] <= close[index + i])
      {
         momentumConfirmed = false;
         break;
      }
   }

   // Additional momentum check - compare with recent average
   if(momentumConfirmed && InpBuyMomentumPeriod > 0)
   {
      double avgCandle = 0;
      int count = 0;
      for(int i = 1; i <= InpBuyMomentumPeriod && (index + i) < ArraySize(close); i++)
      {
         avgCandle += MathAbs(close[index + i] - open[index + i]);
         count++;
      }

      if(count > 0)
      {
         avgCandle /= count;
         if(candleSize < avgCandle * InpBuyVolatilityThreshold)
            momentumConfirmed = false;
      }
   }

   return momentumConfirmed;
}

//+------------------------------------------------------------------+
//| Check for Jump 75 SELL signal (Bearish Momentum Burst Entry)   |
//+------------------------------------------------------------------+
bool IsJumpSellSignal(int index, double candleSize, double atr,
                      const double &close[], const double &open[],
                      const double &high[], const double &low[])
{
   // Must be bearish candle
   if(close[index] >= open[index]) return false;

   // Check candle size against ATR threshold
   if(candleSize < atr * InpSellCandleSize) return false;

   // Check volatility expansion
   if(candleSize < atr * InpSellVolatilityThreshold) return false;

   // Momentum confirmation (bearish)
   bool momentumConfirmed = true;
   for(int i = 1; i <= InpSellConfirmationBars && (index + i) < ArraySize(close); i++)
   {
      if(close[index] >= close[index + i])
      {
         momentumConfirmed = false;
         break;
      }
   }

   // Additional momentum check - compare with recent average (bearish)
   if(momentumConfirmed && InpSellMomentumPeriod > 0)
   {
      double avgCandle = 0;
      int count = 0;
      for(int i = 1; i <= InpSellMomentumPeriod && (index + i) < ArraySize(close); i++)
      {
         avgCandle += MathAbs(close[index + i] - open[index + i]);
         count++;
      }

      if(count > 0)
      {
         avgCandle /= count;
         if(candleSize < avgCandle * InpSellVolatilityThreshold)
            momentumConfirmed = false;
      }
   }

   return momentumConfirmed;
}

//+------------------------------------------------------------------+
//| Check for exit signals (bidirectional)                         |
//+------------------------------------------------------------------+
bool IsExitSignal(int index, const double &close[], const double &open[])
{
   if(index + 2 >= ArraySize(close)) return false;

   // Check for momentum reversal (bearish reversal after bullish trend)
   bool bearishReversal = (close[index] < open[index]) &&
                         (close[index + 1] > open[index + 1]) &&
                         (close[index + 2] > open[index + 2]);

   // Check for momentum reversal (bullish reversal after bearish trend)
   bool bullishReversal = (close[index] > open[index]) &&
                         (close[index + 1] < open[index + 1]) &&
                         (close[index + 2] < open[index + 2]);

   return (bearishReversal || bullishReversal);
}

//+------------------------------------------------------------------+
//| Create information panel                                         |
//+------------------------------------------------------------------+
void CreateInfoPanel()
{
   int xStart = 7;
   int yStart = 30;
   int yIncrement = 16;
   int ySize = 100;  // Increased for additional label

   // Create background box (from reference structure)
   ObjectCreate(chartID, LabelBox, OBJ_RECTANGLE_LABEL, 0, 0, 0);
   ObjectSetInteger(chartID, LabelBox, OBJPROP_XDISTANCE, 5);
   ObjectSetInteger(chartID, LabelBox, OBJPROP_YDISTANCE, 25);
   ObjectSetInteger(chartID, LabelBox, OBJPROP_XSIZE, 200);
   ObjectSetInteger(chartID, LabelBox, OBJPROP_YSIZE, ySize);
   ObjectSetInteger(chartID, LabelBox, OBJPROP_BGCOLOR, clrDarkGreen);
   ObjectSetInteger(chartID, LabelBox, OBJPROP_BORDER_TYPE, BORDER_FLAT);
   ObjectSetInteger(chartID, LabelBox, OBJPROP_BORDER_COLOR, clrLime);

   // Create labels
   ObjectCreate(chartID, Label1, OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(chartID, Label1, OBJPROP_XDISTANCE, xStart);
   ObjectSetInteger(chartID, Label1, OBJPROP_YDISTANCE, yStart);
   ObjectSetString(chartID, Label1, OBJPROP_FONT, "Arial Bold");
   ObjectSetInteger(chartID, Label1, OBJPROP_FONTSIZE, 9);
   ObjectSetInteger(chartID, Label1, OBJPROP_COLOR, clrWhite);

   ObjectCreate(chartID, Label2, OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(chartID, Label2, OBJPROP_XDISTANCE, xStart);
   ObjectSetInteger(chartID, Label2, OBJPROP_YDISTANCE, yStart + yIncrement);
   ObjectSetString(chartID, Label2, OBJPROP_FONT, "Arial Bold");
   ObjectSetInteger(chartID, Label2, OBJPROP_FONTSIZE, 9);
   ObjectSetInteger(chartID, Label2, OBJPROP_COLOR, clrYellow);

   ObjectCreate(chartID, Label3, OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(chartID, Label3, OBJPROP_XDISTANCE, xStart);
   ObjectSetInteger(chartID, Label3, OBJPROP_YDISTANCE, yStart + yIncrement * 2);
   ObjectSetString(chartID, Label3, OBJPROP_FONT, "Arial Bold");
   ObjectSetInteger(chartID, Label3, OBJPROP_FONTSIZE, 9);
   ObjectSetInteger(chartID, Label3, OBJPROP_COLOR, clrLime);

   ObjectCreate(chartID, Label4, OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(chartID, Label4, OBJPROP_XDISTANCE, xStart);
   ObjectSetInteger(chartID, Label4, OBJPROP_YDISTANCE, yStart + yIncrement * 3);
   ObjectSetString(chartID, Label4, OBJPROP_FONT, "Arial Bold");
   ObjectSetInteger(chartID, Label4, OBJPROP_FONTSIZE, 9);
   ObjectSetInteger(chartID, Label4, OBJPROP_COLOR, clrLime);

   // Add fifth label for status
   ObjectCreate(chartID, Label5, OBJ_LABEL, 0, 0, 0);
   ObjectSetInteger(chartID, Label5, OBJPROP_XDISTANCE, xStart);
   ObjectSetInteger(chartID, Label5, OBJPROP_YDISTANCE, yStart + yIncrement * 4);
   ObjectSetString(chartID, Label5, OBJPROP_FONT, "Arial Bold");
   ObjectSetInteger(chartID, Label5, OBJPROP_FONTSIZE, 9);
   ObjectSetInteger(chartID, Label5, OBJPROP_COLOR, clrCyan);
}

//+------------------------------------------------------------------+
//| Update information panel for bidirectional trading             |
//+------------------------------------------------------------------+
void UpdateInfoPanel()
{
   if(!InpShowInfoPanel) return;

   // Update spread info (from reference structure)
   if(SymbolInfoTick(_Symbol, tick))
      ObjectSetString(chartID, Label1, OBJPROP_TEXT, label1 +
                      DoubleToString((tick.ask - tick.bid) * doubleToPip, 1) + " pips");

   // Update BUY momentum status
   double currentATR = GetATR(0);
   double currentCandle = MathAbs(iClose(_Symbol, PERIOD_CURRENT, 0) - iOpen(_Symbol, PERIOD_CURRENT, 0));
   double buyMomentum = currentATR > 0 ? (currentCandle / currentATR) : 0;
   ObjectSetString(chartID, Label2, OBJPROP_TEXT, label2 + DoubleToString(buyMomentum, 2));

   // Update SELL momentum status
   double sellMomentum = buyMomentum; // Same calculation but for bearish context
   ObjectSetString(chartID, Label3, OBJPROP_TEXT, label3 + DoubleToString(sellMomentum, 2));

   // Update signal status
   string signalStatus = "Waiting";
   if(JumpBuyBuffer[0] != 0.0) signalStatus = "BUY ACTIVE";
   else if(JumpSellBuffer[0] != 0.0) signalStatus = "SELL ACTIVE";
   else if(ExitBuffer[0] != 0.0) signalStatus = "EXIT ACTIVE";
   ObjectSetString(chartID, Label4, OBJPROP_TEXT, label4 + signalStatus);

   // Update time and status
   string enabledSignals = "";
   if(InpEnableBuySignals && InpEnableSellSignals) enabledSignals = "BUY+SELL";
   else if(InpEnableBuySignals) enabledSignals = "BUY ONLY";
   else if(InpEnableSellSignals) enabledSignals = "SELL ONLY";
   else enabledSignals = "DISABLED";

   ObjectSetString(chartID, Label5, OBJPROP_TEXT, label5 + enabledSignals + " | " + TimeToString(TimeCurrent(), TIME_SECONDS));
}

//+------------------------------------------------------------------+
//| Handle alerts for all signal types                              |
//+------------------------------------------------------------------+
void HandleAlerts(const datetime &time, const ENUM_SIGNAL signal_type, double price)
{
   static datetime timePrev;
   static ENUM_SIGNAL typePrev;

   if(timePrev != time || typePrev != signal_type)
   {
      timePrev = time;
      typePrev = signal_type;

      string alertMessage = "";

      switch(signal_type)
      {
         case SIGNAL_JUMP_BUY:
            alertMessage = StringFormat("🚀 JUMP 75 BUY SIGNAL @ %s - Price: %s - Momentum: %.2f",
                          _Symbol, DoubleToString(price, _Digits),
                          GetATR(0) > 0 ? (MathAbs(iClose(_Symbol, PERIOD_CURRENT, 0) - iOpen(_Symbol, PERIOD_CURRENT, 0)) / GetATR(0)) : 0);
            break;

         case SIGNAL_JUMP_SELL:
            alertMessage = StringFormat("📉 JUMP 75 SELL SIGNAL @ %s - Price: %s - Momentum: %.2f",
                          _Symbol, DoubleToString(price, _Digits),
                          GetATR(0) > 0 ? (MathAbs(iClose(_Symbol, PERIOD_CURRENT, 0) - iOpen(_Symbol, PERIOD_CURRENT, 0)) / GetATR(0)) : 0);
            break;

         case SIGNAL_EXIT:
            alertMessage = StringFormat("🔄 JUMP 75 EXIT SIGNAL @ %s - Price: %s",
                          _Symbol, DoubleToString(price, _Digits));
            break;
      }

      // Show alerts based on settings
      if(InpShowAlerts && alertMessage != "")
      {
         Alert(alertMessage);
         Print(alertMessage);
      }

      if(InpSoundAlerts)
         PlaySound("alert.wav");

      if(InpEmailAlerts)
         SendMail("AMPD Jump 75 Alert", alertMessage);

      if(InpPushAlerts)
         SendNotification(alertMessage);
   }
}

//+------------------------------------------------------------------+
//| Get current signal for EA integration                           |
//+------------------------------------------------------------------+
ENUM_SIGNAL GetCurrentSignal()
{
   if(JumpBuyBuffer[0] != 0.0) return SIGNAL_JUMP_BUY;
   if(JumpSellBuffer[0] != 0.0) return SIGNAL_JUMP_SELL;
   if(ExitBuffer[0] != 0.0) return SIGNAL_EXIT;
   return SIGNAL_NONE;
}

//+------------------------------------------------------------------+
//| Check if Jump BUY signal is active                              |
//+------------------------------------------------------------------+
bool IsJumpBuySignalActive()
{
   return (JumpBuyBuffer[0] != 0.0);
}

//+------------------------------------------------------------------+
//| Check if Jump SELL signal is active                             |
//+------------------------------------------------------------------+
bool IsJumpSellSignalActive()
{
   return (JumpSellBuffer[0] != 0.0);
}

//+------------------------------------------------------------------+
//| Check if any Jump signal is active                              |
//+------------------------------------------------------------------+
bool IsJumpSignalActive()
{
   return (JumpBuyBuffer[0] != 0.0 || JumpSellBuffer[0] != 0.0);
}

//+------------------------------------------------------------------+
//| Check if Exit signal is active                                  |
//+------------------------------------------------------------------+
bool IsExitSignalActive()
{
   return (ExitBuffer[0] != 0.0);
}
